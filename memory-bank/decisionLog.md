# Decision Log

This file records architectural and implementation decisions using a list format.
2025-06-30 03:45:38 - Log of updates made.

*
      
## Decision

*
      
## Rationale 

*

## Implementation Details

*

[2025-06-30 03:47:57] - Decided on the pattern for handling dynamically sized data in `cudastf`.

## Decision

*   `cudastf`'s `logical_data` does not support in-place dynamic resizing. The size is immutable after creation.

## Rationale

*   The size of `logical_data` is determined by a `shape_of` object, which is immutable. This design choice simplifies dependency tracking and avoids concurrency issues in the task-based execution model.

## Implementation Details

*   To handle data that changes size, a new `logical_data` object with the desired dimensions must be created.
*   If the old data needs to be preserved, a `cudastf` task should be used to copy the data from the old `logical_data` object to the new one.
*   The old `logical_data` object should then be destroyed to deallocate its memory.