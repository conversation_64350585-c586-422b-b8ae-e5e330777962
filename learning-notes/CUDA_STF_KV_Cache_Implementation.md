# CUDA STF 中的 KV Cache 动态扩展实现

## 概述

是的，CUDA STF 支持类似 PyTorch 的 KV Cache 动态扩展方式！虽然 STF 没有直接的 `.narrow()` 方法，但它提供了更强大的 `slice` 机制来实现相同的功能。

## 核心机制对比

### PyTorch 方式
```python
# 1. 预分配最大长度
kv_cache = torch.empty(batch_size, num_heads, max_seq_len, head_dim)

# 2. 使用 narrow() 控制有效范围
current_kv = kv_cache.narrow(2, 0, current_seq_len)  # 只使用前 current_seq_len 个位置
```

### CUDA STF 方式
```cpp
// 1. 预分配最大长度
auto kv_cache_full = ctx.logical_data(shape_of<slice<float, 4>>(batch_size, num_heads, max_seq_len, head_dim));

// 2. 使用 make_slice 创建有效范围的视图
auto create_kv_view = [&](size_t current_seq_len) {
    // 创建指向相同内存但限制序列长度的 slice
    return make_slice(kv_cache_ptr, 
                     std::tuple{batch_size, num_heads, current_seq_len, head_dim},
                     std::array<size_t, 4>{num_heads * max_seq_len * head_dim, 
                                          max_seq_len * head_dim, 
                                          head_dim, 
                                          1});
};
```

## 完整的 KV Cache 实现示例

```cpp
#include <cuda/experimental/stf.cuh>
#include <vector>
#include <memory>

using namespace cuda::experimental::stf;

class STFKVCache {
private:
    context& ctx;
    size_t batch_size;
    size_t num_heads;
    size_t max_seq_len;
    size_t head_dim;
    size_t current_seq_len = 0;
    
    // 预分配的完整 KV cache
    logical_data<slice<float, 4>> kv_cache_full;
    
    // 当前有效的 KV cache 视图
    std::shared_ptr<slice<float, 4>> current_kv_view;
    
public:
    STFKVCache(context& ctx, size_t batch, size_t heads, size_t max_len, size_t dim)
        : ctx(ctx), batch_size(batch), num_heads(heads), max_seq_len(max_len), head_dim(dim) {
        
        // 预分配最大长度的 KV cache
        kv_cache_full = ctx.logical_data(
            shape_of<slice<float, 4>>(batch_size, num_heads, max_seq_len, head_dim));
        kv_cache_full.set_symbol("KV_Cache_Full");
    }
    
    // 获取当前有效长度的 KV cache 视图
    auto get_current_kv_view() {
        return ctx.task(kv_cache_full.read())->*[=](auto stream, auto full_cache) {
            // 在 kernel 中创建当前长度的视图
            auto current_view = make_slice(
                full_cache.data_handle(),
                std::tuple{batch_size, num_heads, current_seq_len, head_dim},
                std::array<size_t, 4>{
                    num_heads * max_seq_len * head_dim,  // batch stride
                    max_seq_len * head_dim,              // head stride  
                    head_dim,                            // seq stride
                    1                                    // dim stride
                }
            );
            return current_view;
        };
    }
    
    // 添加新的 token 到 KV cache
    void append_kv(const logical_data<slice<float, 3>>& new_kv) {
        ctx.task(kv_cache_full.rw(), new_kv.read())->*[=](auto stream, auto cache, auto new_data) {
            // 将新的 KV 数据写入到 current_seq_len 位置
            append_kv_kernel<<<dim3(batch_size, num_heads), head_dim, 0, stream>>>(
                cache.data_handle(), 
                new_data.data_handle(),
                current_seq_len,
                max_seq_len,
                head_dim
            );
        };
        current_seq_len++;
    }
    
    // 获取指定范围的 KV cache
    auto get_kv_range(size_t start_pos, size_t length) {
        return ctx.task(kv_cache_full.read())->*[=](auto stream, auto full_cache) {
            auto range_view = make_slice(
                full_cache.data_handle() + start_pos * head_dim,  // 偏移到起始位置
                std::tuple{batch_size, num_heads, length, head_dim},
                std::array<size_t, 4>{
                    num_heads * max_seq_len * head_dim,
                    max_seq_len * head_dim,
                    head_dim,
                    1
                }
            );
            return range_view;
        };
    }
    
    size_t get_current_length() const { return current_seq_len; }
    size_t get_max_length() const { return max_seq_len; }
};

// CUDA kernel 实现
__global__ void append_kv_kernel(
    float* kv_cache,           // [batch, heads, max_seq, dim]
    const float* new_kv,       // [batch, heads, dim]
    size_t current_pos,
    size_t max_seq_len,
    size_t head_dim) {
    
    size_t batch_idx = blockIdx.x;
    size_t head_idx = blockIdx.y;
    size_t dim_idx = threadIdx.x;
    
    if (dim_idx < head_dim) {
        // 计算在完整 cache 中的位置
        size_t cache_offset = batch_idx * (gridDim.y * max_seq_len * head_dim) +
                             head_idx * (max_seq_len * head_dim) +
                             current_pos * head_dim +
                             dim_idx;
        
        // 计算在新数据中的位置
        size_t new_offset = batch_idx * (gridDim.y * head_dim) +
                           head_idx * head_dim +
                           dim_idx;
        
        kv_cache[cache_offset] = new_kv[new_offset];
    }
}
```

## 高级用法示例

### 1. 注意力计算中的 KV Cache 使用

```cpp
void compute_attention_with_kv_cache(
    STFKVCache& kv_cache,
    const logical_data<slice<float, 3>>& query,  // [batch, heads, dim]
    logical_data<slice<float, 3>>& output) {     // [batch, heads, dim]
    
    size_t current_len = kv_cache.get_current_length();
    
    // 获取当前有效的 KV cache
    auto current_kv = kv_cache.get_current_kv_view();
    
    ctx.task(query.read(), current_kv, output.write())
        ->*[=](auto stream, auto q, auto kv, auto out) {
            // 注意力计算 kernel
            attention_kernel<<<dim3(batch_size, num_heads), head_dim, 0, stream>>>(
                q.data_handle(),
                kv.data_handle(),
                out.data_handle(),
                current_len,
                head_dim
            );
        };
}
```

### 2. 批量处理不同序列长度

```cpp
class BatchedKVCache {
private:
    std::vector<STFKVCache> caches;
    std::vector<size_t> sequence_lengths;
    
public:
    BatchedKVCache(context& ctx, const std::vector<size_t>& max_lengths, 
                   size_t num_heads, size_t head_dim) {
        for (size_t i = 0; i < max_lengths.size(); ++i) {
            caches.emplace_back(ctx, 1, num_heads, max_lengths[i], head_dim);
            sequence_lengths.push_back(0);
        }
    }
    
    void append_batch_kv(const std::vector<logical_data<slice<float, 3>>>& new_kvs) {
        for (size_t i = 0; i < caches.size(); ++i) {
            caches[i].append_kv(new_kvs[i]);
            sequence_lengths[i]++;
        }
    }
    
    auto get_batch_attention_inputs() {
        std::vector<decltype(caches[0].get_current_kv_view())> views;
        for (auto& cache : caches) {
            views.push_back(cache.get_current_kv_view());
        }
        return views;
    }
};
```

## 性能优化技巧

### 1. 内存布局优化

```cpp
// 优化的内存布局：将序列维度放在最内层以提高缓存局部性
auto optimized_kv_cache = ctx.logical_data(
    shape_of<slice<float, 4>>(batch_size, num_heads, head_dim, max_seq_len));

// 相应的 stride 调整
auto create_optimized_view = [&](size_t current_seq_len) {
    return make_slice(
        cache_ptr,
        std::tuple{batch_size, num_heads, head_dim, current_seq_len},
        std::array<size_t, 4>{
            num_heads * head_dim * max_seq_len,  // batch stride
            head_dim * max_seq_len,              // head stride
            max_seq_len,                         // dim stride
            1                                    // seq stride (最内层)
        }
    );
};
```

### 2. 异步操作

```cpp
void async_append_kv(STFKVCache& cache, 
                     const logical_data<slice<float, 3>>& new_kv) {
    // STF 自动处理异步执行和依赖关系
    cache.append_kv(new_kv);  // 非阻塞操作
    
    // 可以立即开始下一个操作，STF 会自动排序
    auto current_view = cache.get_current_kv_view();
}
```

## 与 PyTorch 的对比

| 特性 | PyTorch | CUDA STF |
|------|---------|----------|
| 预分配 | `torch.empty()` | `ctx.logical_data(shape_of<>())` |
| 视图创建 | `.narrow()` | `make_slice()` |
| 内存共享 | 自动 | 通过相同指针自动 |
| 异步执行 | 需要显式管理 | STF 自动管理 |
| 依赖跟踪 | 手动 | STF 自动跟踪 |
| 多设备 | 需要显式管理 | STF 透明处理 |

## 总结

CUDA STF 完全支持 PyTorch 风格的 KV Cache 动态扩展：

1. **预分配策略**：使用 `shape_of<>()` 预分配最大长度
2. **视图控制**：使用 `make_slice()` 创建有效范围的视图
3. **零拷贝**：slice 视图共享底层内存，无额外开销
4. **自动管理**：STF 自动处理依赖关系和异步执行

STF 的优势在于更强大的依赖跟踪和自动化的内存管理，使得 KV Cache 的实现更加简洁和高效。
