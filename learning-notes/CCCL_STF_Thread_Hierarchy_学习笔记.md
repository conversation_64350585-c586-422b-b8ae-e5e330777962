# CUDASTF 综合学习笔记

## 目录
1. [异步工作流与执行模式](#异步工作流与执行模式)
2. [Thread Hierarchy 基本概念](#thread-hierarchy-基本概念)
3. [数据类型与归约操作](#数据类型与归约操作)
4. [std::visit 与后端分发机制](#stdvisit-与后端分发机制)
5. [Token 使用详解](#token-使用详解)
6. [性能优化与最佳实践](#性能优化与最佳实践)

---

## 异步工作流与执行模式

### 核心执行函数对比

| **函数** | **执行时机** | **适用后端** | **特点** |
|---------|-------------|-------------|---------|
| **`ctx.task`** | 立即执行lambda，提交异步工作 | stream_ctx, graph_ctx | 通用任务构造，支持复杂逻辑 |
| **`ctx.host_launch`** | 异步执行，等待依赖就绪 | stream_ctx, graph_ctx | 主机端任务，自动同步 |
| **`ctx.cuda_kernel`** | 直接转换为内核启动API | stream_ctx, graph_ctx | 避免图捕获开销，性能更优 |
| **`ctx.parallel_for`** | 自动生成并行内核 | stream_ctx, graph_ctx | 声明式并行，自动优化 |
| **`ctx.launch`** | 用户定义线程层级的内核 | stream_ctx, graph_ctx | 精确控制并行执行 |

### ctx.submit() 和 ctx.finalize() 的作用

```cpp
// ctx.submit() - 提交所有挂起的任务，但不立即等待完成
ctx.submit();
// 此时可以在 CPU 上执行一些与 STF 任务无关的工作
// ... Unrelated CPU-based code might go here...

// ctx.finalize() - 等待所有任务完成
ctx.finalize();
```

**使用场景：**
- **并发执行**：`ctx1.submit(); ctx2.submit(); ctx1.finalize(); ctx2.finalize();`
- **CPU-GPU 重叠**：在GPU工作时执行CPU任务

### CUDA Graph 后端详解

#### stream_ctx vs graph_ctx 的区别

| **特性** | **stream_ctx** | **graph_ctx** |
|---------|---------------|---------------|
| **同步机制** | CUDA streams 和 events | CUDA graphs |
| **任务执行** | 立即执行lambda，提交异步工作 | lambda立即捕获，图延迟执行 |
| **适用场景** | 动态工作负载，调试阶段 | 重复任务模式，性能优化 |
| **图构建** | 无图构建开销 | 有图构建和实例化开销 |
| **执行开销** | 每次任务都有启动开销 | 图启动开销低，适合重复执行 |
| **限制** | 较少限制 | 不支持某些动态操作（如cudaStreamSynchronize） |

#### graph_ctx 的工作机制

```cpp
// graph_ctx 的执行流程
graph_ctx ctx;

// 1. 任务定义阶段 - lambda立即捕获，但不执行
ctx.task(lX.read(), lY.rw())->*[&](cudaStream_t s, auto dX, auto dY) {
    // 这个lambda在task()调用时立即捕获
    // 但实际的内核启动会延迟到图执行时
    axpy<<<16, 128, 0, s>>>(alpha, dX, dY);
};

// 2. 图构建和启动 - 在同步点或finalize时发生
ctx.finalize(); // 或 ctx.submit()
```

**重要特性：**
- **立即捕获，延迟执行**：lambda函数在`ctx.task`调用时立即捕获，但内核启动延迟到图执行
- **自动图管理**：STF自动构建和管理CUDA图
- **图缓存**：相同结构的图会被缓存和重用
- **epoch机制**：支持图的版本管理

### cuda_kernel vs task 的性能差异

| **特性** | **task 构造** | **cuda_kernel 构造** |
|---------|--------------|-------------------|
| **设计目的** | 通用任务，支持复杂逻辑 | 专门用于单个CUDA内核启动 |
| **底层机制** | **依赖图捕获机制** | **直接转换为内核启动API** |
| **与Graph的关系** | 创建复杂的**子图** | 创建简单的**原子节点** |
| **开销** | 有图捕获的**一次性开销** | **避免图捕获开销** |
| **graph_ctx优势** | 适合复杂逻辑，但有捕获开销 | **避免图捕获，性能更优** |

```cpp
// task 方式 - 在graph_ctx中有图捕获开销
ctx.task(lX.read(), lY.rw())->*[&](cudaStream_t s, auto dX, auto dY) {
    axpy<<<16, 128, 0, s>>>(alpha, dX, dY);
};

// cuda_kernel 方式 - 在graph_ctx中避免图捕获开销
ctx.cuda_kernel(lX.read(), lY.rw())->*[&]() {
    return cuda_kernel_desc{axpy, 16, 128, 0, alpha, lX, lY};
};
```

#### 为什么cuda_kernel在graph_ctx中更高效？

1. **图捕获机制**：
   - `task`构造依赖CUDA的图捕获机制来录制操作
   - 图捕获需要分析和录制lambda内部的一系列操作
   - 这个过程有一定的运行时开销

2. **直接API转换**：
   - `cuda_kernel`构造直接转换为CUDA内核启动API
   - 作为原子操作添加到图中，无需复杂的捕获过程
   - 避免了图捕获的分析和录制开销

3. **图节点类型**：
   - `task`：在图中创建一个可能包含多个操作的复杂子图
   - `cuda_kernel`：在图中创建一个简单的内核启动节点

### CUDA Graph 的限制和注意事项

#### graph_ctx 中不支持的操作

```cpp
// ❌ 在graph_ctx中不允许的操作
ctx.task(lX.read())->*[](cudaStream_t s, auto dX) {
    // 这在graph_ctx中是不允许的！
    cudaStreamSynchronize(s);  // 显式同步操作

    // 其他不支持的操作：
    // - cudaDeviceSynchronize()
    // - cudaEventSynchronize()
    // - 动态内存分配（某些情况下）
    // - 主机端的条件分支依赖GPU结果
};

// ✅ 推荐的替代方案：使用host_launch
ctx.host_launch(lX.read())->*[](auto hX) {
    // 这个会自动等待依赖就绪，与所有后端兼容
    assert(hX(0) == expected_value);
};
```

#### 何时选择stream_ctx vs graph_ctx

**选择 stream_ctx 的场景：**
- 开发和调试阶段
- 动态工作负载（任务模式经常变化）
- 需要复杂的主机-设备交互
- 一次性执行的任务

**选择 graph_ctx 的场景：**
- 生产环境的性能优化
- 重复执行相同的任务模式
- 批处理作业
- 需要最小化启动开销的场景

```cpp
// 运行时选择后端的示例
bool use_graph = (iteration_count > 100);  // 重复执行多次时使用图
context ctx = use_graph ? context(graph_ctx()) : context(stream_ctx());
```

### CUDA Graph 高级概念

#### 图的生命周期

```cpp
graph_ctx ctx;

// 1. 图构建阶段
ctx.task(lX.read(), lY.rw())->*[&](cudaStream_t s, auto dX, auto dY) {
    axpy<<<16, 128, 0, s>>>(alpha, dX, dY);
};

// 2. 图实例化和缓存
ctx.submit();  // 触发图构建和实例化

// 3. 图执行
// 后续相同结构的图会重用已实例化的版本
```

#### 图缓存和Epoch机制

**图缓存的工作原理：**
- STF会根据图的结构（节点数、边数）来缓存已实例化的图
- 相同结构的图会被重用，避免重复实例化的开销
- 缓存基于图的拓扑结构，而不是具体的数据值

**Epoch机制：**
- 每个图都有一个epoch（时代）标识
- 当图结构发生变化时，epoch会递增
- 用于管理图的版本和缓存失效

```cpp
// 图结构相同，会重用缓存的实例化图
for (int i = 0; i < 1000; i++) {
    ctx.task(lX.read(), lY.rw())->*[&](cudaStream_t s, auto dX, auto dY) {
        axpy<<<16, 128, 0, s>>>(alpha, dX, dY);  // 相同的图结构
    };
    ctx.submit();  // 第一次会实例化，后续重用
}
```

---

## Thread Hierarchy 基本概念

### 1.1 核心构造
STF (CUDA Sequential Task Framework) 提供了分层的线程组织结构，用于描述并行计算的执行模式。

- **`par(num_threads)`**: 并行组 (Parallel Group) - 线程独立执行，不能同步
- **`con(num_threads)`**: 并发组 (Concurrent Group) - 线程可以通过`sync()`进行同步

### 1.2 构造语法
```cpp
// 基本构造
con(128);           // 128个可同步的线程
par(256);           // 256个独立的线程
con<128>();         // 静态指定大小的128个可同步线程
par();              // 动态大小的并行组

// 嵌套层级
par(128, con<32>()); // 128个独立组，每组32个可同步线程

// 带共享内存
con(256, mem(64));   // 256个线程共享64字节内存

// 硬件范围亲和性
par(hw_scope::device | hw_scope::block, par<128>(hw_scope::thread));
```

### 1.3 硬件范围 (Hardware Scope)
```cpp
enum class hw_scope : unsigned int {
  none   = 0, ///< No hardware scope.
  thread = 1, ///< Thread-level scope.
  block  = 2, ///< Block-level scope.
  device = 4, ///< Device-level scope.
  all    = 7  ///< All levels of hardware scope.
};
```

## 2. Launch Scan 算法详解

### 2.1 算法概述
前缀扫描算法分为四个阶段：
1. **线程级前缀扫描** - 每个线程处理自己的数据段
2. **块级前缀扫描** - 块内线程协作计算块级前缀和
3. **设备级前缀扫描** - 所有块协作计算最终结果
4. **结果合并** - 将各层级的贡献合并到最终结果

### 2.2 Thread Hierarchy 配置
```cpp
constexpr size_t BLOCK_THREADS = 128;  // 每个块128个线程
constexpr size_t NBLOCKS = 8;          // 8个块

// 两层嵌套的concurrent组 + 设备级共享内存
auto spec = con<NBLOCKS>(con<BLOCK_THREADS>(), mem(NBLOCKS * sizeof(double)));
```

### 2.3 关键代码解析
```cpp
// 1. 线程标识
const size_t block_id = th.rank(0);      // 块ID (0到7)
const size_t tid = th.inner().rank();    // 块内线程ID (0到127)

// 2. 共享内存分配
__shared__ double block_partial_sum[th.static_width(1)];  // 块级共享内存
slice<double> dev_partial_sum = th.template storage<double>(0);  // 设备级共享内存

// 3. 数据分区
const box<1> b = th.apply_partition(shape(x), std::tuple<blocked_partition, blocked_partition>());

// 4. 线程本地前缀扫描（通常不执行，因为每个线程只处理一个元素）
for (size_t i = b.get_begin(0) + 1; i < b.get_end(0); i++) {
    x(i) += x(i - 1);
}

// 5. 同步和合并
th.inner().sync();  // 块内同步
th.sync();          // 设备级同步
```

### 2.4 数据分区实际情况
**重要发现**：每个线程实际上只处理一个元素，而不是连续的数据段。

假设1024个元素，8个块，每块128个线程：
- 块0: 处理元素 [0-127]
- 块1: 处理元素 [128-255]
- ...
- 在块0中，线程5只处理元素5

## 3. apply_partition 详解

### 3.1 核心作用
`apply_partition` 根据thread hierarchy的结构，将输入数据空间分配给不同的线程组和线程。

### 3.2 实现机制
```cpp
template <typename shape_t, typename P, typename... sub_partitions>
_CCCL_HOST_DEVICE auto apply_partition(const shape_t& s, const ::std::tuple<P, sub_partitions...>& t) const
{
  // 第一层分区
  auto s0 = P::apply(s, pos4(rank(0)), dim4(size(0)));
  
  // 递归分区
  if constexpr (sizeof...(sub_partitions)) {
    return inner().apply_partition(s0, sans_first);
  }
  else {
    return s0;
  }
}
```

### 3.3 rank() 和 size() 方法
- `th.rank(0)`: 当前线程在第0层（最外层）的排名
- `th.size(0)`: 第0层的总大小
- `th.rank(1)`: 当前线程在第1层的排名
- `th.size(1)`: 第1层的总大小

这些值会根据当前执行的CUDA线程自动计算：
```cpp
// 对于块2中的线程5：
th.rank(0)     // 返回 2 (块ID)
th.rank(1)     // 返回 5 (线程ID)  
th.size(0)     // 返回 8 (总块数)
th.size(1)     // 返回 128 (每块线程数)
```

## 4. 分区策略详解

### 4.1 blocked_partition（块分区）
将数据连续地分成块，每个执行单元处理一个连续的数据段。
```cpp
// 1024个元素，4个执行单元
// 执行单元0: [0-255]
// 执行单元1: [256-511]  
// 执行单元2: [512-767]
// 执行单元3: [768-1023]
```

### 4.2 cyclic_partition（循环分区）
以轮询方式分配数据，每个执行单元处理间隔的元素。
```cpp
// 16个元素，4个执行单元
// 执行单元0: [0, 4, 8, 12]
// 执行单元1: [1, 5, 9, 13]
// 执行单元2: [2, 6, 10, 14]
// 执行单元3: [3, 7, 11, 15]
```

### 4.3 tiled_partition（瓦片分区）
将数据分成固定大小的瓦片，以轮询方式分配瓦片。
```cpp
// 16个元素，瓦片大小4，2个执行单元
// 执行单元0: [0-3, 8-11]    (瓦片0, 瓦片2)
// 执行单元1: [4-7, 12-15]   (瓦片1, 瓦片3)
```

### 4.4 null_partition（空分区）
不进行分区，用于表示没有分区策略。在网格执行中会报错。

### 4.5 分区策略组合
```cpp
// 通过tuple组合不同策略
auto p = ::std::tuple<blocked_partition, cyclic_partition>();
for (auto i : th.apply_partition(shape(A), p)) {
    A(i) = 2 * i + 7;
}
```

## 5. pos4 和 dim4 的设计考虑

### 5.1 当前实现局限
从当前实现来看，`apply_partition` 主要只支持一维分区：
- `blocked_partition` 只在最后一个维度分区
- `thread_hierarchy` 的 rank/size 也是一维的
- 实际使用中都是一维分区

### 5.2 设计原因
虽然当前主要是一维实现，但使用 pos4/dim4 是为了：
1. **支持多维执行网格** - 未来可能需要2D、3D的线程组织
2. **接口统一性** - 所有分区策略使用相同的接口
3. **与CUDA对应** - 直接映射到CUDA的多维grid/block概念
4. **扩展性** - 为未来的复杂并行模式预留空间

### 5.3 当前使用模式
```cpp
pos4(rank(0))     // 等价于 pos4(rank_value, 0, 0, 0)
dim4(size(0))     // 等价于 dim4(size_value, 1, 1, 1)
```

## 6. 关键数据结构

### 6.1 box<1> 类型
`apply_partition` 返回的 `box<1>` 对象描述当前线程应该处理的数据范围：
```cpp
// 对于块0的线程5：
// b.get_begin(0) = 5
// b.get_end(0) = 6
// 表示线程5处理索引为5的元素
```

### 6.2 thread_hierarchy 对象的主要方法
```cpp
th.rank();           // 全局线程排名
th.size();           // 总线程数
th.rank(level);      // 指定层级的排名
th.size(level);      // 指定层级的大小
th.sync();           // 同步最顶层
th.sync(level);      // 同步指定层级
th.inner();          // 获取内层线程层级
th.depth();          // 层级深度
th.template storage<T>(level); // 获取指定层级的共享存储
th.static_width(level);        // 获取静态指定的宽度
```

---

## 数据类型与归约操作

### owning_container_of 与归约支持

**核心概念：**
- **"实现了 `owning_container_of`"** = 为该类型提供了 `owning_container_of<T>` 的模板特化
- **目前只有 `scalar_view<T>` 支持归约操作**
- **`slice<T>` 等其他类型目前不支持归约操作**

### 为什么只有 scalar_view 支持归约？

```mermaid
graph TD
    A[用户代码: lsum.reduce] --> B[STF 检测到归约操作]
    B --> C[STF 创建 redux_vars 缓冲区]
    C --> D[STF 使用 owning_container_of::fill/get_value]
    D --> E[用户 lambda 接收 double& 引用]
    E --> F[用户操作: sum += value]
    F --> G[STF 自动合并所有线程的结果]
    G --> H[STF 使用 owning_container_of::fill 写回最终结果]
```

**原因分析：**
- `slice` 是直接传递给用户的，如 `sX(i)`
- `reduce` 变量是通过STF的"魔法"实现的，需要特定的读写接口
- STF需要为不同数据类型实现特定的读写接口供内部使用

### 归约操作示例

```cpp
// ✅ 正确：使用 scalar_view 进行归约
auto lsum = ctx.logical_data(shape_of<scalar_view<double>>());

// 计算点积: sum(X[i] * Y[i])
ctx.parallel_for(lX.shape(),
                 lX.read(),
                 lY.read(),
                 lsum.reduce(reducer::sum<double>{}))
    ->*[] __device__(size_t i, auto sX, auto sY, double& sum) {
        sum += sX(i) * sY(i);  // 每个线程贡献一个乘积
    };

// ❌ 错误：slice 不支持归约
// auto lsum_slice = ctx.logical_data(shape_of<slice<double>>(1));
```

### 数据传递方式

```cpp
// 如何在logical_data中传入 std::vector对象？
// 方法：数据地址 + size
std::vector<double> h_x(N);
auto lX = ctx.logical_data(&h_x[0], {N});

// extern shared memory 的使用
extern __shared__ T sdata[];  // extern关键词告诉编译器shared的大小在运行时确定
```

---

## std::visit 与后端分发机制

### std::visit 学习笔记

**核心作用：** 以类型安全的方式"访问"并操作 `std::variant` 对象中当前存储的那个具体类型的值。

**与 CUDASTF 的关联：**

1. **`payload` (是 `std::variant`)**:
   - 可以把它想象成一个"盒子" (`payload`)，这个盒子里在任何时候只装着**一种**东西
   - 在 CUDASTF 中，这个"东西"代表了当前的上下文后端实现，要么是与 `stream_ctx`（流上下文）相关的逻辑，要么是与 `graph_ctx`（图上下文）相关的逻辑
   - 具体装的是哪一种，取决于 STF 上下文是如何初始化的

2. **`std::visit(visitor, payload)`**:
   - `std::visit` 的工作就是打开 `payload` 这个盒子，准确识别出里面当前装的是"流上下文逻辑对象"还是"图上下文逻辑对象"
   - 然后，它会调用您提供的 `visitor`（在代码中是一个 lambda 函数 `[&](auto& self) { ... }`），并将盒子里的那个**具体对象**（我们叫它 `self`）传递给这个 lambda

3. **`visitor` (Lambda 函数 `[&](auto& self) { ... }`)**:
   - 这里的 `auto& self` 非常巧妙：
     - 如果 `payload` 里是"流上下文逻辑对象"，那么 `self` 就自动成为对这个"流上下文逻辑对象"的引用
     - 如果 `payload` 里是"图上下文逻辑对象"，那么 `self` 就自动成为对这个"图上下文逻辑对象"的引用
   - Lambda 内部通过 `self.parallel_for(...)` 来调用成员函数。因为 `self` 的类型在此时是确定的（要么是流相关的，要么是图相关的），所以这里会调用到**对应后端版本的 `parallel_for` 实现**

### 实际代码示例

```cpp
// context 类中的 std::variant payload
::std::variant<stream_ctx, graph_ctx> payload;

// 使用 std::visit 进行后端分发
return ::std::visit(
    [&](auto& self) {
        return self.parallel_for(deps...);  // 调用具体后端的实现
    },
    payload);
```

**一句话总结：**
`std::visit` 就像一个智能分拣员，它能准确识别出 `payload`（一个 `std::variant` 类型的上下文"容器"）里当前是哪种后端（`stream_ctx` 或 `graph_ctx`）的"零件"，然后把这个具体的"零件"交给后续的 lambda 函数 (`self`) 去调用该零件专属的方法，从而实现了在统一的顶层接口下，根据实际上下文类型执行不同后端逻辑的目的。这是一种高效且类型安全的**后端分发**机制。

## Token 使用详解

### 什么是Token？

Token（令牌）是CUDA STF中的一种特殊类型的逻辑数据，它的**唯一目的是自动化同步**，而不包含任何实际的数据内容。Token让应用程序可以管理自己的数据缓冲区，同时利用STF的依赖关系管理来确保正确的执行顺序。

### Token的核心特性

1. **轻量级同步**
   - Token基于`void_interface`实现
   - 不进行实际的内存分配或数据传输
   - 运行时开销最小化
   - 专门用于依赖关系管理

2. **三种访问模式**
   - **read()**: 只读访问，多个任务可以并发读取
   - **write()**: 写访问，独占访问，其他任务必须等待
   - **rw()**: 读写访问，独占访问

3. **自动依赖管理**
   - STF自动根据token的访问模式建立任务间的依赖关系
   - 确保数据竞争的避免
   - 简化复杂同步逻辑的编写

### Token的创建方式

```cpp
// 方式1：使用ctx.token()（推荐）
auto token = ctx.token();
token.set_symbol("MyToken");  // 可选：设置符号名用于调试

// 方式2：通过void_interface的shape创建
auto token = ctx.logical_data(shape_of<void_interface>());

// 方式3：通过void_interface实例创建
void_interface sync_obj;
auto token = ctx.logical_data(sync_obj);
```

### 典型使用场景

```cpp
// 1. 简单的任务排序
auto token = ctx.token();

// 任务1：获得写权限，独占执行
ctx.task(token.write())->*[](cudaStream_t stream) {
    // 初始化工作
};

// 任务2：等待任务1完成后执行
ctx.task(token.read())->*[](cudaStream_t stream) {
    // 后续工作
};

// 2. 复杂的多阶段流水线
auto phase1_token = ctx.token().set_symbol("Phase1");
auto phase2_token = ctx.token().set_symbol("Phase2");
auto phase3_token = ctx.token().set_symbol("Phase3");

// 阶段1：并行初始化
ctx.task(phase1_token.write())->*[](cudaStream_t) {
    // 初始化组件A
};

// 阶段2：等待阶段1完成
ctx.task(phase1_token.read(), phase2_token.write())->*[](cudaStream_t) {
    // 基于阶段1的结果进行处理
};

// 阶段3：等待阶段2完成
ctx.task(phase2_token.read(), phase3_token.write())->*[](cudaStream_t) {
    // 最终处理
};
```

### Token vs 普通逻辑数据

| 特性 | Token | 普通逻辑数据 |
|------|-------|-------------|
| 数据内容 | 无实际数据 | 包含实际数据 |
| 内存分配 | 不分配内存 | 需要分配内存 |
| 数据传输 | 无数据传输 | 可能需要Host-Device传输 |
| 主要用途 | 同步控制 | 数据处理 |
| 性能开销 | 极低 | 相对较高 |
| Lambda参数 | 可以省略 | 必须使用 |

---

## 重要概念补充

### inner() 函数的作用

在heat_mgpu中，`inner<1>(lU.shape())` 的使用是为了避免边界错误：

```cpp
// 原始形状：假设是 800x800 的矩阵
// inner<1>(lU.shape()) 返回的形状是 (1,798) x (1,798)
// 即从 [1, 799) x [1, 799) 的范围

ctx.parallel_for(blocked_partition(), all_devs, inner<1>(lU.shape()), lU.read(), lU1.write())
    ->*[=] _CCCL_DEVICE(size_t i, size_t j, auto U, auto U1) {
        // 这里可以安全地访问 U(i-1,j), U(i+1,j), U(i,j-1), U(i,j+1)
        // 因为 i 和 j 的范围保证了不会越界
        U1(i, j) = U(i, j) + c * (
            (U(i - 1, j) - 2 * U(i, j) + U(i + 1, j)) / dx2 +
            (U(i, j - 1) - 2 * U(i, j) + U(i, j + 1)) / dy2
        );
    };
```

**`inner<thickness>` 函数的作用：**
- 将形状的每个维度在两端各缩小 `thickness` 个元素
- 例如：`inner<2>` 应用于 `{10, 100}` 会产生 `{12, 98}` (即 [2,8) x [2,98))
- 这确保了在使用有限差分等需要访问邻居元素的算法时不会越界

### 迭代索引空间与box数据结构

在某些情况下，迭代索引空间不直接对应逻辑数据的形状，此时可以用box数据结构来定义索引空间：

```cpp
// 使用 box 定义自定义的迭代空间
ctx.parallel_for(box(N), lsum.reduce(reducer::sum<size_t>{}))
    ->*[] __device__(size_t i, auto& sum) {
        sum++;  // 计数操作
    };
```

---

## 实际应用模式

### 8.1 前缀扫描算法的数据流
```
输入: [1, 1, 1, 1, 1, 1, 1, 1] (8个元素)
配置: 2个块，每块2个线程

阶段1 - 线程级处理:
块0: 线程0处理元素0, 线程1处理元素1
块1: 线程0处理元素2, 线程1处理元素3

阶段2 - 块级前缀扫描:
块0: [1, 2] -> 块和为2
块1: [1, 2] -> 块和为2

阶段3 - 设备级前缀扫描:
块部分和: [2, 4] (块1的前缀和包含块0的贡献)

阶段4 - 结果合并:
最终结果: [1, 2, 3, 4] (每个元素加上前面块的贡献)
```

### 8.2 常见使用模式
```cpp
// 1. 简单的归约操作
auto spec = par<16>(con<32>());
ctx.launch(spec, where, data.read(), result.rw())->*[](auto th, auto x, auto sum) {
    // 每个线程计算本地和
    double local_sum = 0.0;
    for (size_t i = th.rank(); i < x.size(); i += th.size()) {
        local_sum += x(i);
    }

    // 块内归约
    auto ti = th.inner();
    __shared__ double block_sum[th.static_width(1)];
    block_sum[ti.rank()] = local_sum;

    // 树形归约
    for (size_t s = ti.size() / 2; s > 0; s /= 2) {
        ti.sync();
        if (ti.rank() < s) {
            block_sum[ti.rank()] += block_sum[ti.rank() + s];
        }
    }
};

// 2. 多层分区的AXPY操作
ctx.launch(spec, all_devs, X.read(), Y.rw())->*[=](auto th, auto x, auto y) {
    // 外层块分区，内层循环分区
    auto outer_sh = blocked_partition::apply(shape(x), pos4(th.rank(0)), dim4(th.size(0)));
    auto inner_sh = cyclic_partition::apply(outer_sh, pos4(th.inner().rank()), dim4(th.inner().size()));

    for (auto ind : inner_sh) {
        y(ind) += alpha * x(ind);
    }
};
```

## 9. 性能考虑和最佳实践

### 9.1 分区策略选择指南
| 分区策略 | 适用场景 | 优势 | 劣势 |
|---------|---------|------|------|
| **blocked_partition** | 需要局部性的算法<br>矩阵运算、图像处理 | 内存访问连续<br>缓存友好 | 可能负载不均衡 |
| **cyclic_partition** | 不规则工作负载<br>动态负载均衡 | 负载均衡好<br>避免数据倾斜 | 内存访问不连续 |
| **tiled_partition** | 需要平衡局部性和负载均衡<br>大规模并行计算 | 兼顾局部性和均衡 | 实现复杂度高 |
| **组合策略** | 复杂的多层并行算法<br>前缀扫描、归约 | 灵活性高<br>适应性强 | 理解和调试困难 |

### 9.2 内存管理
```cpp
// 1. 静态共享内存
__shared__ double block_data[th.static_width(1)];

// 2. 动态共享内存（通过STF自动管理）
slice<double> dev_storage = th.template storage<double>(0);

// 3. 内存层级对应
// - 块级同步 -> 共享内存
// - 设备级同步 -> 设备内存
// - 系统级同步 -> 统一内存
```

### 9.3 调试技巧
```cpp
// 1. 使用符号名称
ctx.launch(spec, where, data.rw()).set_symbol("my_kernel")->*[](auto th, auto data) {
    // kernel implementation
};

// 2. 检查线程层级结构
printf("Block %zu, Thread %zu, Total blocks %zu, Threads per block %zu\n",
       th.rank(0), th.inner().rank(), th.size(0), th.inner().size());

// 3. 验证数据分区
auto b = th.apply_partition(shape(data));
printf("Thread processes elements [%zu, %zu)\n", b.get_begin(0), b.get_end(0));
```

## 10. 与传统CUDA编程的对比

### 10.1 传统CUDA方式
```cpp
__global__ void prefix_scan_kernel(double* data, int N) {
    int tid = threadIdx.x;
    int bid = blockIdx.x;
    int global_id = bid * blockDim.x + tid;

    // 手动计算数据分区
    int elements_per_thread = (N + gridDim.x * blockDim.x - 1) / (gridDim.x * blockDim.x);
    int start = global_id * elements_per_thread;
    int end = min(start + elements_per_thread, N);

    // 手动管理共享内存
    __shared__ double shared_data[256];

    // 手动同步
    __syncthreads();
}
```

### 10.2 STF方式
```cpp
ctx.launch(con<8>(con<128>()), where, data.rw())->*[](auto th, auto x) {
    // 自动数据分区
    auto b = th.apply_partition(shape(x));

    // 自动内存管理
    auto shared_storage = th.template storage<double>(1);

    // 声明式同步
    th.inner().sync();
    th.sync();
};
```

### 10.3 STF的优势
1. **声明式编程**：描述"做什么"而不是"怎么做"
2. **自动优化**：STF可以根据硬件特性自动优化
3. **错误减少**：减少手动索引计算的错误
4. **可移植性**：同样的代码可以在不同硬件上运行
5. **可读性**：代码更接近算法本身的描述

---

## 性能优化与最佳实践

### 常见问题和注意事项

#### 常见误解
- ❌ **错误**：每个线程处理连续的大数据段
- ✅ **正确**：每个线程通常只处理少量元素（甚至一个）

- ❌ **错误**：pos4/dim4 是多余的设计
- ✅ **正确**：为未来多维扩展预留的接口设计

- ❌ **错误**：所有执行函数都是立即执行的
- ✅ **正确**：只有task的lambda立即执行，host_launch等待依赖就绪

#### 调试要点
1. **理解数据分区**：使用 `apply_partition` 后检查每个线程的数据范围
2. **同步层级**：确保在正确的层级调用 `sync()`
3. **内存访问**：注意共享内存的大小和访问模式
4. **硬件映射**：理解thread hierarchy如何映射到CUDA的grid/block结构

#### 性能优化建议

1. **选择合适的执行构造**：
   - **在graph_ctx中优先使用 `cuda_kernel`** 而不是 `task` 来避免图捕获开销
   - 对于简单并行操作使用 `parallel_for`
   - 对于复杂线程层级使用 `launch`

2. **CUDA Graph 优化策略**：
   - **重复执行优化**：对于重复执行的任务模式，使用 `graph_ctx` 获得更好性能
   - **图结构稳定性**：保持图结构稳定以充分利用图缓存
   - **批处理**：将多个小任务合并为大任务，减少图节点数量
   - **避免动态分支**：在图中避免依赖GPU结果的主机端条件分支

3. **后端选择策略**：
   ```cpp
   // 根据工作负载特性选择后端
   bool is_repeated_pattern = (iterations > 50);
   bool has_dynamic_logic = need_host_device_sync;

   context ctx = (is_repeated_pattern && !has_dynamic_logic)
                 ? context(graph_ctx())
                 : context(stream_ctx());
   ```

4. **图构建优化**：
   - **最小化图变化**：避免频繁改变图结构
   - **预热图缓存**：在性能关键路径前预先构建和缓存图
   - **监控图缓存命中率**：使用STF提供的统计信息优化图使用

5. **内存和同步优化**：
   - **合理设置层级大小**：考虑硬件限制（如共享内存大小、线程数限制）
   - **最小化同步**：只在必要时进行同步操作
   - **利用静态信息**：尽量使用编译时已知的大小（如 `con<128>()` 而不是 `con(128)`）

### 学习要点总结

1. **CUDASTF 提供了多层次的抽象**：
   - 底层：`task` 和 `cuda_kernel` 提供基础任务构造
   - 中层：`parallel_for` 和 `launch` 提供并行构造
   - 高层：`Token` 提供同步控制

2. **后端透明性**：
   - 通过 `std::visit` 和 `std::variant` 实现后端分发
   - 同一套代码可以在 `stream_ctx` 和 `graph_ctx` 上运行
   - 用户代码无需关心底层实现差异

3. **声明式编程模式**：
   - 描述"做什么"而不是"怎么做"
   - STF自动处理数据传输、同步和优化
   - 减少手动管理的复杂性和错误

4. **类型安全的设计**：
   - 编译时检查数据类型和访问模式
   - `owning_container_of` 确保归约操作的类型安全
   - 模板特化提供类型特定的优化

5. **灵活的同步机制**：
   - 分层同步支持复杂的并行算法
   - Token提供轻量级的任务排序
   - 自动依赖推断简化编程

### 实际应用建议

1. **从简单开始**：先使用 `parallel_for`，然后根据需要转向 `launch` 或 `cuda_kernel`
2. **合理使用Token**：在逻辑阶段边界使用Token，不要过度细化
3. **注意边界条件**：使用 `inner<>` 函数避免有限差分等算法的越界访问
4. **性能测试**：对比不同构造的性能，选择最适合的方案
5. **调试工具**：利用 `.set_symbol()` 和 DOT 图生成来调试和可视化任务图

---

## 总结

CUDASTF 是一个强大的并行编程框架，它通过声明式的API设计、自动的依赖管理和类型安全的接口，大大简化了CUDA编程的复杂性。掌握其核心概念（异步工作流、Thread Hierarchy、数据类型系统、后端分发机制）是高效使用这个框架的关键。

通过合理使用不同的执行构造、优化数据访问模式、利用Token进行同步控制，可以构建出既高效又易于维护的并行计算程序。
