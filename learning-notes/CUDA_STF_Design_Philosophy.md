# CUDA STF 核心设计思想文档

## 概述

CUDA STF (Sequential Task Flow) 是一个声明式 GPU 编程框架，通过三个核心抽象：**Context**、**Logical Data** 和 **Task**，实现了从命令式 CUDA 编程到声明式任务流编程的转变。

## 设计哲学

### 1. 声明式编程范式
- **传统 CUDA**：开发者需要显式管理内存分配、数据传输、流同步
- **STF 方式**：开发者只需声明数据依赖关系，系统自动处理执行细节

```cpp
// 传统 CUDA 方式
cudaMalloc(&d_x, size);
cudaMemcpy(d_x, h_x, size, cudaMemcpyHostToDevice);
kernel<<<blocks, threads>>>(d_x, d_y);
cudaDeviceSynchronize();

// STF 方式
auto lX = ctx.logical_data(h_x);
ctx.task(lX.read())->*[](cudaStream_t s, auto dX) {
    kernel<<<blocks, threads, 0, s>>>(dX);
};
```

### 2. 关注点分离
- **算法逻辑**：开发者专注于计算核心
- **系统管理**：框架处理内存、同步、调度

## 核心组件设计

## 1. Context：执行环境抽象

### 设计思想
Context 是整个 STF 系统的控制中心，采用**统一接口 + 多后端实现**的设计模式。

### 架构特点

#### 1.1 统一抽象层
```cpp
class context {
    std::variant<stream_ctx, graph_ctx> payload;
    
    template<typename... Deps>
    unified_task<Deps...> task(task_dep<Deps>... deps) {
        return std::visit([&](auto& self) {
            return unified_task<Deps...>(self.task(std::move(deps)...));
        }, payload);
    }
};
```

**设计优势**：
- **用户友好**：单一 API 接口，降低学习成本
- **后端透明**：运行时选择执行策略，无需修改用户代码
- **类型安全**：编译时类型检查，运行时零开销分发

#### 1.2 多后端支持
- **Stream Context**：立即执行模式，适合交互式开发
- **Graph Context**：延迟执行模式，适合生产环境优化

#### 1.3 资源管理
- **流池管理**：自动管理 CUDA 流的分配和重用
- **内存分配器**：支持缓存、池化等多种分配策略
- **设备管理**：自动处理多 GPU 环境

## 2. Logical Data：数据抽象

### 设计思想
Logical Data 将**数据的逻辑概念**与**物理存储**分离，实现了数据的位置透明性和一致性管理。

### 核心特性

#### 2.1 位置透明性
```cpp
auto lX = ctx.logical_data(host_array);  // 创建逻辑数据
// STF 自动管理数据在 host/device 间的传输
ctx.task(lX.read(data_place::device(0)))->*[](auto dX) { /* 使用设备数据 */ };
ctx.task(lX.read(data_place::host()))->*[](auto hX) { /* 使用主机数据 */ };
```

#### 2.2 MSI 一致性协议
采用类似 CPU 缓存的 MSI (Modified-Shared-Invalid) 协议：
- **Modified**：数据已修改，是最新版本
- **Shared**：数据可被多处共享读取
- **Invalid**：数据无效，需要从其他位置获取

#### 2.3 实例管理
```cpp
class logical_data_untyped_impl {
    // 每个逻辑数据可以有多个物理实例
    std::unordered_map<instance_id_t, data_instance> instances;
    
    // MSI 状态管理
    void enforce_msi_protocol(instance_id_t id, access_mode mode);
};
```

#### 2.4 延迟分配
- 只在实际需要时分配内存
- 支持从形状创建：`ctx.logical_data(shape_of<slice<double>>(N))`

## 3. Task：计算抽象

### 设计思想
Task 是 STF 的核心抽象，将**计算意图**与**执行细节**分离。

### 关键设计

#### 3.1 依赖驱动执行
```cpp
// 任务通过数据依赖声明执行顺序
ctx.task(lX.read(), lY.write())->*lambda;  // Task 1
ctx.task(lY.read(), lZ.write())->*lambda;  // Task 2 自动等待 Task 1
```

#### 3.2 类型安全的参数传递
```cpp
// 编译时类型推导链
lX.read()  → task_dep<slice<const T>>    // 只读依赖
lY.write() → task_dep<slice<T>>          // 可写依赖
           ↓
unified_task<slice<const T>, slice<T>>   // 类型化任务
           ↓
lambda(cudaStream_t, slice<const T>, slice<T>)  // 类型安全的参数
```

#### 3.3 访问模式语义
- **read()**：只读访问，多任务可并发
- **write()**：写访问，独占执行
- **rw()**：读写访问，独占执行
- **reduce()**：归约访问，支持并行归约

#### 3.4 任务生命周期
```cpp
// 1. Setup 阶段：收集依赖信息
auto task = ctx.task(deps...);

// 2. Submission 阶段：提交执行
task->*lambda;

// 3. Execution 阶段：
//    - acquire(): 获取执行权限，解析依赖
//    - 执行用户 lambda
//    - release(): 释放资源，更新依赖状态
```

## 协同工作机制

### 1. 任务创建流程
```
用户调用: ctx.task(lX.read(), lY.write())
    ↓
1. 创建 task_dep 对象 (依赖声明)
    ↓
2. 后端分发 (std::visit)
    ↓
3. 创建具体任务对象 (stream_task/graph_task)
    ↓
4. 包装为 unified_task (统一接口)
```

### 2. 任务执行流程
```
operator->*(lambda)  // 任务提交
    ↓
start()             // 任务启动
    ↓
acquire()           // 依赖解析
    ↓
fetch_data()        // 数据准备
    ↓
std::apply(lambda)  // 执行用户代码
    ↓
release()           // 资源释放
```

### 3. 依赖管理机制
```cpp
// STF 自动分析访问模式建立依赖
Task A: lX.write()  →  Task B: lX.read()   // Write-After-Read
Task B: lX.read()   →  Task C: lX.write()  // Read-After-Write
Task C: lX.write()  →  Task D: lX.read()   // Write-After-Write
```

## 设计优势

### 1. 开发效率
- **简化 API**：声明式编程，减少样板代码
- **自动优化**：系统自动处理内存管理和同步
- **类型安全**：编译时错误检查

### 2. 性能优化
- **流池管理**：减少流创建开销
- **缓存分配器**：减少内存分配开销
- **智能传输**：最小化数据传输
- **并行执行**：自动识别可并行的任务

### 3. 可维护性
- **关注点分离**：算法与系统管理分离
- **统一抽象**：一套 API 支持多种执行模式
- **模块化设计**：各组件职责清晰

### 4. 可扩展性
- **后端可插拔**：易于添加新的执行后端
- **分配器可定制**：支持多种内存管理策略
- **调度器可配置**：支持不同的任务调度策略

## 总结

CUDA STF 通过 Context、Logical Data 和 Task 三个核心抽象，实现了：

1. **编程模式转变**：从命令式到声明式
2. **复杂性隐藏**：将 GPU 编程细节抽象化
3. **性能与易用性平衡**：在保持高性能的同时大幅简化开发
4. **架构灵活性**：支持多种执行策略和优化方案

这种设计让开发者能够专注于算法本身，而将内存管理、同步控制、性能优化等复杂任务交给框架处理，大大降低了 GPU 编程的门槛。

## 深入技术实现

### 1. Context 实现细节

#### 1.1 异步资源管理
```cpp
class async_resources_handle {
    // 流池：为每个设备维护计算和传输流池
    std::vector<std::pair<stream_pool, stream_pool>> pool;

    // ID 池：管理流的唯一标识符，用于同步优化
    id_pool ids;

    // 同步缓存：避免重复的流间同步操作
    last_event_per_stream sync_cache;

    // 图缓存：存储已实例化的 CUDA 图
    std::unordered_map<graph_key, cuda_graph> graph_cache;
};
```

**设计亮点**：
- **流池优化**：预分配流，避免运行时创建开销
- **同步优化**：缓存流间同步事件，跳过冗余同步
- **图重用**：缓存 CUDA 图，提高重复执行效率

#### 1.2 分配器层次结构
```cpp
// 三层分配器架构
Uncached Allocator (底层)
    ↓ 直接调用 CUDA API
Cached Allocator (中层)
    ↓ 缓存策略，减少分配开销
Custom Allocator (用户层)
    ↓ 用户可定制的分配策略
```

### 2. Logical Data 实现细节

#### 2.1 实例管理机制
```cpp
class logical_data_untyped_impl {
    // 全局唯一 ID
    reserved::unique_id_t unique_id;

    // 实例映射：每个内存位置一个实例
    std::unordered_map<instance_id_t, data_instance> instances;

    // 引用计数：防止数据被过早回收
    std::atomic<int> refcnt;

    // 写回机制：支持数据自动写回原始位置
    instance_id_t reference_instance_id;
    bool enable_write_back;
};
```

#### 2.2 MSI 协议实现
```cpp
void enforce_msi_protocol(instance_id_t id, access_mode mode) {
    auto& instance = get_data_instance(id);
    auto current_state = instance.get_msir();

    switch (mode) {
        case access_mode::read:
            if (current_state == msir_state_id::invalid) {
                // 从其他有效实例拷贝数据
                initiate_data_copy(id);
                instance.set_msir(msir_state_id::shared);
            }
            break;

        case access_mode::write:
            // 写访问使其他实例失效
            invalidate_other_instances(id);
            instance.set_msir(msir_state_id::modified);
            break;
    }
}
```

#### 2.3 数据接口抽象
```cpp
template<typename T>
class slice_interface : public data_interface {
    // 类型安全的实例获取
    template<typename U>
    decltype(auto) instance(instance_id_t id) {
        if constexpr (std::is_same_v<U, readonly_type_of<T>>) {
            return const_cast<const T&>(get_instance(id));
        } else {
            return get_instance(id);
        }
    }

    // 内存管理
    void allocate(instance_id_t id, const data_place& place) override;
    void deallocate(instance_id_t id) override;
    void copy(instance_id_t src, instance_id_t dst) override;
};
```

### 3. Task 实现细节

#### 3.1 依赖解析算法
```cpp
event_list task::acquire(backend_ctx_untyped& ctx) {
    auto& deps = get_task_deps();

    // 1. 排序依赖避免死锁
    std::sort(deps.begin(), deps.end(), [](const auto& a, const auto& b) {
        return a.get_data().get_unique_id() < b.get_data().get_unique_id();
    });

    // 2. 逐个处理依赖
    for (auto& dep : deps) {
        auto& logical_data = dep.get_data();

        // 3. 锁定逻辑数据
        logical_data.get_mutex().lock();

        // 4. 获取或创建实例
        auto instance_id = logical_data.find_instance_id(dep.get_dplace());
        dep.set_instance_id(instance_id);

        // 5. 强制执行 STF 依赖
        auto prereqs = enforce_stf_deps_before(ctx, logical_data, instance_id,
                                               *this, dep.get_access_mode());

        // 6. 分配和传输数据
        fetch_data(ctx, logical_data, instance_id, *this,
                   dep.get_access_mode(), get_exec_place(),
                   dep.get_dplace(), prereqs);
    }
}
```

#### 3.2 STF 依赖管理
```cpp
template<typename task_type>
event_list enforce_stf_deps_before(/* ... */) {
    auto& state = logical_data.get_state();
    event_list result;

    switch (access_mode) {
        case access_mode::read:
            // 读任务等待写任务完成
            if (state.current_writer) {
                result.merge(state.current_writer.get_event());
            }
            // 多个读任务可以并发
            state.current_readers.add(task);
            break;

        case access_mode::write:
            // 写任务等待所有读写任务完成
            result.merge(state.current_readers.get_event_list());
            if (state.current_writer) {
                result.merge(state.current_writer.get_event());
            }
            // 更新当前写任务
            state.current_writer = task;
            break;
    }

    return result;
}
```

#### 3.3 类型安全的参数传递
```cpp
template<typename Fun>
auto stream_task<Deps...>::operator->*(Fun&& fun) {
    // 启动任务
    start();

    // 准备类型化的数据实例
    auto typed_data = make_tuple_indexwise<sizeof...(Deps)>([&](auto i) {
        using DataType = std::tuple_element_t<i, std::tuple<Deps...>>;
        return this->get<DataType>(i);
    });

    // 调用用户 lambda
    if constexpr (std::is_invocable_v<Fun, cudaStream_t, Deps...>) {
        auto args = tuple_prepend(get_stream(), typed_data);
        return std::apply(std::forward<Fun>(fun), args);
    }
}
```

### 4. 性能优化机制

#### 4.1 流重用优化
```cpp
decorated_stream pick_stream_from_pool(const exec_place& place) {
    auto& pool = place.get_stream_pool(async_resources, true);

    // 优先重用相同任务类型的流
    for (auto& stream : pool) {
        if (stream.id != -1 && can_reuse_stream(stream)) {
            return stream;
        }
    }

    // 创建新流
    return create_new_stream(place);
}
```

#### 4.2 同步跳过机制
```cpp
bool validate_sync_and_update(std::ptrdiff_t dst_id, std::ptrdiff_t src_id, int event_id) {
    // 如果流不在池中，不能跳过同步
    if (dst_id == -1 || src_id == -1) return false;

    // 检查是否已有更新的同步事件
    auto& last_sync = sync_matrix[dst_id][src_id];
    if (last_sync >= event_id) {
        return true;  // 可以跳过同步
    }

    // 更新同步记录
    last_sync = event_id;
    return false;  // 需要执行同步
}
```

#### 4.3 内存回收机制
```cpp
void reclaim_memory(backend_ctx_untyped& ctx, const data_place& place,
                    size_t requested_size, size_t& reclaimed_size) {
    auto& instances = get_reclaimable_instances(place);

    // 按 LRU 策略排序
    std::sort(instances.begin(), instances.end(),
              [](const auto& a, const auto& b) {
                  return a.last_access_time < b.last_access_time;
              });

    // 回收内存直到满足需求
    for (auto& inst : instances) {
        if (reclaimed_size >= requested_size) break;

        if (inst.can_reclaim()) {
            deallocate_instance(inst);
            reclaimed_size += inst.allocated_size;
        }
    }
}
```

## 设计模式总结

### 1. 架构模式
- **分层架构**：用户 API → 统一抽象 → 后端实现 → CUDA API
- **策略模式**：可插拔的分配器、调度器
- **观察者模式**：依赖关系管理

### 2. 内存管理模式
- **RAII**：自动资源管理
- **引用计数**：防止过早释放
- **写时复制**：延迟数据传输

### 3. 并发模式
- **生产者-消费者**：任务队列管理
- **读写锁**：数据访问控制
- **事件驱动**：异步执行同步

这些设计和实现细节共同构成了 STF 强大而高效的 GPU 编程抽象，实现了易用性和性能的完美平衡。

## 实际应用示例

### 1. 基础数据流水线
```cpp
// 传统 CUDA 实现
void traditional_pipeline() {
    // 手动内存管理
    double *d_input, *d_temp, *d_output;
    cudaMalloc(&d_input, size);
    cudaMalloc(&d_temp, size);
    cudaMalloc(&d_output, size);

    // 手动数据传输
    cudaMemcpy(d_input, h_input, size, cudaMemcpyHostToDevice);

    // 手动同步
    cudaStream_t stream1, stream2;
    cudaStreamCreate(&stream1);
    cudaStreamCreate(&stream2);

    // 执行计算
    preprocess<<<blocks, threads, 0, stream1>>>(d_input, d_temp);
    cudaStreamSynchronize(stream1);

    postprocess<<<blocks, threads, 0, stream2>>>(d_temp, d_output);
    cudaStreamSynchronize(stream2);

    // 手动清理
    cudaMemcpy(h_output, d_output, size, cudaMemcpyDeviceToHost);
    cudaFree(d_input); cudaFree(d_temp); cudaFree(d_output);
}

// STF 实现
void stf_pipeline() {
    context ctx;

    // 声明式数据管理
    auto input = ctx.logical_data(h_input);
    auto temp = ctx.logical_data(shape_of<slice<double>>(N));
    auto output = ctx.logical_data(shape_of<slice<double>>(N));

    // 声明式任务流
    ctx.task(input.read(), temp.write())->*[](cudaStream_t s, auto in, auto tmp) {
        preprocess<<<blocks, threads, 0, s>>>(in, tmp);
    };

    ctx.task(temp.read(), output.write())->*[](cudaStream_t s, auto tmp, auto out) {
        postprocess<<<blocks, threads, 0, s>>>(tmp, out);
    };

    // 自动执行和清理
    ctx.finalize();
}
```

### 2. 复杂依赖管理
```cpp
void complex_dependencies() {
    context ctx;

    auto A = ctx.logical_data(matrix_A);
    auto B = ctx.logical_data(matrix_B);
    auto C = ctx.logical_data(shape_of<slice<double>>(N*N));
    auto D = ctx.logical_data(shape_of<slice<double>>(N*N));
    auto result = ctx.logical_data(shape_of<slice<double>>(N*N));

    // 并行计算：C = A * B, D = A^T * B
    auto task1 = ctx.task(A.read(), B.read(), C.write())
        ->*[](cudaStream_t s, auto a, auto b, auto c) {
            gemm<<<blocks, threads, 0, s>>>(a, b, c);
        };

    auto task2 = ctx.task(A.read(), B.read(), D.write())
        ->*[](cudaStream_t s, auto a, auto b, auto d) {
            gemm_transpose<<<blocks, threads, 0, s>>>(a, b, d);
        };

    // 串行计算：result = C + D（自动等待 task1 和 task2 完成）
    ctx.task(C.read(), D.read(), result.write())
        ->*[](cudaStream_t s, auto c, auto d, auto res) {
            add_matrices<<<blocks, threads, 0, s>>>(c, d, res);
        };

    ctx.finalize();
}
```

### 3. 多 GPU 协作
```cpp
void multi_gpu_example() {
    context ctx;

    auto data = ctx.logical_data(large_dataset);
    auto result1 = ctx.logical_data(shape_of<slice<double>>(N/2));
    auto result2 = ctx.logical_data(shape_of<slice<double>>(N/2));
    auto final_result = ctx.logical_data(shape_of<slice<double>>(N));

    // GPU 0 处理前半部分
    ctx.task(exec_place::device(0),
             data.read(data_place::device(0)),
             result1.write(data_place::device(0)))
        ->*[](cudaStream_t s, auto data_slice, auto res1) {
            process_first_half<<<blocks, threads, 0, s>>>(data_slice, res1);
        };

    // GPU 1 处理后半部分
    ctx.task(exec_place::device(1),
             data.read(data_place::device(1)),
             result2.write(data_place::device(1)))
        ->*[](cudaStream_t s, auto data_slice, auto res2) {
            process_second_half<<<blocks, threads, 0, s>>>(data_slice, res2);
        };

    // 合并结果
    ctx.task(result1.read(), result2.read(), final_result.write())
        ->*[](cudaStream_t s, auto r1, auto r2, auto final) {
            merge_results<<<blocks, threads, 0, s>>>(r1, r2, final);
        };

    ctx.finalize();
}
```

## 最佳实践指南

### 1. 任务设计原则

#### 1.1 粒度控制
```cpp
// ❌ 过细粒度：每个元素一个任务
for (int i = 0; i < N; i++) {
    ctx.task(data.read(), result.write())->*[i](auto d, auto r) {
        process_element<<<1, 1>>>(d, r, i);
    };
}

// ✅ 合适粒度：批量处理
ctx.task(data.read(), result.write())->*[](auto d, auto r) {
    process_batch<<<blocks, threads>>>(d, r);
};
```

#### 1.2 依赖最小化
```cpp
// ❌ 不必要的依赖
ctx.task(A.read(), B.read(), C.read(), result.write())->*[](auto a, auto b, auto c, auto r) {
    // 实际只使用 A 和 B
    compute<<<blocks, threads>>>(a, b, r);
};

// ✅ 最小依赖
ctx.task(A.read(), B.read(), result.write())->*[](auto a, auto b, auto r) {
    compute<<<blocks, threads>>>(a, b, r);
};
```

### 2. 内存管理最佳实践

#### 2.1 数据位置策略
```cpp
// 明确指定数据位置以优化传输
ctx.task(data.read(data_place::device(0)),      // 在 GPU 0 上读取
         result.write(data_place::device(0)))   // 在 GPU 0 上写入
    ->*[](auto d, auto r) {
        kernel<<<blocks, threads>>>(d, r);
    };
```

#### 2.2 临时数据管理
```cpp
// 使用作用域限制临时数据生命周期
{
    auto temp = ctx.logical_data(shape_of<slice<double>>(N));
    ctx.task(input.read(), temp.write())->*[](auto in, auto tmp) {
        preprocess<<<blocks, threads>>>(in, tmp);
    };
    ctx.task(temp.read(), output.write())->*[](auto tmp, auto out) {
        postprocess<<<blocks, threads>>>(tmp, out);
    };
} // temp 在此处可以被回收
```

### 3. 性能优化技巧

#### 3.1 流重用
```cpp
// 为相似任务使用相同的执行位置
auto place = exec_place::device(0);
ctx.task(place, A.read(), B.write())->*lambda1;
ctx.task(place, B.read(), C.write())->*lambda2;  // 可能重用相同的流
```

#### 3.2 批量操作
```cpp
// 批量提交任务以减少开销
std::vector<decltype(ctx.task(data.read()))> tasks;
for (int i = 0; i < batch_size; i++) {
    tasks.push_back(ctx.task(data.read(), results[i].write()));
}

// 批量设置 lambda
for (int i = 0; i < batch_size; i++) {
    tasks[i]->*[i](auto d, auto r) {
        process<<<blocks, threads>>>(d, r, i);
    };
}
```

### 4. 调试和性能分析

#### 4.1 符号命名
```cpp
// 为任务和数据设置有意义的符号名
auto input = ctx.logical_data(data);
input.set_symbol("InputMatrix");

ctx.task(input.read(), output.write())
    .set_symbol("MatrixProcessing")
    ->*[](auto in, auto out) {
        process<<<blocks, threads>>>(in, out);
    };
```

#### 4.2 性能监控
```cpp
// 启用性能统计
export CUDASTF_DOT_FILE=task_graph.dot  // 生成任务图
export CUDASTF_TIMING=1                 // 启用时间统计

// 在代码中检查性能
ctx.finalize();
auto stats = ctx.get_statistics();
std::cout << "Total execution time: " << stats.total_time << "ms\n";
```

## 常见陷阱和解决方案

### 1. 数据竞争
```cpp
// ❌ 潜在的数据竞争
ctx.task(data.write())->*lambda1;  // Task 1 写入
ctx.task(data.read())->*lambda2;   // Task 2 读取，但可能在 Task 1 之前执行

// ✅ 正确的依赖声明
auto task1 = ctx.task(data.write())->*lambda1;
ctx.task(data.read())->*lambda2;  // STF 自动确保正确顺序
```

### 2. 内存泄漏
```cpp
// ❌ 忘记 finalize
{
    context ctx;
    ctx.task(data.read())->*lambda;
    // 没有调用 ctx.finalize()，资源可能不会被正确释放
}

// ✅ 确保资源清理
{
    context ctx;
    ctx.task(data.read())->*lambda;
    ctx.finalize();  // 确保所有任务完成并清理资源
}
```

### 3. 类型不匹配
```cpp
// ❌ Lambda 参数类型错误
ctx.task(data.read())->*[](slice<double> d) {  // 应该是 slice<const double>
    // ...
};

// ✅ 使用 auto 避免类型错误
ctx.task(data.read())->*[](auto d) {
    // ...
};
```

这份文档全面展示了 CUDA STF 的设计思想、实现细节和实际应用，为开发者提供了从理论到实践的完整指导。
