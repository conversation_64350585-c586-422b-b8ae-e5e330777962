# CUDA STF Token 使用详解

## 什么是Token？

Token（令牌）是CUDA STF中的一种特殊类型的逻辑数据，它的**唯一目的是自动化同步**，而不包含任何实际的数据内容。Token让应用程序可以管理自己的数据缓冲区，同时利用STF的依赖关系管理来确保正确的执行顺序。

## Token的核心特性

### 1. 轻量级同步
- Token基于`void_interface`实现
- 不进行实际的内存分配或数据传输
- 运行时开销最小化
- 专门用于依赖关系管理

### 2. 三种访问模式
- **read()**: 只读访问，多个任务可以并发读取
- **write()**: 写访问，独占访问，其他任务必须等待
- **rw()**: 读写访问，独占访问

### 3. 自动依赖管理
- STF自动根据token的访问模式建立任务间的依赖关系
- 确保数据竞争的避免
- 简化复杂同步逻辑的编写

## Token的创建方式

### 方式1：使用ctx.token()（推荐）
```cpp
auto token = ctx.token();
token.set_symbol("MyToken");  // 可选：设置符号名用于调试
```

### 方式2：通过void_interface的shape创建
```cpp
auto token = ctx.logical_data(shape_of<void_interface>());
```

### 方式3：通过void_interface实例创建
```cpp
void_interface sync_obj;
auto token = ctx.logical_data(sync_obj);
```

## 典型使用场景

### 1. 简单的任务排序
```cpp
auto token = ctx.token();

// 任务1：获得写权限，独占执行
ctx.task(token.write())->*[](cudaStream_t stream) {
    // 初始化工作
};

// 任务2：等待任务1完成后执行
ctx.task(token.read())->*[](cudaStream_t stream) {
    // 后续工作
};
```

### 2. 与实际数据结合使用
```cpp
auto data = ctx.logical_data(host_array);
auto sync_token = ctx.token();

// 控制数据访问顺序
ctx.task(data.write(), sync_token.write())->*[](cudaStream_t s, auto d) {
    // 初始化数据
};

ctx.task(data.rw(), sync_token.read())->*[](cudaStream_t s, auto d) {
    // 处理数据（等待初始化完成）
};
```

### 3. 复杂的多阶段流水线
```cpp
auto phase1_token = ctx.token().set_symbol("Phase1");
auto phase2_token = ctx.token().set_symbol("Phase2");
auto phase3_token = ctx.token().set_symbol("Phase3");

// 阶段1：并行初始化
ctx.task(phase1_token.write())->*[](cudaStream_t) {
    // 初始化组件A
};

// 阶段2：等待阶段1完成
ctx.task(phase1_token.read(), phase2_token.write())->*[](cudaStream_t) {
    // 基于阶段1的结果进行处理
};

// 阶段3：等待阶段2完成
ctx.task(phase2_token.read(), phase3_token.write())->*[](cudaStream_t) {
    // 最终处理
};
```

### 4. 并发读取场景
```cpp
auto shared_token = ctx.token();

// 初始化阶段
ctx.task(shared_token.write())->*[](cudaStream_t) {
    // 设置共享状态
};

// 多个任务可以并发读取
ctx.task(shared_token.read())->*[](cudaStream_t) {
    // 读取操作1
};

ctx.task(shared_token.read())->*[](cudaStream_t) {
    // 读取操作2（可以与操作1并发）
};
```

## Token vs 普通逻辑数据

| 特性 | Token | 普通逻辑数据 |
|------|-------|-------------|
| 数据内容 | 无实际数据 | 包含实际数据 |
| 内存分配 | 不分配内存 | 需要分配内存 |
| 数据传输 | 无数据传输 | 可能需要Host-Device传输 |
| 主要用途 | 同步控制 | 数据处理 |
| 性能开销 | 极低 | 相对较高 |
| Lambda参数 | 可以省略 | 必须使用 |

## 高级用法

### 1. Token参数省略
由于token不包含实际数据，在lambda函数中可以省略对应的参数：

```cpp
auto token = ctx.token();

// 完整写法
ctx.task(token.rw())->*[](cudaStream_t stream, void_interface dummy) {
    // 处理逻辑
};

// 简化写法（推荐）
ctx.task(token.rw())->*[](cudaStream_t stream) {
    // 处理逻辑，token参数被自动省略
};
```

### 2. Token冻结
对于频繁访问的token，可以使用冻结机制：

```cpp
auto token = ctx.token();
auto frozen_token = ctx.freeze(token);

cudaStream_t stream = ctx.pick_stream();
auto device_token = frozen_token.get(data_place::current_device(), stream);
// 使用frozen token...
frozen_token.unfreeze(stream);
```

### 3. 与其他STF特性结合
```cpp
// 与执行位置结合
ctx.task(exec_place::device(0), token.write())->*[](cudaStream_t) {
    // 在特定设备上执行
};

// 与任务符号结合
ctx.task(token.rw()).set_symbol("ImportantTask")->*[](cudaStream_t) {
    // 带符号的任务，便于调试和可视化
};
```

## 最佳实践

### 1. 命名规范
```cpp
auto init_token = ctx.token().set_symbol("InitializationToken");
auto compute_token = ctx.token().set_symbol("ComputationToken");
auto cleanup_token = ctx.token().set_symbol("CleanupToken");
```

### 2. 合理的粒度
- 不要为每个小操作都创建token
- 在逻辑阶段边界使用token
- 平衡同步精度和性能开销

### 3. 与数据逻辑数据配合
```cpp
// 好的做法：token控制阶段，数据控制具体操作
auto phase_token = ctx.token();
auto data = ctx.logical_data(array);

ctx.task(phase_token.write(), data.write())->*[](cudaStream_t s, auto d) {
    // 阶段初始化 + 数据初始化
};

ctx.task(phase_token.read(), data.rw())->*[](cudaStream_t s, auto d) {
    // 等待阶段就绪 + 处理数据
};
```

### 4. 错误避免
```cpp
// 避免：不必要的token链
auto token1 = ctx.token();
auto token2 = ctx.token();
ctx.task(token1.write())->*[](cudaStream_t) {};
ctx.task(token1.read(), token2.write())->*[](cudaStream_t) {};
ctx.task(token2.read())->*[](cudaStream_t) {};

// 推荐：直接使用数据依赖
auto data = ctx.logical_data(array);
ctx.task(data.write())->*[](cudaStream_t, auto d) {};
ctx.task(data.read())->*[](cudaStream_t, auto d) {};
```

## 总结

Token是CUDA STF中强大而轻量级的同步工具，特别适用于：

1. **复杂的多阶段计算流水线**
2. **需要精确控制执行顺序的场景**
3. **用户管理自己缓冲区但需要同步的情况**
4. **性能敏感的应用（最小化同步开销）**

通过合理使用token，可以构建出既高效又易于理解的并行计算程序。
