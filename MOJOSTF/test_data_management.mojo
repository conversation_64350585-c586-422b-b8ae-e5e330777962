#!/usr/bin/env mojo

"""
Test Real Data Management.

This test specifically verifies that our STF implementation has proper data management:
1. Tensors should have persistent data storage
2. Buffer reuse across tasks on the same context
3. Data transfer between contexts when needed
4. No unnecessary buffer creation

Usage:
    cd /root/stf_exp/MOJOSTF
    pixi shell
    mojo test_data_management.mojo
"""

from layout import Layout
from gpu import global_idx
from src import (
    STFContext, LogicalTensor, TensorDependency,
    create_stf_context, 
    tensor_scale_f32_kernel,
    HostPlace, DevicePlace, ExecPlace, DType
)


fn test_buffer_reuse_same_context() raises:
    """Test that buffers are reused on the same context."""
    print("=== Testing Buffer Reuse on Same Context ===")
    
    var ctx = create_stf_context(num_streams=2)
    
    alias layout = Layout([32, 32], [32, 1])
    var tensor = ctx.logical_tensor[DType.float32, layout]()
    
    print("Created tensor", tensor._tensor_id)
    
    # Task 1: First operation on context 0
    print("\n📝 Task 1: First operation on context 0")
    ctx.task(tensor.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Task 2: Second operation on SAME context 0 - should reuse buffer
    print("\n📝 Task 2: Second operation on SAME context 0 (should reuse buffer)")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Task 3: Third operation on SAME context 0 - should reuse buffer again
    print("\n📝 Task 3: Third operation on SAME context 0 (should reuse buffer again)")
    ctx.task(tensor.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.synchronize()
    print("✅ Buffer reuse test completed!")
    print("Expected: Only ONE buffer creation for tensor on context 0")


fn test_data_transfer_between_contexts() raises:
    """Test data transfer between different contexts."""
    print("\n=== Testing Data Transfer Between Contexts ===")
    
    var ctx = create_stf_context(num_streams=3)
    
    alias layout = Layout([64, 64], [64, 1])
    var tensor = ctx.logical_tensor[DType.float32, layout]()
    
    print("Created tensor", tensor._tensor_id)
    
    # Task 1: Write data on context 0
    print("\n📝 Task 1: Write data on context 0")
    ctx.task(tensor.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Task 2: Read data on context 1 - should transfer data from context 0
    print("\n📝 Task 2: Read data on context 1 (should transfer from context 0)")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Task 3: Read data on context 2 - should transfer data from context 0
    print("\n📝 Task 3: Read data on context 2 (should transfer from context 0)")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.synchronize()
    print("✅ Data transfer test completed!")
    print("Expected: Data transfer messages between contexts")


fn test_multiple_tensors_independent_buffers() raises:
    """Test that different tensors have independent buffer management."""
    print("\n=== Testing Multiple Tensors Independent Buffers ===")
    
    var ctx = create_stf_context(num_streams=2)
    
    alias layout = Layout([32, 32], [32, 1])
    var tensor_a = ctx.logical_tensor[DType.float32, layout]()
    var tensor_b = ctx.logical_tensor[DType.float32, layout]()
    var tensor_c = ctx.logical_tensor[DType.float32, layout]()
    
    print("Created tensors:", tensor_a._tensor_id, tensor_b._tensor_id, tensor_c._tensor_id)
    
    # Each tensor should have its own buffer on context 0
    print("\n📝 Operations on context 0 for different tensors")
    ctx.task(tensor_a.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.task(tensor_b.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.task(tensor_c.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Reuse buffers for same tensors
    print("\n📝 Reusing buffers for same tensors on context 0")
    ctx.task(tensor_a.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.task(tensor_b.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.synchronize()
    print("✅ Multiple tensors test completed!")
    print("Expected: 3 buffer creations (one per tensor), then reuse")


fn test_data_lifecycle() raises:
    """Test complete data lifecycle: create, write, read, transfer, modify."""
    print("\n=== Testing Complete Data Lifecycle ===")
    
    var ctx = create_stf_context(num_streams=4)
    
    alias layout = Layout([128, 128], [128, 1])
    var tensor = ctx.logical_tensor[DType.float32, layout]()
    
    print("Created tensor", tensor._tensor_id, "for lifecycle test")
    
    # Phase 1: Initial creation and write
    print("\n🔄 Phase 1: Initial creation and write on context 0")
    ctx.task(tensor.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Phase 2: Read on same context (should reuse buffer)
    print("\n🔄 Phase 2: Read on same context 0 (should reuse buffer)")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Phase 3: Transfer to different context
    print("\n🔄 Phase 3: Transfer to context 1")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Phase 4: Modify on new context
    print("\n🔄 Phase 4: Modify on context 1")
    ctx.task(tensor.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Phase 5: Read from multiple contexts
    print("\n🔄 Phase 5: Read from multiple contexts")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.synchronize()
    print("✅ Data lifecycle test completed!")
    print("Expected: Proper buffer management throughout entire lifecycle")


fn main() raises:
    """Run all data management tests."""
    print("MOJO STF Data Management Test")
    print("=============================")
    print("Testing real data management capabilities")
    
    test_buffer_reuse_same_context()
    test_data_transfer_between_contexts()
    test_multiple_tensors_independent_buffers()
    test_data_lifecycle()
    
    print("\n🎉 ALL DATA MANAGEMENT TESTS COMPLETED! 🎉")
    print("\n📊 Analysis of Current Implementation:")
    print("   If you see 'Creating new buffer' for every operation:")
    print("   ❌ Data management is NOT properly implemented")
    print("   ❌ Buffers are being created unnecessarily")
    print("   ❌ No real data persistence")
    print("")
    print("   If you see 'Reusing existing buffer' for same tensor/context:")
    print("   ✅ Data management is properly implemented")
    print("   ✅ Buffers are being reused correctly")
    print("   ✅ Real data persistence achieved")
    print("")
    print("   If you see 'Copying data from context X to context Y':")
    print("   ✅ Data transfer is properly implemented")
    print("   ✅ Position transparency is working")
    print("")
    print("🎯 This test reveals whether we have REAL data management!")
    print("   Real STF should minimize buffer creation and maximize reuse.")
