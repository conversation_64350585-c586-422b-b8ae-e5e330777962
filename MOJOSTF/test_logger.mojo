#!/usr/bin/env mojo

"""
Test the STF logging system.

This test verifies that the logging system works correctly with different log levels.

Usage:
    cd /root/stf_exp/MOJOSTF
    pixi shell
    
    # Test with different log levels
    STF_LOG_LEVEL=DEBUG mojo test_logger.mojo
    STF_LOG_LEVEL=INFO mojo test_logger.mojo
    STF_LOG_LEVEL=WARNING mojo test_logger.mojo
    STF_LOG_LEVEL=ERROR mojo test_logger.mojo
    STF_LOG_LEVEL=SILENT mojo test_logger.mojo
"""

from src.logger import (
    STFLogger, STFTaskLogger, STFDataLogger, STFSyncLogger,
    log_debug, log_info, log_warning, log_error,
    task_log, data_log, sync_log
)


fn test_basic_logging():
    """Test basic logging functions."""
    print("=== Testing Basic Logging ===")
    
    log_debug("This is a debug message")
    log_info("This is an info message")
    log_warning("This is a warning message")
    log_error("This is an error message")
    
    print("✅ Basic logging test completed")


fn test_specialized_loggers():
    """Test specialized logging functions."""
    print("\n=== Testing Specialized Loggers ===")
    
    # Test task logger
    task_log.task_created(123, 456)
    task_log.task_submitted(123, 0)
    task_log.task_dependency_wait(123, 1)
    
    # Test data logger
    data_log.tensor_created(456, 1024)
    data_log.buffer_created(456, 0)
    data_log.buffer_reused(456, 1)
    data_log.data_transfer("HOST", "context 0")
    
    # Test sync logger
    sync_log.sync_start()
    sync_log.sync_complete()
    
    print("✅ Specialized loggers test completed")


fn test_log_levels():
    """Test different log levels."""
    print("\n=== Testing Log Levels ===")
    
    print("Current log level configuration:")
    print("- Set STF_LOG_LEVEL environment variable to control output")
    print("- Available levels: DEBUG, INFO, WARNING, ERROR, SILENT")
    print("- Default: INFO")
    
    print("\nTesting all log levels:")
    STFLogger.debug("Debug level message")
    STFLogger.info("Info level message")
    STFLogger.warning("Warning level message")
    STFLogger.error("Error level message")
    
    print("✅ Log levels test completed")


fn main():
    """Run all logger tests."""
    print("STF Logging System Test")
    print("=======================")
    
    test_basic_logging()
    test_specialized_loggers()
    test_log_levels()
    
    print("\n🎉 ALL LOGGER TESTS COMPLETED! 🎉")
    print("\n📊 Logger Features Demonstrated:")
    print("✅ Configurable log levels via environment variable")
    print("✅ Basic logging functions: debug, info, warning, error")
    print("✅ Specialized loggers for different STF components:")
    print("   - Task operations (creation, submission, dependencies)")
    print("   - Data operations (tensors, buffers, transfers)")
    print("   - Synchronization operations")
    print("✅ Clean, structured log output")
    print("")
    print("🎯 Usage in STF code:")
    print("   Replace direct print() calls with appropriate log functions")
    print("   Use STF_LOG_LEVEL environment variable to control verbosity")
    print("   Production deployments can use ERROR or SILENT levels")
