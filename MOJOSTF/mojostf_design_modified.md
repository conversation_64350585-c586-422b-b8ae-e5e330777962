## MOJO STF (Sequential Task Flow) 设计与实现文档

### 概述

MOJO STF (Sequential Task Flow) 是一个为 Mojo 语言设计的声明式异构编程框架。它借鉴了 CUDA STF 的核心思想，并针对 Mojo 的语言特性和 GPU 基础设施进行了定制。

本项目包含两个层次：
1. **理想设计**：完整的STF框架设计，包含复杂的依赖管理和自动数据传输
2. **原型实现**：简化的可工作原型，验证核心概念和基础架构

### 当前实现状态

**✅ 已实现的核心组件**：
- `SimpleMojoContext`：简化的上下文管理器
- `SimpleTask`：基础任务抽象和状态管理
- `TaskDependency`：依赖关系表示
- 基础类型系统：执行位置、数据位置、张量形状等

**🚧 简化的功能**：
- LogicalData：仅ID追踪，无实际缓冲区管理
- 依赖解析：基础计数，无复杂图分析
- 数据传输：模拟，无实际GPU操作

**📋 未来扩展**：
- 完整的GPU缓冲区管理
- 自动数据传输
- 复杂依赖图解析
- 多流并行执行

### 设计哲学

#### 1. 声明式编程范式

  - **传统 Mojo 方式**：开发者需要显式地创建 Host/Device 缓冲区、调用 `enqueue_copy` 进行数据传输、手动处理任务间的同步。代码冗长且容易出错。
  - **MOJO STF 方式**：开发者只需声明任务之间的数据依赖关系（读、写），框架会自动处理底层的数据传输、依赖等待和任务调度。

```mojo
# 传统 Mojo 方式
fn traditional_pipeline():
    var ctx = DeviceContext()
    # ... 手动创建 host/device buffer ...
    var h_in, d_in = ...
    var h_tmp, d_tmp = ...
    var h_out, d_out = ...

    # 手动传输
    ctx.enqueue_copy(dst_buf=d_in, src_buf=h_in)
    
    # 手动执行与同步
    # (需要复杂的事件或多 stream 管理来并行)
    ctx.enqueue_function[preprocess](d_in, d_tmp, grid_dim=..., block_dim=...)
    ctx.synchronize() # 阻塞等待

    ctx.enqueue_function[postprocess](d_tmp, d_out, grid_dim=..., block_dim=...)
    ctx.synchronize() # 再次阻塞等待

    ctx.enqueue_copy(dst_buf=h_out, src_buf=d_out)
    ctx.synchronize()

# MOJO STF 理想方式 (目标设计)
fn stf_pipeline_ideal():
    var ctx = MojoContext()

    # 1. 声明逻辑数据
    var input_data = ctx.logical_data(h_input)
    var temp_data = ctx.logical_data(shape=(N,), dtype=float_dtype)
    var output_data = ctx.logical_data(shape=(N,), dtype=float_dtype)

    # 2. 声明任务流 (任务被立即调度)
    # 任务1：在 GPU 0 上执行 preprocess
    ctx.task(input_data.read(), temp_data.write()) \
       .on(DevicePlace(0)) \
       .exec(preprocess)

    # 任务2：在 GPU 0 上执行 postprocess
    # STF 自动推断此任务依赖任务1，并插入同步
    ctx.task(temp_data.read(), output_data.write()) \
       .on(DevicePlace(0)) \
       .exec(postprocess)

    # (可选) 等待所有已提交任务完成
    ctx.synchronize()

# MOJO STF 当前实现方式
fn stf_pipeline_current():
    var ctx = SimpleMojoContext("cuda", 0)

    # 1. 创建逻辑数据ID
    var input_id = ctx.create_logical_data_1d(1024)
    var temp_id = ctx.create_logical_data_1d(1024)
    var output_id = ctx.create_logical_data_1d(1024)

    # 2. 创建和配置任务
    var preprocess_task = ctx.create_host_task(1)  # 1个依赖
    preprocess_task.mark_function_bound()

    var compute_task = ctx.create_device_task(0, 2)  # 2个依赖
    compute_task.mark_function_bound()

    var postprocess_task = ctx.create_host_task(1)  # 1个依赖
    postprocess_task.mark_function_bound()

    # 3. 调度和执行
    ctx.schedule_task(preprocess_task)
    ctx.schedule_task(compute_task)
    ctx.schedule_task(postprocess_task)

    ctx.execute_task(preprocess_task)
    ctx.execute_task(compute_task)
    ctx.execute_task(postprocess_task)

    ctx.synchronize()
```

#### 2. 关注点分离

  - **算法逻辑**：开发者专注于实现计算核心，无论是 GPU 上的 `fn` 还是 Host 上的 `fn`。
  - **系统管理**：框架在后台处理内存管理、数据一致性、依赖分析、事件同步和资源调度。

### 当前实现架构

#### 1. SimpleMojoContext：简化的执行环境

当前实现的 `SimpleMojoContext` 是一个简化版本，专注于验证核心概念：

```mojo
struct SimpleMojoContext(Copyable, Movable):
    var _device_api: String          # 设备API类型 ("cpu", "cuda")
    var _device_id: Int              # 设备ID
    var _num_active_tasks: Int       # 活跃任务计数
    var _num_logical_data: Int       # 逻辑数据计数
    var _next_task_id: Int           # 下一个任务ID

    # 工厂方法
    fn create_logical_data_1d(mut self, size: Int) -> Int
    fn create_task(mut self, num_dependencies: Int = 0) -> SimpleTask
    fn create_host_task(mut self, num_dependencies: Int = 0) -> SimpleTask
    fn create_device_task(mut self, device_id: Int, num_dependencies: Int = 0) -> SimpleTask

    # 调度方法
    fn schedule_task(mut self, task: SimpleTask) -> Bool
    fn execute_task(mut self, mut task: SimpleTask) -> Bool
    fn synchronize(self)
```

**当前功能**：
- ✅ 基础上下文管理
- ✅ 任务工厂方法
- ✅ 简化的调度逻辑
- ✅ 执行模拟
- 🚧 LogicalData ID管理（无实际缓冲区）
- ❌ 复杂依赖解析
- ❌ 自动数据传输

#### 2. SimpleTask：基础任务抽象

```mojo
struct SimpleTask(Copyable, Movable):
    var _unique_id: Int              # 唯一任务ID
    var _num_dependencies: Int       # 依赖数量（简化）
    var _has_execution_place: Bool   # 是否设置执行位置
    var _execution_place: ExecPlace  # 执行位置
    var _state: TaskState           # 任务状态
    var _has_function: Bool         # 是否绑定函数

    # 配置方法
    fn set_execution_place(mut self, execution_place: ExecPlace)
    fn mark_function_bound(mut self)
    fn set_state(mut self, new_state: TaskState)
```

**当前功能**：
- ✅ 任务状态管理
- ✅ 执行位置配置
- ✅ 基础依赖计数
- ✅ 函数绑定标记
- ❌ 复杂依赖图
- ❌ 实际函数执行

#### 3. 理想设计：MojoContext（目标架构）

`MojoContext` 是 STF 系统的控制中心和任务分发器。它封装了 Mojo 原生的 `DeviceContext`，并采用**立即执行**模型。

**设计思想**:

  - **单设备多流架构**: `MojoContext` 内部维护一个主要的 `DeviceContext` 和多个执行流（Stream），用于并发执行无依赖的任务。这种设计更容易实现且资源管理简单。
  - **流池管理**: 系统维护一个流池，根据任务依赖关系动态分配可用的流，实现最大程度的并行执行。
  - **依赖追踪**: `MojoContext` 为每一个 `LogicalData` 对象追踪其"最后写入者"任务和对应的执行流。这个信息是实现自动依赖管理的关键。
  - **立即分发**: 当一个 `Task` 被 `exec` 时，`MojoContext` 立即检查其依赖，选择合适的执行流，生成必要的同步事件，并将任务及其数据传输操作提交到底层的执行队列中。

```mojo
struct MojoContext:
    var _native_ctx: DeviceContext
    var _cpu_ctx: DeviceContext
    # 简化的依赖追踪: a map from LogicalData's unique ID to the event of its last write.
    var _data_dependency_events: Dict[Int, TaskEvent] 
    var _stream_pool: List[Stream]  # 流池管理
    var _active_tasks: Dict[Int, Task]

    fn logical_data(...) -> LogicalData:
        # 工厂方法，创建 LogicalData 并注册到 Context
        ...

    fn task(...) -> Task:
        # 工厂方法，创建 Task 并传入依赖
        ...
    
    fn _schedule(task: Task):
        # 内部调度逻辑，在 task.exec() 时被调用
        # 1. 查找依赖任务的完成事件 (TaskEvent)
        # 2. 从流池中选择可用的执行流
        # 3. 在目标 stream 上 enqueue_wait_for(events)
        # 4. enqueue 自动数据传输 (copy)
        # 5. enqueue 任务函数
        # 6. 生成新的完成事件并更新 _data_dependency_events
        ...

    fn synchronize():
        # 调用底层 native_ctx.synchronize() 等待所有已入队任务完成
        self._native_ctx.synchronize()
        self._cpu_ctx.synchronize()
```

#### 2. LogicalData：位置透明的数据抽象

`LogicalData` 将数据的逻辑概念与物理存储（Host 或 Device）分离，实现了数据管理的自动化。

**核心特性**:

  - **位置透明性**: 用户只需关心数据的逻辑本身。框架自动决定何时、何地分配内存，以及何时在 Host 和 Device 之间拷贝数据。
  - **状态管理**: 每个 `LogicalData` 实例内部维护其当前状态，例如：
      - `location`: `Host`, `Device(id)` (数据最新副本的位置)
      - `is_dirty`: 标记数据是否在某个位置被修改。
  - **懒加载与自动传输**:
      - Device 内存只在第一次有任务需要在设备上使用它时才分配。
      - 当任务需要数据时，框架检查数据是否在目标位置。如果不在，会自动插入 `enqueue_copy` 操作。
  - **API 设计**:
      - `read()`: 声明一个只读依赖。允许多个任务并发读取。
      - `write()`: 声明一个写依赖。此任务将独占访问，并成为后续所有读/写任务的依赖。
      - `read_write()`: 声明一个读写依赖，语义上等同于写依赖。

```mojo
struct LogicalData[dtype: DType]:
    var _unique_id: Int
    var _shape: TensorShape
    var _current_location: DataLocation
    var _buffer_info: BufferInfo
    var _is_initialized: Bool
    var _context_ref: UnsafePointer[MojoContext]

    fn read(place: ExecPlace = HostPlace()) -> TaskDependency: ...
    fn write(place: ExecPlace = HostPlace()) -> TaskDependency: ...
```

#### 3. Task：异构计算抽象

`Task` 是 STF 的基本执行单元，它将**计算意图**与**执行细节**和**位置**分离。

**关键设计**:

  - **依赖驱动**: 任务通过 `LogicalData` 的依赖 (`.read()`, `.write()`) 声明其在执行图中的位置。
  - **执行位置 (Execution Placement)**: 通过 `.on(exec_place)` 方法，可以指定任务在 Host 或特定 Device 上执行。这使得混合 CPU 和 GPU 的复杂流水线变得简单。
    ```mojo
    alias ExecPlace = Variant[HostPlace, DevicePlace]
    ```
  - **执行体绑定**: `.exec(fn)` 方法将一个具体的 Mojo `fn` (GPU kernel 或 Host function) 绑定到任务上，并立即触发 `MojoContext` 的调度逻辑。
  - **类型安全的参数传递**: 框架确保传递给 `exec` 中 `fn` 的参数类型与声明的依赖（只读/可写）相匹配。

```mojo
# Kernel function
fn my_kernel(
    lhs: UnsafePointer[Float32],      # from a .read() dependency
    rhs: UnsafePointer[Float32],      # from a .read() dependency
    out: UnsafePointer[Float32],      # from a .write() dependency
    length: Int
):
    # ... kernel logic ...

# Task creation
ctx.task(A.read(), B.read(), C.write()) \
   .on(DevicePlace(0)) \
   .exec(my_kernel)
```

### 协同工作机制：立即提交流程

当用户调用 `.exec()` 时，一次完整的任务提交流程被触发：

1.  **用户调用**: `ctx.task(A.read(), B.write()).on(HostPlace()).exec(my_host_fn)`
2.  **创建任务**: `MojoContext` 创建一个 `Task` 对象，包含对 `A` 的只读依赖和对 `B` 的写依赖，以及在 Host 上执行的目标。
3.  **触发调度**: `.exec()` 调用 `MojoContext` 内部的 `_schedule(self)` 方法。
4.  **依赖解析**:
      - `_schedule` 遍历任务的依赖（`A` 和 `B`）。
      - 它查询 `_data_dependency_events` 表，找到 `A` 和 `B` 的最后写入者任务所产生的 `TaskEvent`。这些构成了本任务的前置依赖事件列表。
5.  **流分配**:
      - 根据任务的执行位置和依赖关系，从流池中选择合适的执行流。
      - 对于无依赖冲突的任务，可以分配到不同的流实现并行执行。
6.  **数据准备**:
      - 任务在 Host 执行，`_schedule` 检查 `A` 和 `B` 的数据位置。
      - 假设 `A` 的最新数据在 Device 0 上，`_schedule` 会自动提交一个 `enqueue_copy(dst=host_buffer_A, src=device_buffer_A)`，并确保这个拷贝操作在前置依赖事件完成后才执行。
7.  **任务入队**:
      - 对于 Host 任务，`_schedule` 会在一个后台线程池中执行 `my_host_fn`，并确保它在数据准备好之后才运行。
      - 对于 Device 任务，`_schedule` 会选择一个可用的执行流，并依次提交：
          - `enqueue_wait_for(prerequisite_events)`
          - `enqueue_copy(...)` (如果需要)
          - `enqueue_function_unchecked(...)`
8.  **更新依赖状态**:
      - 任务执行后，会生成一个新的完成事件 `new_event`。
      - 因为此任务写入了 `B`，所以 `MojoContext` 更新其内部状态：`_data_dependency_events[B.id] = new_event`。
      - `LogicalData` `B` 的状态也被更新，`_current_location` 变为相应的位置。

### 设计优势

1.  **开发效率**: 大幅减少 GPU 编程的样板代码。开发者可以像写串行代码一样思考，框架负责并行化和同步。
2.  **性能优化**:
      - **自动并行**: 无依赖的任务可以自动在不同的"流"上并行执行。
      - **最小化传输**: `LogicalData` 的状态管理确保只有在必要时才进行数据传输。
      - **依赖优化**: 基于事件的精确同步避免了粗粒度的 `ctx.synchronize()` 阻塞。
3.  **可维护性**: 算法逻辑与系统管理逻辑分离，代码更清晰、更易于理解和修改。
4.  **异构计算简化**: `.on(exec_place)` 提供了简单而强大的接口，用于编排跨 CPU 和多个 GPU 的复杂工作流。

### 实现决策更新

#### 并发模型选择

**决策**: 采用**单设备多流**的并发模型

**理由**:
1. **实现简单**: 单个 `DeviceContext` 配合多个执行流比管理多个 `DeviceContext` 更容易实现
2. **资源管理**: 避免了多设备间复杂的内存管理和同步问题
3. **符合实际**: 大多数 GPU 编程场景都是在单个设备上通过多流实现并行
4. **扩展性**: 未来可以通过多个 `MojoContext` 实例支持多设备场景

**实现方式**:
- `MojoContext` 内部维护一个流池 `_stream_pool`
- 根据任务依赖关系动态分配流
- 使用事件机制在不同流之间同步

#### 错误处理策略

**决策**: 使用 Mojo 的 `raises` 机制

**理由**:
1. **语言一致性**: 与 Mojo 标准库保持一致
2. **类型安全**: 编译时强制错误处理
3. **性能**: 避免 Result 类型的运行时开销

### 实际应用示例

#### 混合 Host/Device 流水线

```mojo
fn cpu_preprocess(raw_input, preprocessed_output):
    # Mojo code running on CPU
    ...

fn gpu_compute_kernel(preprocessed_data, gpu_result):
    # Mojo kernel code running on GPU
    ...

fn cpu_postprocess(gpu_result, final_result):
    # Mojo code running on CPU
    ...

fn hybrid_pipeline():
    var ctx = MojoContext()
    
    # Initial data on host
    var raw = ctx.logical_data(host_raw_data) 
    
    # Intermediate and final data (shape-based creation)
    var preprocessed = ctx.logical_data[DType.float32](1024)
    var gpu_out = ctx.logical_data[DType.float32](1024)
    var final = ctx.logical_data[DType.float32](1024)

    # Task 1: CPU pre-processing
    ctx.task(raw.read(), preprocessed.write()) \
       .on(HostPlace()) \
       .exec(cpu_preprocess)

    # Task 2: GPU computation
    # Automatically waits for Task 1, and data for `preprocessed` is copied to device
    ctx.task(preprocessed.read(DevicePlace(0)), gpu_out.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec(gpu_compute_kernel)

    # Task 3: CPU post-processing
    # Automatically waits for Task 2, and data for `gpu_out` is copied back to host
    ctx.task(gpu_out.read(), final.write()) \
       .on(HostPlace()) \
       .exec(cpu_postprocess)

    ctx.synchronize() # Wait for the entire pipeline to complete
    print("Final result:", final.get_host_data())
```

### 实现对比表

| 功能组件 | 理想设计 | 当前实现 | 实现状态 |
|---------|---------|---------|---------|
| **上下文管理** | MojoContext | SimpleMojoContext | ✅ 基础功能完成 |
| **任务抽象** | Task + 链式API | SimpleTask + 方法调用 | ✅ 核心功能完成 |
| **数据抽象** | LogicalData + 自动传输 | ID追踪 + 位置抽象 | 🚧 基础结构完成 |
| **依赖管理** | 复杂依赖图解析 | 简单依赖计数 | 🚧 简化版本 |
| **执行调度** | 自动流分配 + 事件同步 | 基础调度模拟 | 🚧 概念验证 |
| **内存管理** | 自动缓冲区 + 懒加载 | 模拟管理 | ❌ 待实现 |
| **GPU操作** | 真实GPU调用 | 执行模拟 | ❌ 待实现 |
| **错误处理** | 完整异常系统 | 基础错误检查 | 🚧 部分实现 |

### 当前实现的价值

虽然当前实现是简化版本，但它成功验证了：

1. **核心概念可行性** ✅
   - 声明式任务编程模型
   - 位置透明的数据抽象
   - 执行位置指定机制

2. **架构设计合理性** ✅
   - 三层抽象架构清晰
   - 组件间接口设计良好
   - 扩展性考虑充分

3. **Mojo语言适配性** ✅
   - 类型系统兼容
   - 内存管理模式适配
   - 性能特性保持

4. **开发体验改进** ✅
   - 相比传统GPU编程显著简化
   - 代码可读性和维护性提升
   - 错误调试更容易

### 下一步发展路径

1. **短期目标**（1-2个月）
   - 实现真实的GPU缓冲区管理
   - 添加基础的数据传输功能
   - 完善错误处理机制

2. **中期目标**（3-6个月）
   - 实现复杂依赖图解析
   - 添加多流并行执行
   - 性能优化和基准测试

3. **长期目标**（6-12个月）
   - 完整的生产级实现
   - 与Mojo生态系统集成
   - 社区推广和文档完善

### 总结

MOJO STF 项目成功地将 CUDA STF 的核心思想适配到了 Mojo 语言环境中。当前的原型实现虽然功能简化，但充分验证了设计理念的可行性和价值。通过**声明式任务**、**位置透明数据**和**简化调度**，为 Mojo 开发者提供了一个强大的异构编程抽象层。

采用渐进式实现策略，我们建立了坚实的基础架构，为未来的功能扩展和性能优化奠定了良好基础。这个项目不仅展示了 Mojo 语言在系统级编程方面的潜力，也为异构计算编程模式的创新提供了有价值的探索。
