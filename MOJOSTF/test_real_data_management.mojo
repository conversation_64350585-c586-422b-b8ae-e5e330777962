#!/usr/bin/env mojo

"""
Test REAL Data Management with User-Provided Data.

This test demonstrates the correct STF data management flow:
1. User provides initial host data
2. STF takes ownership of the data
3. STF manages data transfers and buffer reuse
4. Position-transparent computation

Usage:
    cd /root/stf_exp/MOJOSTF
    pixi shell
    mojo test_real_data_management.mojo
"""

from layout import Layout
from gpu import global_idx
from memory import UnsafePointer
from src import (
    STFContext, LogicalTensor, TensorDependency,
    create_stf_context, 
    tensor_scale_f32_kernel,
    HostPlace, DevicePlace, ExecPlace, DType
)


fn create_test_data(size: Int) -> UnsafePointer[Scalar[DType.float32]]:
    """Create test data on host."""
    var data = UnsafePointer[Scalar[DType.float32]].alloc(size)
    
    # Initialize with test values
    for i in range(size):
        data[i] = Scalar[DType.float32](i + 1.0)
    
    print("📊 [HOST] Created test data with", size, "elements")
    print("    First few values:", data[0], data[1], data[2], "...")
    
    return data


fn test_real_data_lifecycle() raises:
    """Test complete data lifecycle starting from user data."""
    print("=== Testing REAL Data Lifecycle ===")
    
    var ctx = create_stf_context(num_streams=3)
    
    # Step 1: User creates data on host
    var data_size = 1024
    var host_data = create_test_data(data_size)
    
    # Step 2: Create LogicalTensor from user data (STF takes ownership)
    alias layout = Layout([32, 32], [32, 1])
    var tensor = ctx.logical_tensor_from_data[DType.float32, layout](host_data, data_size)
    
    print("\n🎯 STF now owns the data - user can forget about memory management!")
    
    # Step 3: Position-transparent computation
    print("\n📝 Task 1: First computation on context 0")
    ctx.task(tensor.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    print("\n📝 Task 2: Second computation on SAME context 0 (should reuse buffer)")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    print("\n📝 Task 3: Computation on DIFFERENT context 1 (should transfer data)")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    print("\n📝 Task 4: Back to context 0 (should reuse existing buffer)")
    ctx.task(tensor.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.synchronize()
    print("✅ Real data lifecycle test completed!")


fn test_multiple_tensors_with_data() raises:
    """Test multiple tensors each with their own data."""
    print("\n=== Testing Multiple Tensors with Real Data ===")
    
    var ctx = create_stf_context(num_streams=2)
    
    # Create multiple tensors with different data
    var size_a = 512
    var size_b = 1024
    var size_c = 2048
    
    var data_a = create_test_data(size_a)
    var data_b = create_test_data(size_b)
    var data_c = create_test_data(size_c)
    
    alias layout_a = Layout([16, 32], [32, 1])
    alias layout_b = Layout([32, 32], [32, 1])
    alias layout_c = Layout([64, 32], [32, 1])
    
    var tensor_a = ctx.logical_tensor_from_data[DType.float32, layout_a](data_a, size_a)
    var tensor_b = ctx.logical_tensor_from_data[DType.float32, layout_b](data_b, size_b)
    var tensor_c = ctx.logical_tensor_from_data[DType.float32, layout_c](data_c, size_c)
    
    print("\n🎯 Created 3 tensors with independent data")
    
    # Each tensor should have independent buffer management
    print("\n📝 Operations on different tensors")
    ctx.task(tensor_a.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.task(tensor_b.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.task(tensor_c.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Reuse buffers for same tensors
    print("\n📝 Reusing buffers for same tensors")
    ctx.task(tensor_a.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.task(tensor_b.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.synchronize()
    print("✅ Multiple tensors test completed!")


fn test_cross_context_data_transfer() raises:
    """Test data transfer between contexts."""
    print("\n=== Testing Cross-Context Data Transfer ===")
    
    var ctx = create_stf_context(num_streams=4)
    
    # Create tensor with data
    var data_size = 2048
    var host_data = create_test_data(data_size)
    
    alias layout = Layout([64, 32], [32, 1])
    var tensor = ctx.logical_tensor_from_data[DType.float32, layout](host_data, data_size)
    
    print("\n🎯 Testing data movement across contexts")
    
    # Write on context 0
    print("\n📝 Phase 1: Write data on context 0")
    ctx.task(tensor.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Read on context 1 (should transfer from context 0)
    print("\n📝 Phase 2: Read data on context 1 (should transfer)")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Read on context 2 (should transfer from context 0 or 1)
    print("\n📝 Phase 3: Read data on context 2 (should transfer)")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Write on context 1 (should invalidate other contexts)
    print("\n📝 Phase 4: Write data on context 1 (should invalidate others)")
    ctx.task(tensor.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    # Read on context 0 (should transfer from context 1)
    print("\n📝 Phase 5: Read data on context 0 (should transfer from context 1)")
    ctx.task(tensor.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[tensor_scale_f32_kernel]()
    
    ctx.synchronize()
    print("✅ Cross-context data transfer test completed!")


fn main() raises:
    """Run all real data management tests."""
    print("MOJO STF REAL Data Management Test")
    print("==================================")
    print("Testing STF with user-provided data and real data management")
    
    test_real_data_lifecycle()
    test_multiple_tensors_with_data()
    test_cross_context_data_transfer()
    
    # print("\n🎉 ALL REAL DATA MANAGEMENT TESTS COMPLETED! 🎉")
    # print("\n📊 Expected Behavior Analysis:")
    # print("✅ Buffer Reuse:")
    # print("   - Should see 'REUSING existing buffer' for same tensor/context")
    # print("   - Should see 'Creating NEW buffer' only once per tensor/context")
    # print("")
    # print("✅ Data Transfer:")
    # print("   - Should see 'Data already valid' when no transfer needed")
    # print("   - Should see 'Copying data from context X to Y' for transfers")
    # print("   - Should see 'Host-to-device copy' for initial transfers")
    # print("")
    # print("✅ Real Data Management:")
    # print("   - Tensors created from user data (not empty)")
    # print("   - STF takes ownership of data lifecycle")
    # print("   - Position-transparent computation")
    # print("   - Automatic data movement and buffer reuse")
    # print("")
    # print("🎯 This demonstrates REAL STF data management!")
