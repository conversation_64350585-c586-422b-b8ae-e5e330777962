# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, MOJO STF Project. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #

"""
Core type definitions for MOJO STF (Sequential Task Flow) system.

This module defines the fundamental types and enums used throughout the STF system,
including execution places, data locations, and dependency types.
"""

from collections import Dict, Optional
from utils import Variant
from gpu.host import Devi<PERSON><PERSON>ontex<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HostBuffer
from memory import UnsafePointer


# ===----------------------------------------------------------------------=== #
# Execution Place Abstractions
# ===----------------------------------------------------------------------=== #

@register_passable("trivial")
struct HostPlace:
    """Represents execution on the host CPU."""

    fn __init__(out self):
        pass


@register_passable("trivial") 
struct DevicePlace:
    """Represents execution on a specific GPU device."""
    var device_id: Int
    
    fn __init__(out self, device_id: Int):
        self.device_id = device_id


alias ExecPlace = Variant[HostPlace, DevicePlace]


# ===----------------------------------------------------------------------=== #
# Data Location and State Management
# ===----------------------------------------------------------------------=== #

@register_passable("trivial")
struct DataLocation:
    """Tracks where the most recent version of data is located."""
    var _location_type: Int  # 0=Host, 1=Device
    var _device_id: Int      # Only valid when _location_type == 1
    
    fn __init__(out self):
        """Initialize as Host location."""
        self._location_type = 0
        self._device_id = -1
    
    @staticmethod
    fn host() -> Self:
        """Create a host data location."""
        return DataLocation()
    
    @staticmethod
    fn device(device_id: Int) -> Self:
        """Create a device data location."""
        var loc = DataLocation()
        loc._location_type = 1
        loc._device_id = device_id
        return loc
    
    fn is_host(self) -> Bool:
        """Check if data is on host."""
        return self._location_type == 0
    
    fn is_device(self) -> Bool:
        """Check if data is on device."""
        return self._location_type == 1
    
    fn get_device_id(self) -> Int:
        """Get device ID (only valid if is_device() returns True)."""
        return self._device_id


# ===----------------------------------------------------------------------=== #
# Task Dependency Types
# ===----------------------------------------------------------------------=== #

@register_passable("trivial")
struct DependencyType:
    """Represents the type of dependency a task has on data."""
    var _type: Int  # 0=Read, 1=Write, 2=ReadWrite
    
    fn __init__(out self, dep_type: Int):
        self._type = dep_type
    
    @staticmethod
    fn read() -> Self:
        """Create a read dependency."""
        return DependencyType(0)
    
    @staticmethod
    fn write() -> Self:
        """Create a write dependency."""
        return DependencyType(1)
    
    @staticmethod
    fn read_write() -> Self:
        """Create a read-write dependency."""
        return DependencyType(2)
    
    fn is_read(self) -> Bool:
        """Check if this is a read dependency."""
        return self._type == 0
    
    fn is_write(self) -> Bool:
        """Check if this is a write dependency."""
        return self._type == 1
    
    fn is_read_write(self) -> Bool:
        """Check if this is a read-write dependency."""
        return self._type == 2
    
    fn has_write_access(self) -> Bool:
        """Check if this dependency includes write access."""
        return self._type == 1 or self._type == 2


# ===----------------------------------------------------------------------=== #
# Unique ID Generation
# ===----------------------------------------------------------------------=== #

struct UniqueIDGenerator:
    """Thread-safe unique ID generator for LogicalData and Task objects."""
    var _next_id: Int
    
    fn __init__(out self):
        self._next_id = 1
    
    fn next_id(mut self) -> Int:
        """Generate the next unique ID."""
        var id = self._next_id
        self._next_id += 1
        return id


# Simple counter for unique IDs
var _id_counter: Int = 0

fn get_unique_id() -> Int:
    """Get a globally unique ID."""
    _id_counter += 1
    return _id_counter


# ===----------------------------------------------------------------------=== #
# Shape and Type Information
# ===----------------------------------------------------------------------=== #

@register_passable("trivial")
struct TensorShape:
    """Represents the shape of a tensor with up to 4 dimensions."""
    var _dim0: Int
    var _dim1: Int
    var _dim2: Int
    var _dim3: Int
    var _rank: Int

    fn __init__(out self, dim0: Int):
        """Initialize with 1D shape."""
        self._dim0 = dim0
        self._dim1 = 1
        self._dim2 = 1
        self._dim3 = 1
        self._rank = 1

    fn __init__(out self, dim0: Int, dim1: Int):
        """Initialize with 2D shape."""
        self._dim0 = dim0
        self._dim1 = dim1
        self._dim2 = 1
        self._dim3 = 1
        self._rank = 2

    fn __init__(out self, dim0: Int, dim1: Int, dim2: Int):
        """Initialize with 3D shape."""
        self._dim0 = dim0
        self._dim1 = dim1
        self._dim2 = dim2
        self._dim3 = 1
        self._rank = 3

    fn __init__(out self, dim0: Int, dim1: Int, dim2: Int, dim3: Int):
        """Initialize with 4D shape."""
        self._dim0 = dim0
        self._dim1 = dim1
        self._dim2 = dim2
        self._dim3 = dim3
        self._rank = 4

    fn __getitem__(self, idx: Int) -> Int:
        """Get dimension at index."""
        if idx == 0:
            return self._dim0
        elif idx == 1:
            return self._dim1
        elif idx == 2:
            return self._dim2
        elif idx == 3:
            return self._dim3
        else:
            return 1

    fn rank(self) -> Int:
        """Get the number of dimensions."""
        return self._rank

    fn total_elements(self) -> Int:
        """Calculate total number of elements."""
        var total = 1
        for i in range(self._rank):
            total *= self[i]
        return total


# ===----------------------------------------------------------------------=== #
# Buffer Management Types (Simplified for now)
# ===----------------------------------------------------------------------=== #

# Note: BufferInfo will be implemented in LogicalData directly
# to avoid complex generic type issues for now


# ===----------------------------------------------------------------------=== #
# Error Types
# ===----------------------------------------------------------------------=== #

@register_passable("trivial")
struct STFError:
    """Error types for STF operations."""
    var _error_code: Int

    fn __init__(out self, code: Int):
        self._error_code = code

    @staticmethod
    fn invalid_dependency() -> Self:
        """Error for invalid dependency specification."""
        return STFError(1)

    @staticmethod
    fn buffer_not_found() -> Self:
        """Error when required buffer is not found."""
        return STFError(2)

    @staticmethod
    fn device_not_available() -> Self:
        """Error when specified device is not available."""
        return STFError(3)

    fn code(self) -> Int:
        """Get error code."""
        return self._error_code

    fn message(self) -> String:
        """Get error message."""
        if self._error_code == 1:
            return "Invalid dependency specification"
        elif self._error_code == 2:
            return "Required buffer not found"
        elif self._error_code == 3:
            return "Specified device not available"
        else:
            return "Unknown error"
