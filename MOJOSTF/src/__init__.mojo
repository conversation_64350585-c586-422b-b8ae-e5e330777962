"""
MOJO STF (Supercomputing Task Framework) - Unified Implementation

This package provides a complete, unified STF implementation for Mojo that combines
all the best features from our development iterations:

✅ Core STF Principles:
- Position-transparent data abstractions with LayoutTensor support
- Automatic dependency analysis and synchronization
- Asynchronous task execution with real GPU kernel integration
- Stream pool management for parallel execution

✅ Advanced Features:
- Type-safe tensor operations with compile-time layout verification
- Mixed precision support (FP32, FP16, BF16)
- Cross-context synchronization using enqueue_wait_for
- Read-after-write (RAW) and write-after-read (WAR) dependency handling

Unified Usage:
```mojo
from src import create_stf_context, DevicePlace, tensor_scale_f32_kernel

var ctx = create_stf_context()
var tensor = ctx.logical_tensor[DType.float32, Layout([32, 32], [32, 1])]()

ctx.task(tensor.write(DevicePlace(0))) \
   .on(DevicePlace(0)) \
   .exec[tensor_scale_f32_kernel]()

ctx.synchronize()
```

This achieves the complete STF vision with a single, maintainable implementation.
"""

# Core types and utilities
from .types import (
    # Execution places
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Exec<PERSON>lace,

    # Dependency types
    DependencyType,

    # Utility functions
    get_unique_id
)

# Logging system
from .logger import (
    STFLogger,
    LogLevel,
    STFTaskLogger,
    STFDataLogger,
    STFSyncLogger,
    log_debug,
    log_info,
    log_warning,
    log_error,
    task_log,
    data_log,
    sync_log
)

# Unified STF implementation - The single source of truth
from .stf import (
    # Main STF context
    STFContext,
    create_stf_context,

    # Data abstractions with LayoutTensor support
    LogicalTensor,
    TensorDependency,

    # Task system with real async execution
    TaskBuilder,
    Task,

    # Infrastructure
    StreamPool,
    DataLocation,
    TaskInfo,

    # GPU kernels for different precisions
    tensor_add_f32_kernel,
    tensor_scale_f32_kernel,
    tensor_scale_f16_kernel,
    tensor_scale_bf16_kernel
)

# All modules are imported above and available for use
# Mojo doesn't support __all__ yet, so all imports are available by default
