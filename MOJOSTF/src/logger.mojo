# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, MOJO STF Project. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #

"""
STF Logging System.

Provides configurable logging levels and proper debug/info/warning/error output
instead of direct print statements throughout the codebase.

Usage:
    from src.logger import STFLogger, LogLevel
    
    # Configure logging level
    STFLogger.set_level(LogLevel.INFO)
    
    # Use structured logging
    STFLogger.debug("Task", task_id, "analyzing dependencies")
    STFLogger.info("STF context created with", num_streams, "streams")
    STFLogger.warning("Buffer not found for tensor", tensor_id)
    STFLogger.error("Failed to submit task", task_id, "to context", context_id)
"""

from sys.param_env import env_get_string


# ===----------------------------------------------------------------------=== #
# Log Level Configuration
# ===----------------------------------------------------------------------=== #

@register_passable("trivial")
struct LogLevel:
    """Log level enumeration."""
    var _level: Int
    
    fn __init__(out self, level: Int):
        self._level = level
    
    @staticmethod
    fn DEBUG() -> Self:
        """Debug level - most verbose."""
        return LogLevel(0)
    
    @staticmethod
    fn INFO() -> Self:
        """Info level - general information."""
        return LogLevel(1)
    
    @staticmethod
    fn WARNING() -> Self:
        """Warning level - potential issues."""
        return LogLevel(2)
    
    @staticmethod
    fn ERROR() -> Self:
        """Error level - serious problems."""
        return LogLevel(3)
    
    @staticmethod
    fn SILENT() -> Self:
        """Silent level - no output."""
        return LogLevel(4)
    
    fn value(self) -> Int:
        return self._level
    
    fn __eq__(self, other: Self) -> Bool:
        return self._level == other._level
    
    fn __ge__(self, other: Self) -> Bool:
        return self._level >= other._level


# ===----------------------------------------------------------------------=== #
# STF Logger Implementation
# ===----------------------------------------------------------------------=== #

struct STFLogger:
    """
    STF logging system with configurable levels.

    Supports environment variable configuration:
    - STF_LOG_LEVEL=DEBUG|INFO|WARNING|ERROR|SILENT
    - Default: INFO
    """

    @staticmethod
    fn _get_log_level() -> LogLevel:
        """Get log level from environment or default to INFO."""
        alias log_level_str = env_get_string["STF_LOG_LEVEL", "INFO"]()

        @parameter
        if log_level_str == "DEBUG":
            return LogLevel.DEBUG()
        elif log_level_str == "WARNING":
            return LogLevel.WARNING()
        elif log_level_str == "ERROR":
            return LogLevel.ERROR()
        elif log_level_str == "SILENT":
            return LogLevel.SILENT()
        else:  # Default to INFO
            return LogLevel.INFO()

    @staticmethod
    fn _should_log(level: LogLevel) -> Bool:
        """Check if message should be logged based on current level."""
        var current_level = Self._get_log_level()
        return level >= current_level

    @staticmethod
    fn debug(msg: String):
        """Log debug message."""
        @parameter
        if Self._should_log(LogLevel.DEBUG()):
            print("[DEBUG] [STF]", msg)

    @staticmethod
    fn info(msg: String):
        """Log info message."""
        @parameter
        if Self._should_log(LogLevel.INFO()):
            print("[INFO] [STF]", msg)

    @staticmethod
    fn warning(msg: String):
        """Log warning message."""
        @parameter
        if Self._should_log(LogLevel.WARNING()):
            print("[WARNING] [STF]", msg)

    @staticmethod
    fn error(msg: String):
        """Log error message."""
        @parameter
        if Self._should_log(LogLevel.ERROR()):
            print("[ERROR] [STF]", msg)


# ===----------------------------------------------------------------------=== #
# Specialized STF Logging Functions
# ===----------------------------------------------------------------------=== #

struct STFTaskLogger:
    """Specialized logger for task operations."""

    @staticmethod
    fn task_created(task_id: Int, tensor_id: Int):
        """Log task creation."""
        STFLogger.debug("📋 [TASK] Created task " + String(task_id) + " with dependency on tensor " + String(tensor_id))

    @staticmethod
    fn task_submitted(task_id: Int, context_id: Int):
        """Log task submission."""
        STFLogger.info("🚀 [EXEC] Task " + String(task_id) + " submitted to context " + String(context_id))

    @staticmethod
    fn task_dependency_wait(task_id: Int, wait_context_id: Int):
        """Log task dependency wait."""
        STFLogger.debug("📋 [SYNC] Task " + String(task_id) + " waiting for context " + String(wait_context_id))


struct STFDataLogger:
    """Specialized logger for data operations."""

    @staticmethod
    fn tensor_created(tensor_id: Int, size: Int):
        """Log tensor creation."""
        STFLogger.debug("📊 [DATA] Created logical tensor " + String(tensor_id) + " size = " + String(size) + " elements")

    @staticmethod
    fn buffer_created(tensor_id: Int, context_id: Int):
        """Log buffer creation."""
        STFLogger.debug("💾 [BUFFER] Creating NEW buffer for tensor " + String(tensor_id) + " on context " + String(context_id))

    @staticmethod
    fn buffer_reused(tensor_id: Int, context_id: Int):
        """Log buffer reuse."""
        STFLogger.debug("🔄 [BUFFER] REUSING existing buffer for tensor " + String(tensor_id) + " on context " + String(context_id))

    @staticmethod
    fn data_transfer(source: String, dest: String):
        """Log data transfer."""
        STFLogger.debug("📤 [TRANSFER] Copying data from " + source + " to " + dest)


struct STFSyncLogger:
    """Specialized logger for synchronization operations."""
    
    @staticmethod
    fn sync_start():
        """Log synchronization start."""
        STFLogger.debug("🔄 [SYNC] Synchronizing all streams...")
    
    @staticmethod
    fn sync_complete():
        """Log synchronization completion."""
        STFLogger.info("✅ [SYNC] All operations completed")


# ===----------------------------------------------------------------------=== #
# Convenience Aliases
# ===----------------------------------------------------------------------=== #

# Short aliases for common usage
alias log_debug = STFLogger.debug
alias log_info = STFLogger.info
alias log_warning = STFLogger.warning
alias log_error = STFLogger.error

# Specialized loggers
alias task_log = STFTaskLogger
alias data_log = STFDataLogger
alias sync_log = STFSyncLogger
