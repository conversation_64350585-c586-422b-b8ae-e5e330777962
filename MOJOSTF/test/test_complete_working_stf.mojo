#!/usr/bin/env mojo

"""
Test the complete working STF implementation.

This test verifies that we have achieved the exact target use case:
    var input_data = ctx.logical_data[DType.float32](1024)
    ctx.task(input_data.read(), temp_data.write()).on(<PERSON><PERSON><PERSON>lace(0)).exec[kernel]()

Note: This test should be run from the MOJOSTF root directory:
    cd /root/stf_exp/MOJOSTF
    mojo test/test_complete_working_stf.mojo
"""

# Import using the src package - this works when run from project root
# The correct way is to run: cd /root/stf_exp/MOJOSTF && mojo test/test_complete_working_stf.mojo
from src.complete_stf import (
    STFContext, LogicalDataRef, TaskDependency, TaskBuilder, ExecutableTask,
    create_stf_context, preprocess_kernel, compute_kernel, postprocess_kernel,
    vector_add_kernel, matrix_multiply_kernel
)
from src.types import Host<PERSON>lace, <PERSON><PERSON><PERSON>lace, ExecPlace, DType


# ===----------------------------------------------------------------------=== #
# Test Functions
# ===----------------------------------------------------------------------=== #

fn test_target_use_case() raises:
    """Test the exact target use case from the design document."""
    print("=== Testing Target Use Case ===")

    var ctx = create_stf_context()

    # 🎯 TARGET USE CASE: This is exactly what we wanted to achieve!
    var input_data = ctx.logical_data[DType.float32](1024)
    var temp_data = ctx.logical_data[DType.float32](1024)
    var output_data = ctx.logical_data[DType.float32](1024)

    print("Created logical data:")
    print("  input_data: ID", input_data._data_id, "size", input_data._size)
    print("  temp_data: ID", temp_data._data_id, "size", temp_data._size)
    print("  output_data: ID", output_data._data_id, "size", output_data._size)

    # 🎯 TARGET USE CASE: Fluent task creation and execution!
    ctx.task(input_data.read(DevicePlace(0)), temp_data.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[preprocess_kernel]()

    ctx.task(temp_data.read(DevicePlace(0)), output_data.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[compute_kernel]()

    ctx.synchronize()

    print("✅ Target use case achieved successfully!")


fn test_different_data_types() raises:
    """Test STF with different data types."""
    print("\n=== Testing Different Data Types ===")

    var ctx = create_stf_context()
    
    # Test different data types
    var float32_data = ctx.logical_data[DType.float32](512)
    var float64_data = ctx.logical_data[DType.float64](256)
    var int32_data = ctx.logical_data[DType.int32](1024)
    var bfloat16_data = ctx.logical_data[DType.bfloat16](2048)
    
    print("Created different data types:")
    print("  float32:", float32_data._data_id)
    print("  float64:", float64_data._data_id)
    print("  int32:", int32_data._data_id)
    print("  bfloat16:", bfloat16_data._data_id)
    
    # Execute tasks on different data types
    ctx.task(float32_data.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[vector_add_kernel]()
    
    ctx.task(int32_data.read(HostPlace())) \
       .on(HostPlace()) \
       .exec[compute_kernel]()
    
    ctx.synchronize()
    print("✅ Different data types test completed!")


fn test_complex_pipeline() raises:
    """Test a complex multi-stage pipeline."""
    print("\n=== Testing Complex Pipeline ===")
    
    var ctx = create_stf_context()
    
    # Create data for complex pipeline
    var raw_input = ctx.logical_data[DType.float32](4096)
    var preprocessed = ctx.logical_data[DType.float32](4096)
    var computed = ctx.logical_data[DType.float32](4096)
    var postprocessed = ctx.logical_data[DType.float32](4096)
    var final_output = ctx.logical_data[DType.float32](4096)
    
    print("Created 5-stage pipeline data")
    
    # Stage 1: Host preprocessing
    print("Stage 1: Host preprocessing")
    ctx.task(raw_input.read(HostPlace())) \
       .on(HostPlace()) \
       .exec[preprocess_kernel]()
    
    # Stage 2: Device computation
    print("Stage 2: Device computation")
    ctx.task(raw_input.read(DevicePlace(0)), preprocessed.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[compute_kernel]()
    
    # Stage 3: Matrix operations
    print("Stage 3: Matrix operations")
    ctx.task(preprocessed.read(DevicePlace(0)), computed.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[matrix_multiply_kernel]()
    
    # Stage 4: Vector operations
    print("Stage 4: Vector operations")
    ctx.task(computed.read(DevicePlace(0)), postprocessed.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[vector_add_kernel]()
    
    # Stage 5: Host postprocessing
    print("Stage 5: Host postprocessing")
    ctx.task(postprocessed.read(HostPlace()), final_output.write(HostPlace())) \
       .on(HostPlace()) \
       .exec[postprocess_kernel]()
    
    ctx.synchronize()
    print("✅ Complex pipeline test completed!")


fn test_mixed_execution_places() raises:
    """Test mixed host and device execution."""
    print("\n=== Testing Mixed Execution Places ===")

    var ctx = create_stf_context()
    
    var data_a = ctx.logical_data[DType.float32](1024)
    var data_b = ctx.logical_data[DType.float32](1024)
    var data_c = ctx.logical_data[DType.float32](1024)
    
    # Host execution
    print("Host execution:")
    ctx.task(data_a.read(HostPlace())) \
       .on(HostPlace()) \
       .exec[compute_kernel]()
    
    # Device 0 execution
    print("Device 0 execution:")
    ctx.task(data_a.read(DevicePlace(0)), data_b.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[vector_add_kernel]()
    
    # Device 1 execution
    print("Device 1 execution:")
    ctx.task(data_b.read(DevicePlace(1)), data_c.write(DevicePlace(1))) \
       .on(DevicePlace(1)) \
       .exec[matrix_multiply_kernel]()
    
    # Back to host
    print("Back to host:")
    ctx.task(data_c.read(HostPlace())) \
       .on(HostPlace()) \
       .exec[postprocess_kernel]()
    
    ctx.synchronize()
    print("✅ Mixed execution places test completed!")


fn test_dependency_types() raises:
    """Test different dependency types."""
    print("\n=== Testing Dependency Types ===")

    var ctx = create_stf_context()
    
    var data = ctx.logical_data[DType.float32](2048)
    
    # Read dependency
    print("Testing read dependency:")
    ctx.task(data.read(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[compute_kernel]()
    
    # Write dependency
    print("Testing write dependency:")
    ctx.task(data.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[vector_add_kernel]()
    
    # Read-write dependency
    print("Testing read-write dependency:")
    ctx.task(data.read_write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[matrix_multiply_kernel]()
    
    ctx.synchronize()
    print("✅ Dependency types test completed!")


fn test_fluent_interface() raises:
    """Test the fluent interface design."""
    print("\n=== Testing Fluent Interface ===")

    var ctx = create_stf_context()
    
    var input_data = ctx.logical_data[DType.float32](1024)
    var output_data = ctx.logical_data[DType.float32](1024)
    
    # Test method chaining
    print("Testing method chaining:")
    var task_builder = ctx.task(input_data.read(DevicePlace(0)), output_data.write(DevicePlace(0)))
    var executable_task = task_builder.on(DevicePlace(0))
    executable_task.exec[compute_kernel]()

    # Test full chain
    print("Testing full chain:")
    ctx.task(input_data.read(DevicePlace(0)), output_data.write(DevicePlace(0))) \
       .on(DevicePlace(0)) \
       .exec[vector_add_kernel]()
    
    ctx.synchronize()
    print("✅ Fluent interface test completed!")


fn test_large_scale_workflow() raises:
    """Test a large-scale workflow with many tasks."""
    print("\n=== Testing Large-Scale Workflow ===")

    var ctx = create_stf_context()
    
    # Create many data objects using List (following Mojo stdlib pattern)
    var data_list = List[LogicalDataRef[DType.float32]]()
    for i in range(5):
        var data = ctx.logical_data[DType.float32](1024)
        data_list.append(data)

    print("Created", len(data_list), "data objects for large-scale workflow")

    # Create many tasks
    print("Creating and executing many tasks:")
    for i in range(len(data_list) - 1):
        var current_data = data_list[i]
        var next_data = data_list[i + 1]

        print("  Task", i, ": processing data", current_data._data_id, "->", next_data._data_id)
        ctx.task(current_data.read(DevicePlace(0)), next_data.write(DevicePlace(0))) \
           .on(DevicePlace(0)) \
           .exec[compute_kernel]()
    
    ctx.synchronize()
    print("✅ Large-scale workflow test completed!")


# ===----------------------------------------------------------------------=== #
# Test Execution - Following Mojo stdlib test pattern
# ===----------------------------------------------------------------------=== #

# Test the exact target use case
test_target_use_case()

# Test various features
test_different_data_types()
test_complex_pipeline()
test_mixed_execution_places()
test_dependency_types()
test_fluent_interface()
test_large_scale_workflow()
