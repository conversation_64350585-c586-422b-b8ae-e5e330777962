version: 6
environments:
  default:
    channels:
    - url: https://conda.modular.com/max-nightly/
    - url: https://conda.anaconda.org/conda-forge/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-3_kmp_llvm.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiofiles-24.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiohappyeyeballs-2.6.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.12.13-py312h178313f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.3.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/annotated-types-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.9.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asgiref-3.8.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.9.0-hbfa7f16_15.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.9.2-h5e3027f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.12.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.3.1-hafb2847_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.5.4-h76f0014_12.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.10.2-h015de20_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.20.1-hdfce8c9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.13.1-h1e5e6c0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.8.3-h5e174a9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.2.4-hafb2847_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.2.7-hafb2847_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.32.10-hff780f1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.510-h937e755_12.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-core-cpp-1.14.0-h5cfcd09_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-identity-cpp-1.10.0-h113e628_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-blobs-cpp-12.13.0-h3cf044e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-common-cpp-12.8.0-h736e048_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-files-datalake-cpp-12.12.0-ha633028_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/backoff-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py312h2ec8cdc_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.6.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py312h06ac9bb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-crt-tools-12.9.86-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cudart-12.9.79-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-cudart_linux-64-12.9.79-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cuobjdump-12.9.82-hbd13f7d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cupti-12.9.79-h9ab20c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvcc-tools-12.9.86-he02047a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvdisasm-12.9.88-hbd13f7d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvvm-tools-12.9.86-he02047a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-12.9-h4f385c5_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/datasets-2.14.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/deprecated-1.2.18-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/dill-0.3.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/dnspython-2.7.0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/email-validator-2.2.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/email_validator-2.2.0-hd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fastapi-0.115.14-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fastapi-cli-0.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.6.0-py312hb9e946c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-h5888daf_1005.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/gguf-0.17.1-pyhc364b38_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.1-hbabe93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.2.1-py312h7201bc8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/googleapis-common-protos-1.70.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/grpcio-1.71.0-py312hdcb7bd4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/grpcio-reflection-1.71.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/grpcio-tools-1.71.0-py312h2a0d124_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/hf-transfer-0.1.9-py312h5bc9d60_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/hf-xet-1.1.5-py39h260a9e5_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/httptools-0.6.4-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.33.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.5.0-pyha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-20.0.0-h1b9301b_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-20.0.0-hcb10f89_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-20.0.0-hcb10f89_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-20.0.0-h1bed206_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_hfdb39a5_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_h372d94f_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.14.1-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.36.0-hc4361e1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.36.0-h0121fbd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.71.0-h8e591d7_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_hc41d3b0_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-1.21.0-hd1b1c89_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-headers-1.21.0-ha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-20.0.0-h081d1f1_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2025.06.26-hba17884_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsentencepiece-0.2.0-he636bdd_11.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.21.0-h0e7cc3e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtorch-2.7.1-cpu_mkl_hb1c5dc7_100.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.10.0-h202a827_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-20.1.7-h024ca30_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py312h178313f_1.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025062805-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-pipelines-25.5.0.dev2025062805-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-python-25.5.0.dev2025062805-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062805-release.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2024.2.2-ha957f24_16.conda
      - conda: https://conda.modular.com/max-nightly/noarch/modular-25.5.0.dev2025062805-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-h24ddda3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/msgspec-0.19.0-py312h66e93f0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.6.0-py312h178313f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/multiprocess-0.70.15-py312h98912ed_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.5-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ninja-1.13.0-h7aa8ee6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nlohmann_json-3.12.0-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.0-py312h6cf2f7f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-api-1.30.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-otlp-proto-common-1.30.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-otlp-proto-http-1.30.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-prometheus-0.51b0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-proto-1.30.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-sdk-1.30.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-semantic-conventions-0.51b0-pyh3cfb1c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/optree-0.16.0-py312h68727a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.1.2-h17f744e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.0-py312hf9745cd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.2.1-py312h80c1187_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus-async-25.1.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/prometheus-cpp-1.3.0-ha5d0236_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/propcache-0.3.1-py312h178313f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/protobuf-5.29.3-py312h0f4f066_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-20.0.0-py312h7900ff3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-core-20.0.0-py312h01725c0_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-2.13.6-pyhc790b64_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-2.13.6-pyh217bc35_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-2.11.7-pyh3cfb1c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pydantic-core-2.33.2-py312h680f630_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-settings-2.10.1-pyh3cfb1c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyinstrument-5.0.2-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysoundfile-0.13.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.11-h9e4cc4f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dotenv-1.1.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-multipart-0.0.20-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.5.0-py312h66e93f0_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-7_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-2.7.1-cpu_mkl_py312_h6a7998d_100.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py312h178313f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py312hbf22597_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2025.06.26-h9925aae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/regex-2024.11.6-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.0.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-toolkit-0.11.3-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.5.21-h7ab7c64_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/safetensors-0.5.3-py312h12e396e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.15.2-py312ha707e6e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-0.2.0-hc8f76dd_11.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-python-0.2.0-py312hb957f94_11.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-spm-0.2.0-he636bdd_11.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sentinel-1.0.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sleef-3.8-h1b44611_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sse-starlette-2.1.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/starlette-0.41.2-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/taskgroup-0.2.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.13.0-hceb3a55_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tiktoken-0.9.0-py312h14ff09d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tokenizers-0.21.2-py312h8360d73_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/transformers-4.53.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tree-2.2.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/triton-3.3.1-cuda126py312hebffaa9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.0-h32cad80_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-inspection-0.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uvicorn-0.34.3-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uvicorn-standard-0.34.3-h31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/uvloop-0.21.0-py312h66e93f0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/watchfiles-1.1.0-py312h12e396e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/websockets-15.0.1-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wrapt-1.17.2-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xgrammar-0.1.19-py312he346f12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.3-hb47aa4a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.20.1-py312h178313f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py312h66e93f0_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-3_kmp_llvm.conda
  build_number: 3
  sha256: cec7343e76c9da6a42c7e7cba53391daa6b46155054ef61a5ef522ea27c5a058
  md5: ee5c2118262e30b972bc0b4db8ef0ba5
  depends:
  - llvm-openmp >=9.0.1
  license: BSD-3-Clause
  license_family: BSD
  size: 7649
  timestamp: 1741390353130
- conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
  sha256: a3967b937b9abf0f2a99f3173fa4630293979bd1644709d89580e7c62a544661
  md5: aaa2a381ccc56eac91d63b6c1240312f
  depends:
  - cpython
  - python-gil
  license: MIT
  license_family: MIT
  size: 8191
  timestamp: 1744137672556
- conda: https://conda.anaconda.org/conda-forge/noarch/aiofiles-24.1.0-pyhd8ed1ab_1.conda
  sha256: 8e18809f00b0bfe504bc6180b80d844016690925ddf0e61272111eec079774c3
  md5: 7e8045a75e921648c082ba7cd7edd114
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 19712
  timestamp: 1733829125632
- conda: https://conda.anaconda.org/conda-forge/noarch/aiohappyeyeballs-2.6.1-pyhd8ed1ab_0.conda
  sha256: 7842ddc678e77868ba7b92a726b437575b23aaec293bca0d40826f1026d90e27
  md5: 18fd895e0e775622906cdabfc3cf0fb4
  depends:
  - python >=3.9
  license: PSF-2.0
  license_family: PSF
  size: 19750
  timestamp: 1741775303303
- conda: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.12.13-py312h178313f_0.conda
  sha256: 5b73f69c26a18236bd65bb48aafa53dbbd47b1f6ba41d7e4539440a849d6ca60
  md5: a91df3f6eaf0d0afd155274a1833ab3c
  depends:
  - __glibc >=2.17,<3.0.a0
  - aiohappyeyeballs >=2.5.0
  - aiosignal >=1.1.2
  - attrs >=17.3.0
  - frozenlist >=1.1.1
  - libgcc >=13
  - multidict >=4.5,<7.0
  - propcache >=0.2.0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - yarl >=1.17.0,<2.0
  license: MIT AND Apache-2.0
  license_family: Apache
  size: 1003059
  timestamp: 1749925160150
- conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.3.2-pyhd8ed1ab_0.conda
  sha256: 7de8ced1918bbdadecf8e1c1c68237fe5709c097bd9e0d254f4cad118f4345d0
  md5: 1a3981115a398535dbe3f6d5faae3d36
  depends:
  - frozenlist >=1.1.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 13229
  timestamp: 1734342253061
- conda: https://conda.anaconda.org/conda-forge/noarch/annotated-types-0.7.0-pyhd8ed1ab_1.conda
  sha256: e0ea1ba78fbb64f17062601edda82097fcf815012cf52bb704150a2668110d48
  md5: 2934f256a8acfe48f6ebb4fce6cde29c
  depends:
  - python >=3.9
  - typing-extensions >=4.0.0
  license: MIT
  license_family: MIT
  size: 18074
  timestamp: 1733247158254
- conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.9.0-pyh29332c3_0.conda
  sha256: b28e0f78bb0c7962630001e63af25a89224ff504e135a02e50d4d80b6155d386
  md5: 9749a2c77a7c40d432ea0927662d7e52
  depends:
  - exceptiongroup >=1.0.2
  - idna >=2.8
  - python >=3.9
  - sniffio >=1.1
  - typing_extensions >=4.5
  - python
  constrains:
  - trio >=0.26.1
  - uvloop >=0.21
  license: MIT
  license_family: MIT
  size: 126346
  timestamp: 1742243108743
- conda: https://conda.anaconda.org/conda-forge/noarch/asgiref-3.8.1-pyhd8ed1ab_1.conda
  sha256: c8d51a32f3530e3ca176d8f7bf5621c68c7c1c03fe9cc5c3c49d8ba4a1248f43
  md5: 21187c8276f87b6aedfe63c267a6eed7
  depends:
  - python >=3.9
  - typing_extensions >=4
  license: BSD-3-Clause
  license_family: BSD
  size: 26436
  timestamp: 1733215704904
- conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
  sha256: 99c53ffbcb5dc58084faf18587b215f9ac8ced36bbfb55fa807c00967e419019
  md5: a10d11958cadc13fdb43df75f8b1903f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 57181
  timestamp: 1741918625732
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.9.0-hbfa7f16_15.conda
  sha256: 85086df9b358450196a13fc55bab1c552227df78cafddbe2d15caaea458b41a6
  md5: 16baa9bb7f70a1e457a82023898314a7
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-sdkutils >=0.2.4,<0.2.5.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 122993
  timestamp: 1750291448852
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.9.2-h5e3027f_0.conda
  sha256: d61cce967e6d97d03aa2828458f7344cdc93422fd2c1126976ab8f475a313363
  md5: 0ead3ab65460d51efb27e5186f50f8e4
  depends:
  - __glibc >=2.17,<3.0.a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 51039
  timestamp: 1749095567725
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.12.3-hb9d3cd8_0.conda
  sha256: 251883d45fbc3bc88a8290da073f54eb9d17e8b9edfa464d80cff1b948c571ec
  md5: 8448031a22c697fac3ed98d69e8a9160
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 236494
  timestamp: 1747101172537
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.3.1-hafb2847_5.conda
  sha256: 68e7ec0ab4f5973343de089ac71c7b9b9387c35640c61e0236ad45fc3dbfaaaa
  md5: e96cc668c0f9478f5771b37d57f90386
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 21817
  timestamp: 1747144982788
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.5.4-h76f0014_12.conda
  sha256: 7b89ed99ac73c863bea4479f1f1af6ce250f9f1722d2804e07cf05d3630c7e08
  md5: f978f2a3032952350d0036c4c4a63bd6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 57252
  timestamp: 1750287878861
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.10.2-h015de20_2.conda
  sha256: ca0268cead19e985f9b153613f0f6cdb46e0ca32e1647466c506f256269bcdd9
  md5: ad05d594704926ba7c0c894a02ea98f1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-compression >=0.3.1,<0.3.2.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 223038
  timestamp: 1750289165728
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.20.1-hdfce8c9_0.conda
  sha256: c6bd4f067a7829795e1c44e4536b71d46f55f69569216aed34a7b375815fa046
  md5: dd2d3530296d75023a19bc9dfb0a1d59
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - s2n >=1.5.21,<1.5.22.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 179223
  timestamp: 1749844480175
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.13.1-h1e5e6c0_3.conda
  sha256: f9e63492d5dd17f361878ce7efa1878de27225216b4e07990a6cb18c378014dc
  md5: d55921ca3469224f689f974278107308
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 215867
  timestamp: 1750291920145
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.8.3-h5e174a9_0.conda
  sha256: f4e7b200da5df7135cd087618fa30b2cd60cec0eebbd5570fb4c1e9a789dd9aa
  md5: dea2540e57e8c1b949ca58ff4c7c0cbf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - openssl >=3.5.0,<4.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  license: Apache-2.0
  size: 133960
  timestamp: 1750831815089
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.2.4-hafb2847_0.conda
  sha256: 18c588c386e21e2a926c6f3c1ba7aaf69059ce1459a134f7c8c1ebfc68cf67ec
  md5: 65853df44b7e4029d978c50be888ed89
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 59037
  timestamp: 1747308292628
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.2.7-hafb2847_1.conda
  sha256: 03a5e4b3dcda35696133632273043d0b81e55129ff0f9e6d75483aa8eb96371b
  md5: 6d28d50637fac4f081a0903b4b33d56d
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 76627
  timestamp: 1747141741534
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.32.10-hff780f1_1.conda
  sha256: 9602a5199dccf257709afdef326abfde6e84c63862b7cee59979803c4d636840
  md5: 843f52366658086c4f0b0654afbf3730
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - aws-c-mqtt >=0.13.1,<0.13.2.0a0
  - aws-c-event-stream >=0.5.4,<0.5.5.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  - aws-c-s3 >=0.8.3,<0.8.4.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-sdkutils >=0.2.4,<0.2.5.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-io >=0.20.1,<0.20.2.0a0
  license: Apache-2.0
  size: 399987
  timestamp: 1750855462459
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.510-h937e755_12.conda
  sha256: 8fa640da0d7223c3d120e8d222d4b4cb519f05b628f60764192d08a937229cec
  md5: f4e09870ecaceb4594574e515bb04747
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.14.1,<9.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-event-stream >=0.5.4,<0.5.5.0a0
  - aws-crt-cpp >=0.32.10,<0.32.11.0a0
  - libzlib >=1.3.1,<2.0a0
  license: Apache-2.0
  size: 3401464
  timestamp: 1751089137364
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-core-cpp-1.14.0-h5cfcd09_0.conda
  sha256: fe07debdb089a3db17f40a7f20d283d75284bb4fc269ef727b8ba6fc93f7cb5a
  md5: 0a8838771cc2e985cd295e01ae83baf1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.10.1,<9.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 345117
  timestamp: 1728053909574
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-identity-cpp-1.10.0-h113e628_0.conda
  sha256: 286b31616c191486626cb49e9ceb5920d29394b9e913c23adb7eb637629ba4de
  md5: 73f73f60854f325a55f1d31459f2ab73
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 232351
  timestamp: 1728486729511
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-blobs-cpp-12.13.0-h3cf044e_1.conda
  sha256: 2606260e5379eed255bcdc6adc39b93fb31477337bcd911c121fc43cd29bf394
  md5: 7eb66060455c7a47d9dcdbfa9f46579b
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-storage-common-cpp >=12.8.0,<12.8.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 549342
  timestamp: 1728578123088
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-common-cpp-12.8.0-h736e048_1.conda
  sha256: 273475f002b091b66ce7366da04bf164c3732c03f8692ab2ee2d23335b6a82ba
  md5: 13de36be8de3ae3f05ba127631599213
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.12.7,<2.14.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 149312
  timestamp: 1728563338704
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-files-datalake-cpp-12.12.0-ha633028_1.conda
  sha256: 5371e4f3f920933bb89b926a85a67f24388227419abd6e99f6086481e5e8d5f2
  md5: 7c1980f89dd41b097549782121a73490
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-storage-blobs-cpp >=12.13.0,<12.13.1.0a0
  - azure-storage-common-cpp >=12.8.0,<12.8.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 287366
  timestamp: 1728729530295
- conda: https://conda.anaconda.org/conda-forge/noarch/backoff-2.2.1-pyhd8ed1ab_1.conda
  sha256: f334115c6b0c6c2cd0d28595365f205ec7eaa60bcc5ff91a75d7245f728be820
  md5: a38b801f2bcc12af80c2e02a9e4ce7d9
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 18816
  timestamp: 1733771192649
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py312h2ec8cdc_3.conda
  sha256: dc27c58dc717b456eee2d57d8bc71df3f562ee49368a2351103bc8f1b67da251
  md5: a32e0c069f6c3dcac635f7b0b0dac67e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  license: MIT
  license_family: MIT
  size: 351721
  timestamp: 1749230265727
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
  sha256: f8003bef369f57396593ccd03d08a8e21966157269426f71e943f96e4b579aeb
  md5: f7f0d6cc2dc986d42ac2689ec88192be
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 206884
  timestamp: 1744127994291
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
  sha256: 7cfec9804c84844ea544d98bda1d9121672b66ff7149141b8415ca42dfcd44f6
  md5: 72525f07d72806e3b639ad4504c30ce5
  depends:
  - __unix
  license: ISC
  size: 151069
  timestamp: 1749990087500
- conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.6.15-pyhd8ed1ab_0.conda
  sha256: d71c85835813072cd6d7ce4b24be34215cd90c104785b15a5d58f4cd0cb50778
  md5: 781d068df0cc2407d4db0ecfbb29225b
  depends:
  - python >=3.9
  license: ISC
  size: 155377
  timestamp: 1749972291158
- conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py312h06ac9bb_0.conda
  sha256: cba6ea83c4b0b4f5b5dc59cb19830519b28f95d7ebef7c9c5cf1c14843621457
  md5: a861504bbea4161a9170b85d4d2be840
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - pycparser
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 294403
  timestamp: 1725560714366
- conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
  sha256: 535ae5dcda8022e31c6dc063eb344c80804c537a5a04afba43a845fa6fa130f5
  md5: 40fe4284b8b5835a9073a645139f35af
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 50481
  timestamp: 1746214981991
- conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
  sha256: 8aee789c82d8fdd997840c952a586db63c6890b00e88c4fb6e80a38edd5f51c0
  md5: 94b550b8d3a614dbd326af798c7dfb40
  depends:
  - __unix
  - python >=3.10
  license: BSD-3-Clause
  license_family: BSD
  size: 87749
  timestamp: 1747811451319
- conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
  sha256: ab29d57dc70786c1269633ba3dff20288b81664d3ff8d21af995742e2bb03287
  md5: 962b9857ee8e7018c22f2776ffa0b2d7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 27011
  timestamp: 1733218222191
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
  noarch: generic
  sha256: 7e7bc8e73a2f3736444a8564cbece7216464c00f0bc38e604b0c792ff60d621a
  md5: e5279009e7a7f7edd3cd2880c502b3cc
  depends:
  - python >=3.12,<3.13.0a0
  - python_abi * *_cp312
  license: Python-2.0
  size: 45852
  timestamp: 1749047748072
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-crt-tools-12.9.86-ha770c72_1.conda
  sha256: 4475409f91176c0a77ead29e961617366ef1fbe932c7315abdd5699ad134f0be
  md5: ba98092d1090d5f5ddd2d7f827e7d3a5
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 28928
  timestamp: 1749226545023
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cudart-12.9.79-h5888daf_0.conda
  sha256: 57d1294ecfaf9dc8cdb5fc4be3e63ebc7614538bddb5de53cfd9b1b7de43aed5
  md5: cb15315d19b58bd9cd424084e58ad081
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-cudart_linux-64 12.9.79 h3f2d84a_0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 23242
  timestamp: 1749218416505
- conda: https://conda.anaconda.org/conda-forge/noarch/cuda-cudart_linux-64-12.9.79-h3f2d84a_0.conda
  sha256: 6cde0ace2b995b49d0db2eefb7bc30bf00ffc06bb98ef7113632dec8f8907475
  md5: 64508631775fbbf9eca83c84b1df0cae
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 197249
  timestamp: 1749218394213
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cuobjdump-12.9.82-hbd13f7d_0.conda
  sha256: a4f37cd8823d209639bdda1eea3ee0eb01040e44e2480c2f393e684c472c2f0c
  md5: 667a138d80047e7869f5330087772fd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-nvdisasm
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 243219
  timestamp: 1749223489014
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cupti-12.9.79-h9ab20c4_0.conda
  sha256: 55922005d1b31ba090455ab39d2e5a9b771fe503713d4b7699752a76aedccb2b
  md5: 229b3cc1f6b6b633923e1c9856ee0d80
  depends:
  - __glibc >=2.28,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 1842820
  timestamp: 1749218443367
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvcc-tools-12.9.86-he02047a_1.conda
  sha256: 7e5ab4ae67254c6d814007708a8183355684c81a917b383a7f042c25149737c3
  md5: a076f1ec812ce8fceacd538d6e672f37
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-crt-tools 12.9.86 ha770c72_1
  - cuda-nvvm-tools 12.9.86 he02047a_1
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=12
  - libstdcxx >=12
  constrains:
  - gcc_impl_linux-64 >=6,<15.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 27490340
  timestamp: 1749226666055
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvdisasm-12.9.88-hbd13f7d_0.conda
  sha256: 6ef7c122897a9e27bc3aaed1745ea03bfecb5f553d420b0e4bf2ef6f568aab81
  md5: 7e9e4991e5890f32e8ef3c9a971171df
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 5517799
  timestamp: 1749221325784
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvvm-tools-12.9.86-he02047a_1.conda
  sha256: 0958aee5a72f4be02c8f988539261cf549c9fcd6b61c6ce895bc6a13fe61f5d6
  md5: f716064b73c93d9aab74b5cc7f57985d
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=12
  - libstdcxx >=12
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 24248725
  timestamp: 1749226615764
- conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-12.9-h4f385c5_3.conda
  sha256: 5f5f428031933f117ff9f7fcc650e6ea1b3fef5936cf84aa24af79167513b656
  md5: b6d5d7f1c171cbd228ea06b556cfa859
  constrains:
  - cudatoolkit 12.9|12.9.*
  - __cuda >=12
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 21578
  timestamp: 1746134436166
- conda: https://conda.anaconda.org/conda-forge/noarch/datasets-2.14.4-pyhd8ed1ab_0.conda
  sha256: 7e09bd083a609138b780fcc4535924cb96814d2c908a36d4c64a2ba9ee3efe7f
  md5: 3e087f072ce03c43a9b60522f5d0ca2f
  depends:
  - aiohttp
  - dill >=0.3.0,<0.3.8
  - fsspec >=2021.11.1
  - huggingface_hub >=0.14.0,<1.0.0
  - importlib-metadata
  - multiprocess
  - numpy >=1.17
  - packaging
  - pandas
  - pyarrow >=8.0.0
  - python >=3.8.0
  - python-xxhash
  - pyyaml >=5.1
  - requests >=2.19.0
  - tqdm >=4.62.1
  license: Apache-2.0
  license_family: Apache
  size: 347303
  timestamp: 1691593908658
- conda: https://conda.anaconda.org/conda-forge/noarch/deprecated-1.2.18-pyhd8ed1ab_0.conda
  sha256: d614bcff10696f1efc714df07651b50bf3808401fcc03814309ecec242cc8870
  md5: 0cef44b1754ae4d6924ac0eef6b9fdbe
  depends:
  - python >=3.9
  - wrapt <2,>=1.10
  license: MIT
  license_family: MIT
  size: 14382
  timestamp: 1737987072859
- conda: https://conda.anaconda.org/conda-forge/noarch/dill-0.3.7-pyhd8ed1ab_0.conda
  sha256: 4ff20c6be028be2825235631c45d9e4a75bca1de65f8840c02dfb28ea0137c45
  md5: 5e4f3466526c52bc9af2d2353a1460bd
  depends:
  - python >=3.7
  license: BSD-3-Clause
  license_family: BSD
  size: 87553
  timestamp: 1690101185422
- conda: https://conda.anaconda.org/conda-forge/noarch/dnspython-2.7.0-pyhff2d567_1.conda
  sha256: 3ec40ccf63f2450c5e6c7dd579e42fc2e97caf0d8cd4ba24aa434e6fc264eda0
  md5: 5fbd60d61d21b4bd2f9d7a48fe100418
  depends:
  - python >=3.9,<4.0.0
  - sniffio
  constrains:
  - aioquic >=1.0.0
  - wmi >=1.5.1
  - httpx >=0.26.0
  - trio >=0.23
  - cryptography >=43
  - httpcore >=1.0.0
  - idna >=3.7
  - h2 >=4.1.0
  license: ISC
  license_family: OTHER
  size: 172172
  timestamp: 1733256829961
- conda: https://conda.anaconda.org/conda-forge/noarch/email-validator-2.2.0-pyhd8ed1ab_1.conda
  sha256: b91a19eb78edfc2dbb36de9a67f74ee2416f1b5273dd7327abe53f2dbf864736
  md5: da16dd3b0b71339060cd44cb7110ddf9
  depends:
  - dnspython >=2.0.0
  - idna >=2.0.0
  - python >=3.9
  license: Unlicense
  size: 44401
  timestamp: 1733300827551
- conda: https://conda.anaconda.org/conda-forge/noarch/email_validator-2.2.0-hd8ed1ab_1.conda
  sha256: e0d0fdf587aa0ed0ff08b2bce3ab355f46687b87b0775bfba01cc80a859ee6a2
  md5: 0794f8807ff2c6f020422cacb1bd7bfa
  depends:
  - email-validator >=2.2.0,<2.2.1.0a0
  license: Unlicense
  size: 6552
  timestamp: 1733300828176
- conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
  sha256: ce61f4f99401a4bd455b89909153b40b9c823276aefcbb06f2044618696009ca
  md5: 72e42d28960d875c7654614f8b50939a
  depends:
  - python >=3.9
  - typing_extensions >=4.6.0
  license: MIT and PSF-2.0
  size: 21284
  timestamp: 1746947398083
- conda: https://conda.anaconda.org/conda-forge/noarch/fastapi-0.115.14-pyhe01879c_0.conda
  sha256: 4e1d1aabe3199033c9c5a47176b0b4e0cd40621156fc72f706047c2348dd72ff
  md5: 8f4fcc62c241e372495c19fe6f8b1908
  depends:
  - python >=3.9
  - starlette >=0.40.0,<0.47.0
  - typing_extensions >=4.8.0
  - pydantic >=1.7.4,!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0
  - email_validator >=2.0.0
  - fastapi-cli >=0.0.5
  - httpx >=0.23.0
  - jinja2 >=3.1.5
  - python-multipart >=0.0.18
  - uvicorn-standard >=0.12.0
  - python
  license: MIT
  size: 78363
  timestamp: 1750986285010
- conda: https://conda.anaconda.org/conda-forge/noarch/fastapi-cli-0.0.7-pyhd8ed1ab_0.conda
  sha256: 300683731013b7221922339cd40430bb3c2ddeeb658fd7e37f5099ffe64e4db0
  md5: d960e0ea9e1c561aa928f6c4439f04c7
  depends:
  - python >=3.9
  - rich-toolkit >=0.11.1
  - typer >=0.12.3
  - uvicorn-standard >=0.15.0
  license: MIT
  license_family: MIT
  size: 15546
  timestamp: 1734302408607
- conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
  sha256: de7b6d4c4f865609ae88db6fa03c8b7544c2452a1aa5451eb7700aad16824570
  md5: 4547b39256e296bb758166893e909a7c
  depends:
  - python >=3.9
  license: Unlicense
  size: 17887
  timestamp: 1741969612334
- conda: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.6.0-py312hb9e946c_0.conda
  sha256: 685ef959d9f3ceeb2bd0dbda36b4bdcfb6e3ae7d1a7cc2c364de543cc28c597f
  md5: 13290e5d9cb327b1b61c1bd8089ac920
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: APACHE
  size: 113391
  timestamp: 1746635510382
- conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.5.1-pyhd8ed1ab_0.conda
  sha256: cd6ae92ae5aa91a7e58cf39f1442d4821279f43f1c9499d15f45558d4793d1e0
  md5: 2d2c9ef879a7e64e2dc657b09272c2b6
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 145521
  timestamp: 1748101667956
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
  sha256: 88db27c666e1f8515174bf622a3e2ad983c94d69e3a23925089e476b9b06ad00
  md5: c63e7590d4d6f4c85721040ed8b12888
  depends:
  - __glibc >=2.17,<3.0.a0
  - gettext-tools 0.24.1 h5888daf_0
  - libasprintf 0.24.1 h8e693c7_0
  - libasprintf-devel 0.24.1 h8e693c7_0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  - libgettextpo-devel 0.24.1 h5888daf_0
  - libstdcxx >=13
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  size: 511988
  timestamp: 1746228987123
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
  sha256: 3ba33868630b903e3cda7a9176363cdf02710fb8f961efed5f8200c4d53fb4e3
  md5: d54305672f0361c2f3886750e7165b5f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 3129801
  timestamp: 1746228937647
- conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-h5888daf_1005.conda
  sha256: 6c33bf0c4d8f418546ba9c250db4e4221040936aef8956353bc764d4877bc39a
  md5: d411fc29e338efb48c5fd4576d71d881
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 119654
  timestamp: 1726600001928
- conda: https://conda.anaconda.org/conda-forge/noarch/gguf-0.17.1-pyhc364b38_0.conda
  sha256: 06aa364c6ce109e21858fc016a430c22f738fe6377c67944504df7fc0da3ec20
  md5: aaaa7074fd79c4e1e79b3e1af5a77efa
  depends:
  - python >=3.8
  - numpy >=1.17
  - tqdm >=4.27
  - pyyaml >=5.1
  - sentencepiece >=0.1.98,<=0.2.0
  - python
  license: MIT
  license_family: MIT
  size: 92085
  timestamp: 1750400728782
- conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.1-hbabe93e_0.conda
  sha256: dc824dc1d0aa358e28da2ecbbb9f03d932d976c8dca11214aa1dcdfcbd054ba2
  md5: ff862eebdfeb2fd048ae9dc92510baca
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 143452
  timestamp: 1718284177264
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
  sha256: 309cf4f04fec0c31b6771a5809a1909b4b3154a2208f52351e1ada006f4c750c
  md5: c94a5994ef49749880a8139cf9afcbe1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  size: 460055
  timestamp: 1718980856608
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.2.1-py312h7201bc8_0.conda
  sha256: 92cd104e06fafabc5a0da93ad16a18a7e33651208901bdb0ecd89d10c846e43a
  md5: c539cba0be444c6cefcb853987187d9e
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpc >=1.3.1,<2.0a0
  - mpfr >=4.2.1,<5.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: LGPL-3.0-or-later
  license_family: LGPL
  size: 213405
  timestamp: 1745509508879
- conda: https://conda.anaconda.org/conda-forge/noarch/googleapis-common-protos-1.70.0-pyhd8ed1ab_0.conda
  sha256: e0aa51de5565e92139791c5b8e2908e3cadd2c5fce6941a225889070815bcd99
  md5: 7999fb45c48645272d7d88de0b7dc188
  depends:
  - protobuf >=3.20.2,<7.0.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 142129
  timestamp: 1744688907411
- conda: https://conda.anaconda.org/conda-forge/linux-64/grpcio-1.71.0-py312hdcb7bd4_1.conda
  sha256: fabc35be513624005d9bc8585f807c3d8386bcf2f172631750305bf2f890e90f
  md5: 5aa1cb5ae0ce3986f70c155608865134
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgrpc 1.71.0 h8e591d7_1
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: APACHE
  size: 919668
  timestamp: 1745229564678
- conda: https://conda.anaconda.org/conda-forge/noarch/grpcio-reflection-1.71.0-pyhd8ed1ab_0.conda
  sha256: 53744917bd0774f0738416c8699cc38faeccea9b6478e526b0501b4137efc5b8
  md5: 670906e71608d886502a7293cf1e0677
  depends:
  - grpcio 1.71.0.*
  - protobuf >=3.12.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 27846
  timestamp: 1741643573344
- conda: https://conda.anaconda.org/conda-forge/linux-64/grpcio-tools-1.71.0-py312h2a0d124_1.conda
  sha256: fbe8a6c17bb41c9b82f06bfad34a8b4b411b8909e491d7f616f73f697ccd3a83
  md5: a6ff9b3b25fe6c088d1e27ec572df4bb
  depends:
  - __glibc >=2.17,<3.0.a0
  - grpcio 1.71.0 *_1
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - protobuf
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - setuptools
  license: Apache-2.0
  license_family: APACHE
  size: 234597
  timestamp: 1745229889814
- conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
  sha256: f64b68148c478c3bfc8f8d519541de7d2616bf59d44485a5271041d40c061887
  md5: 4b69232755285701bc86a5afe4d9933a
  depends:
  - python >=3.9
  - typing_extensions
  license: MIT
  license_family: MIT
  size: 37697
  timestamp: 1745526482242
- conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
  sha256: 0aa1cdc67a9fe75ea95b5644b734a756200d6ec9d0dff66530aec3d1c1e9df75
  md5: b4754fb1bdcb70c8fd54f918301582c6
  depends:
  - hpack >=4.1,<5
  - hyperframe >=6.1,<7
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 53888
  timestamp: 1738578623567
- conda: https://conda.anaconda.org/conda-forge/linux-64/hf-transfer-0.1.9-py312h5bc9d60_1.conda
  sha256: 21acb87a6403f88b2dbdefb79a537bc8fe871b86c60f9b690206eaf7ad1f009c
  md5: 3639aa7b1297e680220f52c2b8a21200
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - python_abi 3.12.* *_cp312
  - openssl >=3.4.1,<4.0a0
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 1339225
  timestamp: 1739803760467
- conda: https://conda.anaconda.org/conda-forge/linux-64/hf-xet-1.1.5-py39h260a9e5_3.conda
  noarch: python
  sha256: b28905ff975bd935cd113ee97b7eb5b5e3b0969a21302135c6ae096aa06a61f6
  md5: 7b6007f4ad18a970ca3a977148cf47de
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  - _python_abi3_support 1.*
  - cpython >=3.9
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 2537615
  timestamp: 1750541218448
- conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
  sha256: 6ad78a180576c706aabeb5b4c8ceb97c0cb25f1e112d76495bff23e3779948ba
  md5: 0a802cb9888dd14eeefc611f05c40b6e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 30731
  timestamp: 1737618390337
- conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
  sha256: 04d49cb3c42714ce533a8553986e1642d0549a05dc5cc48e0d43ff5be6679a5b
  md5: 4f14640d58e2cc0aa0819d9d8ba125bb
  depends:
  - python >=3.9
  - h11 >=0.16
  - h2 >=3,<5
  - sniffio 1.*
  - anyio >=4.0,<5.0
  - certifi
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 49483
  timestamp: 1745602916758
- conda: https://conda.anaconda.org/conda-forge/linux-64/httptools-0.6.4-py312h66e93f0_0.conda
  sha256: 621e7e050b888e5239d33e37ea72d6419f8367e5babcad38b755586f20264796
  md5: 8b1160b32557290b64d5be68db3d996d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 101872
  timestamp: 1732707756745
- conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
  sha256: cd0f1de3697b252df95f98383e9edb1d00386bfdd03fdf607fa42fe5fcb09950
  md5: d6989ead454181f4f9bc987d3dc4e285
  depends:
  - anyio
  - certifi
  - httpcore 1.*
  - idna
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 63082
  timestamp: 1733663449209
- conda: https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.33.1-pyhd8ed1ab_0.conda
  sha256: bdbfb0a2aa957fc2a79dc342022529def69162825d6420f03b2dcfaab92765a2
  md5: 4a634f9e9ad0e28ecd4da031a4616d03
  depends:
  - filelock
  - fsspec >=2023.5.0
  - hf-xet >=1.1.2,<2.0.0
  - packaging >=20.9
  - python >=3.9
  - pyyaml >=5.1
  - requests
  - tqdm >=4.42.1
  - typing-extensions >=3.7.4.3
  - typing_extensions >=3.7.4.3
  license: Apache-2.0
  size: 317782
  timestamp: 1750865913736
- conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
  sha256: 77af6f5fe8b62ca07d09ac60127a30d9069fdc3c68d6b256754d0ffb1f7779f8
  md5: 8e6923fc12f1fe8f8c4e5c9f343256ac
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 17397
  timestamp: 1737618427549
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12129203
  timestamp: 1720853576813
- conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
  sha256: d7a472c9fd479e2e8dcb83fb8d433fce971ea369d704ece380e876f9c3494e87
  md5: 39a4f67be3286c86d696df570b1201b7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 49765
  timestamp: 1733211921194
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.5.0-pyha770c72_1.conda
  sha256: 13766b88fc5b23581530d3a0287c0c58ad82f60401afefab283bf158d2be55a9
  md5: 315607a3030ad5d5227e76e0733798ff
  depends:
  - python >=3.9
  - zipp >=0.5
  license: Apache-2.0
  license_family: APACHE
  size: 28623
  timestamp: 1733223207185
- conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
  sha256: f1ac18b11637ddadc05642e8185a851c7fab5998c6f5470d716812fae943b2af
  md5: 446bd6c8cb26050d528881df495ce646
  depends:
  - markupsafe >=2.0
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 112714
  timestamp: 1741263433881
- conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 117831
  timestamp: 1646151697040
- conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
  sha256: 99df692f7a8a5c27cd14b5fb1374ee55e756631b9c3d659ed3ee60830249b238
  md5: 3f43953b7d3fb3aaa1d0d0723d91e368
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1370023
  timestamp: 1719463201255
- conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
  sha256: aad2a703b9d7b038c0f745b853c6bb5f122988fe1a7a096e0e606d9cbec4eaab
  md5: a8832b479f93521a9e7b5b743803be51
  depends:
  - libgcc-ng >=12
  license: LGPL-2.0-only
  license_family: LGPL
  size: 508258
  timestamp: 1664996250081
- conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
  sha256: d6a61830a354da022eae93fa896d0991385a875c6bba53c82263a289deda9db8
  md5: 000e85703f0fd9594c81710dd5066471
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  size: 248046
  timestamp: 1739160907615
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
  sha256: dcd2b1a065bbf5c54004ddf6551c775a8eb6993c8298ca8a6b92041ed413f785
  md5: 6dc9e1305e7d3129af4ad0dabda30e56
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  size: 670635
  timestamp: 1749858327854
- conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
  sha256: 412381a43d5ff9bbed82cd52a0bbca5b90623f62e41007c9c42d3870c60945ff
  md5: 9344155d33912347b37f0ae6c410a835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 264243
  timestamp: 1745264221534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
  sha256: 65d5ca837c3ee67b9d769125c21dc857194d7f6181bb0e7bd98ae58597b457d0
  md5: 00290e549c5c8a32cc271020acc9ec6b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - abseil-cpp =20250127.1
  - libabseil-static =20250127.1=cxx17*
  license: Apache-2.0
  license_family: Apache
  size: 1325007
  timestamp: 1742369558286
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-20.0.0-h1b9301b_8_cpu.conda
  build_number: 8
  sha256: e218ae6165e6243d8850352640cee57f06a8d05743647918a0370cc5fcc8b602
  md5: 31fc3235e7c84fe61575041cad3756a8
  depends:
  - __glibc >=2.17,<3.0.a0
  - aws-crt-cpp >=0.32.10,<0.32.11.0a0
  - aws-sdk-cpp >=1.11.510,<1.11.511.0a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-identity-cpp >=1.10.0,<1.10.1.0a0
  - azure-storage-blobs-cpp >=12.13.0,<12.13.1.0a0
  - azure-storage-files-datalake-cpp >=12.12.0,<12.12.1.0a0
  - bzip2 >=1.0.8,<2.0a0
  - glog >=0.7.1,<0.8.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libbrotlidec >=1.1.0,<1.2.0a0
  - libbrotlienc >=1.1.0,<1.2.0a0
  - libgcc >=13
  - libgoogle-cloud >=2.36.0,<2.37.0a0
  - libgoogle-cloud-storage >=2.36.0,<2.37.0a0
  - libopentelemetry-cpp >=1.21.0,<1.22.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libre2-11 >=2024.7.2
  - libstdcxx >=13
  - libutf8proc >=2.10.0,<2.11.0a0
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - orc >=2.1.2,<2.1.3.0a0
  - re2
  - snappy >=1.2.1,<1.3.0a0
  - zstd >=1.5.7,<1.6.0a0
  constrains:
  - parquet-cpp <0.0a0
  - arrow-cpp <0.0a0
  - apache-arrow-proc =*=cpu
  license: Apache-2.0
  size: 9203820
  timestamp: 1750865083349
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-20.0.0-hcb10f89_8_cpu.conda
  build_number: 8
  sha256: 7be0682610864ec3866214b935c9bf8adeda2615e9a663e3bf4fe57ef203fa2d
  md5: a9d337e1f407c5d92e609cb39c803343
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0 h1b9301b_8_cpu
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  size: 642522
  timestamp: 1750865165581
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-20.0.0-hcb10f89_8_cpu.conda
  build_number: 8
  sha256: 23f6a1dc75e8d12478aa683640169ac14baaeb086d1f0ed5bfe96a562a3c5bab
  md5: 14bb8eeeff090f873056fa629d2d82b5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0 h1b9301b_8_cpu
  - libarrow-acero 20.0.0 hcb10f89_8_cpu
  - libgcc >=13
  - libparquet 20.0.0 h081d1f1_8_cpu
  - libstdcxx >=13
  license: Apache-2.0
  size: 607588
  timestamp: 1750865314449
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-20.0.0-h1bed206_8_cpu.conda
  build_number: 8
  sha256: 04f214b1f6d5b35fa89a17cce43f5c321167038d409d1775d7457015c6a26cba
  md5: 8a98f2bf0cf61725f8842ec45dbd7986
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libarrow 20.0.0 h1b9301b_8_cpu
  - libarrow-acero 20.0.0 hcb10f89_8_cpu
  - libarrow-dataset 20.0.0 hcb10f89_8_cpu
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  license: Apache-2.0
  size: 525599
  timestamp: 1750865405214
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
  sha256: e30733a729eb6efd9cb316db0202897c882d46f6c20a0e647b4de8ec921b7218
  md5: 57566a81dd1e5aa3d98ac7582e8bfe03
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-or-later
  size: 53115
  timestamp: 1746228856865
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
  sha256: ccbfc465456133042eea3e8d69bae009893f57a47a786f772c0af382bda7ad99
  md5: 8f66ed2e34507b7ae44afa31c3e4ec79
  depends:
  - __glibc >=2.17,<3.0.a0
  - libasprintf 0.24.1 h8e693c7_0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 34680
  timestamp: 1746228884730
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_hfdb39a5_mkl.conda
  build_number: 32
  sha256: 7a04219d42b3b0b85ed9d019f481e4227efa2baa12ff48547758e90e2e208adc
  md5: eceb19ae9105bc4d0e8d5a321d66c426
  depends:
  - mkl >=2024.2.2,<2025.0a0
  constrains:
  - liblapack  3.9.0   32*_mkl
  - blas 2.132   mkl
  - liblapacke 3.9.0   32*_mkl
  - libcblas   3.9.0   32*_mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  size: 17657
  timestamp: 1750388671003
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_3.conda
  sha256: 462a8ed6a7bb9c5af829ec4b90aab322f8bcd9d8987f793e6986ea873bbd05cf
  md5: cb98af5db26e3f482bebb80ce9d947d3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 69233
  timestamp: 1749230099545
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_3.conda
  sha256: 3eb27c1a589cbfd83731be7c3f19d6d679c7a444c3ba19db6ad8bf49172f3d83
  md5: 1c6eecffad553bde44c5238770cfb7da
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 33148
  timestamp: 1749230111397
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_3.conda
  sha256: 76e8492b0b0a0d222bfd6081cae30612aa9915e4309396fdca936528ccf314b7
  md5: 3facafe58f3858eb95527c7d3a3fc578
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 282657
  timestamp: 1749230124839
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_h372d94f_mkl.conda
  build_number: 32
  sha256: d0449cdfb6c6e993408375bcabbb4c9630a9b8750c406455ce3a4865ec7a321c
  md5: 68b55daaf083682f58d9b7f5d52aeb37
  depends:
  - libblas 3.9.0 32_hfdb39a5_mkl
  constrains:
  - liblapack  3.9.0   32*_mkl
  - liblapacke 3.9.0   32*_mkl
  - blas 2.132   mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  size: 17280
  timestamp: 1750388682101
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
  sha256: fd1d153962764433fe6233f34a72cdeed5dcf8a883a85769e8295ce940b5b0c5
  md5: c965a5aa0d5c1c37ffc62dff36e28400
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 20440
  timestamp: 1633683576494
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.14.1-h332b0f4_0.conda
  sha256: b6c5cf340a4f80d70d64b3a29a7d9885a5918d16a5cb952022820e6d3e79dc8b
  md5: 45f6713cb00f124af300342512219182
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libnghttp2 >=1.64.0,<2.0a0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 449910
  timestamp: 1749033146806
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
  sha256: 8420748ea1cc5f18ecc5068b4f24c7a023cc9b20971c99c824ba10641fb95ddf
  md5: 64f0c503da58ec25ebd359e4d990afa8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 72573
  timestamp: 1747040452262
- conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
  sha256: d789471216e7aba3c184cd054ed61ce3f6dac6f87a50ec69291b9297f8c18724
  md5: c277e0a4d549b03ac1e9d6cbbe3d017b
  depends:
  - ncurses
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 134676
  timestamp: 1738479519902
- conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
  sha256: 1cd6048169fa0395af74ed5d8f1716e22c19a81a8a36f934c110ca3ad4dd27b4
  md5: 172bf1cd1ff8629f2b1179945ed45055
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 112766
  timestamp: 1702146165126
- conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
  sha256: 2e14399d81fb348e9d231a82ca4d816bf855206923759b69ad006ba482764131
  md5: a1cfcc585f0c42bf8d5546bb1dfb668d
  depends:
  - libgcc-ng >=12
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 427426
  timestamp: 1685725977222
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
  sha256: 33ab03438aee65d6aa667cf7d90c91e5e7d734c19a67aa4c7040742c0a13d505
  md5: db0bfbe7dd197b68ad5f30333bae6ce0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  size: 74427
  timestamp: 1743431794976
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
  sha256: 65908b75fa7003167b8a8f0001e11e58ed5b1ef5e98b96ab2ba66d7c1b822c7d
  md5: ee48bf17cc83a00f59ca1494d5646869
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 394383
  timestamp: 1687765514062
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
  sha256: 7be9b3dac469fe3c6146ff24398b685804dfc7a1de37607b84abd076f57cc115
  md5: 51f5be229d83ecd401fb369ab96ae669
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7693
  timestamp: 1745369988361
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
  sha256: 7759bd5c31efe5fbc36a7a1f8ca5244c2eabdbeb8fc1bee4b99cf989f35c7d81
  md5: 3c255be50a506c50765a93a6644f32fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 380134
  timestamp: 1745369987697
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
  sha256: 59a87161212abe8acc57d318b0cc8636eb834cdfdfddcf1f588b5493644b39a3
  md5: 9e60c55e725c20d23125a5f0dd69af5d
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_3
  - libgomp 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 824921
  timestamp: 1750808216066
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
  sha256: b0b0a5ee6ce645a09578fc1cb70c180723346f8a45fdb6d23b3520591c6d6996
  md5: e66f2b8ad787e7beb0f846e4bd7e8493
  depends:
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29033
  timestamp: 1750808224854
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
  sha256: 104f2341546e295d1136ab3010e81391bd3fd5be0f095db59266e8eba2082d37
  md5: 2ee6d71b72f75d50581f2f68e965efdb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 171165
  timestamp: 1746228870846
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
  sha256: a9a0cba030778eb2944a1f235dba51e503b66f8be0ce6f55f745173a515c3644
  md5: 8f04c7aae6a46503bc36d1ed5abc8c7c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 37234
  timestamp: 1746228897993
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
  sha256: 77dd1f1efd327e6991e87f09c7c97c4ae1cfbe59d9485c41d339d6391ac9c183
  md5: bfbca721fd33188ef923dfe9ba172f29
  depends:
  - libgfortran5 15.1.0 hcea5267_3
  constrains:
  - libgfortran-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29057
  timestamp: 1750808257258
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
  sha256: eea6c3cf22ad739c279b4d665e6cf20f8081f483b26a96ddd67d4df3c88dfa0a
  md5: 530566b68c3b8ce7eec4cd047eae19fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 1565627
  timestamp: 1750808236464
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.36.0-hc4361e1_1.conda
  sha256: 3a56c653231d6233de5853dc01f07afad6a332799a39c3772c0948d2e68547e4
  md5: ae36e6296a8dd8e8a9a8375965bf6398
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libcurl >=8.12.1,<9.0a0
  - libgcc >=13
  - libgrpc >=1.71.0,<1.72.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - openssl >=3.4.1,<4.0a0
  constrains:
  - libgoogle-cloud 2.36.0 *_1
  license: Apache-2.0
  license_family: Apache
  size: 1246764
  timestamp: 1741878603939
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.36.0-h0121fbd_1.conda
  sha256: 54235d990009417bb20071f5ce7c8dcf186b19fa7d24d72bc5efd2ffb108001c
  md5: a0f7588c1f0a26d550e7bae4fb49427a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil
  - libcrc32c >=1.1.2,<1.2.0a0
  - libcurl
  - libgcc >=13
  - libgoogle-cloud 2.36.0 hc4361e1_1
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl
  license: Apache-2.0
  license_family: Apache
  size: 785719
  timestamp: 1741878763994
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.71.0-h8e591d7_1.conda
  sha256: 37267300b25f292a6024d7fd9331085fe4943897940263c3a41d6493283b2a18
  md5: c3cfd72cbb14113abee7bbd86f44ad69
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.34.5,<2.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libre2-11 >=2024.7.2
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - re2
  constrains:
  - grpc-cpp =1.71.0
  license: Apache-2.0
  license_family: APACHE
  size: 7920187
  timestamp: 1745229332239
- conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
  sha256: d14c016482e1409ae1c50109a9ff933460a50940d2682e745ab1c172b5282a69
  md5: 804ca9e91bcaea0824a341d55b1684f2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.13.4,<2.14.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 2423200
  timestamp: 1731374922090
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
  sha256: 98b399287e27768bf79d48faba8a99a2289748c65cd342ca21033fab1860d4a4
  md5: 9fa334557db9f63da6c9285fd2a48638
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 628947
  timestamp: 1745268527144
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_hc41d3b0_mkl.conda
  build_number: 32
  sha256: dc1be931203a71f5c84887cde24659fdd6fda73eb8c6cf56e67b68e3c7916efd
  md5: 6dc827963c12f90c79f5b2be4eaea072
  depends:
  - libblas 3.9.0 32_hfdb39a5_mkl
  constrains:
  - liblapacke 3.9.0   32*_mkl
  - blas 2.132   mkl
  - libcblas   3.9.0   32*_mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  size: 17284
  timestamp: 1750388691797
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
  sha256: b0f2b3695b13a989f75d8fd7f4778e1c7aabe3b36db83f0fe80b2cd812c0e975
  md5: 19e57602824042dfd0446292ef90488b
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.32.3,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 647599
  timestamp: 1729571887612
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
  sha256: 927fe72b054277cde6cb82597d0fcf6baf127dcbce2e0a9d8925a68f1265eef5
  md5: d864d34357c3b65a4b731f78c0801dc4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  license_family: GPL
  size: 33731
  timestamp: 1750274110928
- conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
  sha256: ffb066ddf2e76953f92e06677021c73c85536098f1c21fcd15360dbc859e22e4
  md5: 68e52064ed3897463c0e958ab5c8f91b
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  size: 218500
  timestamp: 1745825989535
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-1.21.0-hd1b1c89_0.conda
  sha256: b88de51fa55513483e7c80c43d38ddd3559f8d17921879e4c99909ba66e1c16b
  md5: 4b25cd8720fd8d5319206e4f899f2707
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libcurl >=8.14.0,<9.0a0
  - libgrpc >=1.71.0,<1.72.0a0
  - libopentelemetry-cpp-headers 1.21.0 ha770c72_0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libzlib >=1.3.1,<2.0a0
  - nlohmann_json
  - prometheus-cpp >=1.3.0,<1.4.0a0
  constrains:
  - cpp-opentelemetry-sdk =1.21.0
  license: Apache-2.0
  license_family: APACHE
  size: 882002
  timestamp: 1748592427188
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-headers-1.21.0-ha770c72_0.conda
  sha256: dbd811e7a7bd9b96fccffe795ba539ac6ffcc5e564d0bec607f62aa27fa86a17
  md5: ********************************
  license: Apache-2.0
  license_family: APACHE
  size: 359509
  timestamp: 1748592389311
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
  sha256: 786d43678d6d1dc5f88a6bad2d02830cfd5a0184e84a8caa45694049f0e3ea5f
  md5: b64523fb87ac6f87f0790f324ad43046
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  size: 312472
  timestamp: 1744330953241
- conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-20.0.0-h081d1f1_8_cpu.conda
  build_number: 8
  sha256: c3bc9454b25f8d32db047c282645ae33fe96b5d4d9bde66099fb49cf7a6aa90c
  md5: d64065a5ab0a8d466b7431049e531995
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0 h1b9301b_8_cpu
  - libgcc >=13
  - libstdcxx >=13
  - libthrift >=0.21.0,<0.21.1.0a0
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  size: 1244187
  timestamp: 1750865279989
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
  sha256: c8f5dc929ba5fcee525a66777498e03bbcbfefc05a0773e5163bb08ac5122f1a
  md5: 37511c874cf3b8d0034c8d24e73c0884
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 289506
  timestamp: 1750095629466
- conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
  sha256: 691af28446345674c6b3fb864d0e1a1574b6cc2f788e0f036d73a6b05dcf81cf
  md5: edb86556cf4a0c133e7932a1597ff236
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 3358788
  timestamp: 1745159546868
- conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2025.06.26-hba17884_0.conda
  sha256: 89535af669f63e0dc4ae75a5fc9abb69b724b35e0f2ca0304c3d9744a55c8310
  md5: f6881c04e6617ebba22d237c36f1b88e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - re2 2025.06.26.*
  license: BSD-3-Clause
  size: 211720
  timestamp: 1751053073521
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsentencepiece-0.2.0-he636bdd_11.conda
  sha256: c5b98351daa23979a6728d297bf3b3eaae0324ae60487f5637b09a9ed7656d43
  md5: aed2d089d7d343500921f9ad3f7ba9c8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 826579
  timestamp: 1741898316659
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
  sha256: f709cbede3d4f3aee4e2f8d60bd9e256057f410bd60b8964cb8cf82ec1457573
  md5: ef1910918dd895516a769ed36b5b3a4e
  depends:
  - lame >=3.100,<3.101.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 354372
  timestamp: 1695747735668
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
  sha256: 0105bd108f19ea8e6a78d2d994a6d4a8db16d19a41212070d2d1d48a63c34161
  md5: a587892d3c13b6621a6091be690dbca2
  depends:
  - libgcc-ng >=12
  license: ISC
  size: 205978
  timestamp: 1716828628198
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
  sha256: 9a9e5bf30178f821d4f8de25eac0ae848915bfde6a78a66ae8b77d9c33d9d0e5
  md5: c7c4888059a8324e52de475d1e7bdc53
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  size: 919723
  timestamp: 1750925531920
- conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
  sha256: fa39bfd69228a13e553bd24601332b7cfeb30ca11a3ca50bb028108fe90a7661
  md5: eecce068c7e4eddeb169591baac20ac4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 304790
  timestamp: 1745608545575
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
  sha256: 7650837344b7850b62fdba02155da0b159cf472b9ab59eb7b472f7bd01dff241
  md5: 6d11a5edae89fe413c0569f16d308f5a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 3896407
  timestamp: 1750808251302
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
  sha256: bbaea1ecf973a7836f92b8ebecc94d3c758414f4de39d2cc6818a3d10cb3216b
  md5: 57541755b5a51691955012b8e197c06c
  depends:
  - libstdcxx 15.1.0 h8f9b012_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29093
  timestamp: 1750808292700
- conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.21.0-h0e7cc3e_0.conda
  sha256: ebb395232973c18745b86c9a399a4725b2c39293c9a91b8e59251be013db42f0
  md5: dcb95c0a98ba9ff737f7ae482aef7833
  depends:
  - __glibc >=2.17,<3.0.a0
  - libevent >=2.1.12,<2.1.13.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 425773
  timestamp: 1727205853307
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
  sha256: 7fa6ddac72e0d803bb08e55090a8f2e71769f1eb7adbd5711bdd7789561601b1
  md5: e79a094918988bb1807462cd42c83962
  depends:
  - __glibc >=2.17,<3.0.a0
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 429575
  timestamp: 1747067001268
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtorch-2.7.1-cpu_mkl_hb1c5dc7_100.conda
  sha256: e221eaa1b3caf0e228cc7fa296d17708b5f0099122084f539e4b75844789f4e9
  md5: 80bf999d61d95328cb37391ccdb9f03d
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libblas * *mkl
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libuv >=1.51.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.7
  - mkl >=2024.2.2,<2025.0a0
  - sleef >=3.8,<4.0a0
  constrains:
  - pytorch 2.7.1 cpu_mkl_*_100
  - pytorch-gpu <0.0a0
  - pytorch-cpu 2.7.1
  license: BSD-3-Clause
  license_family: BSD
  size: 55596081
  timestamp: 1750205154609
- conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.10.0-h202a827_0.conda
  sha256: c4ca78341abb308134e605476d170d6f00deba1ec71b0b760326f36778972c0e
  md5: 0f98f3e95272d118f7931b6bef69bfe5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 83080
  timestamp: 1748341697686
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb9d3cd8_0.conda
  sha256: 770ca175d64323976c9fe4303042126b2b01c1bd54c8c96cafeaba81bdb481b8
  md5: 1349c022c92c5efd3fd705a79a5804d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 890145
  timestamp: 1748304699136
- conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
  sha256: 53080d72388a57b3c31ad5805c93a7328e46ff22fab7c44ad2a86d712740af33
  md5: 309dec04b70a3cc0f1e84a4013683bc0
  depends:
  - libgcc-ng >=9.3.0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  size: 286280
  timestamp: 1610609811627
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
  sha256: c45283fd3e90df5f0bd3dbcd31f59cdd2b001d424cf30a07223655413b158eaf
  md5: 63f790534398730f59e1b899c3644d4a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  size: 429973
  timestamp: 1734777489810
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  size: 100393
  timestamp: 1702724383534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
  sha256: b0b3a96791fa8bb4ec030295e8c8bf2d3278f33c0f9ad540e73b5e538e6268e7
  md5: 14dbe05b929e329dbaa6f2d0aa19466d
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 690864
  timestamp: 1746634244154
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-20.1.7-h024ca30_0.conda
  sha256: 10f2f6be8ba4c018e1fc741637a8d45c0e58bea96954c25e91fbe4238b7c9f60
  md5: b9c9b2f494533250a9eb7ece830f4422
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - openmp 20.1.7|20.1.7.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  size: 4165732
  timestamp: 1749892194931
- conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
  sha256: 47326f811392a5fd3055f0f773036c392d26fdb32e4d8e7a8197eed951489346
  md5: 9de5350a85c4a20c685259b889aa6393
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 167055
  timestamp: 1733741040117
- conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
  sha256: 0fbacdfb31e55964152b24d5567e9a9996e1e7902fb08eb7d91b5fd6ce60803a
  md5: fee3164ac23dfca50cfcc8b85ddefb81
  depends:
  - mdurl >=0.1,<1
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 64430
  timestamp: 1733250550053
- conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py312h178313f_1.conda
  sha256: 4a6bf68d2a2b669fecc9a4a009abd1cf8e72c2289522ff00d81b5a6e51ae78f5
  md5: eb227c3e0bf58f5bd69c0532b157975b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  size: 24604
  timestamp: 1733219911494
- conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025062805-release.conda
  sha256: 77172c624b09ee52cfe91be10c6782985e34b29c2beaf0cfae8f2e7d39b12d5a
  depends:
  - mblack ==25.5.0.dev2025062805 release
  license: LicenseRef-Modular-Proprietary
  size: 223160182
  timestamp: 1751087855303
- conda: https://conda.modular.com/max-nightly/noarch/max-pipelines-25.5.0.dev2025062805-release.conda
  noarch: python
  sha256: 1d19eba2beeebaa5ca50a15027986232f7043c09c33ccb066ce46cb2c4482bb0
  depends:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  - max-python ==25.5.0.dev2025062805 release
  license: LicenseRef-Modular-Proprietary
  size: 10027
  timestamp: 1751087843810
- conda: https://conda.modular.com/max-nightly/linux-64/max-python-25.5.0.dev2025062805-release.conda
  noarch: python
  sha256: 413309056d9a99425703b30672a11174353b04c73d7357185003f7e375a56cd4
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025062805 release
  constrains:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 30511723
  timestamp: 1751087855303
- conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062805-release.conda
  noarch: python
  sha256: 3975f444aec9785842f5afcfba4ae455131fac18ad6c6af343d7d5cd2fd1d3b8
  depends:
  - python >=3.9,<3.14
  - click >=8.0.0
  - mypy_extensions >=0.4.3
  - packaging >=22.0
  - pathspec >=0.9.0
  - platformdirs >=2
  - typing_extensions >=v4.12.2
  - python
  license: MIT
  size: 131263
  timestamp: 1751087843809
- conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
  sha256: 78c1bbe1723449c52b7a9df1af2ee5f005209f67e40b6e1d3c7619127c43b1c7
  md5: 592132998493b3ff25fd7479396e8351
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 14465
  timestamp: 1733255681319
- conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2024.2.2-ha957f24_16.conda
  sha256: 77906b0acead8f86b489da46f53916e624897338770dbf70b04b8f673c9273c1
  md5: 1459379c79dda834673426504d52b319
  depends:
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - llvm-openmp >=19.1.2
  - tbb 2021.*
  license: LicenseRef-IntelSimplifiedSoftwareOct2022
  license_family: Proprietary
  size: 124718448
  timestamp: 1730231808335
- conda: https://conda.modular.com/max-nightly/noarch/modular-25.5.0.dev2025062805-release.conda
  noarch: python
  sha256: 4a9375907226bd72106242fe2a10b6824df6f90d8e8fef3385f6689ce6d30599
  depends:
  - max-pipelines ==25.5.0.dev2025062805 release
  license: LicenseRef-Modular-Proprietary
  size: 9388
  timestamp: 1751087843810
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-h24ddda3_1.conda
  sha256: 1bf794ddf2c8b3a3e14ae182577c624fa92dea975537accff4bc7e5fea085212
  md5: aa14b9a5196a6d8dd364164b7ce56acf
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpfr >=4.2.1,<5.0a0
  license: LGPL-3.0-or-later
  license_family: LGPL
  size: 116777
  timestamp: 1725629179524
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
  sha256: f25d2474dd557ca66c6231c8f5ace5af312efde1ba8290a6ea5e1732a4e669c0
  md5: 2eeb50cab6652538eee8fc0bc3340c81
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  license: LGPL-3.0-only
  license_family: LGPL
  size: 634751
  timestamp: 1725746740014
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
  sha256: 39c4700fb3fbe403a77d8cc27352fa72ba744db487559d5d44bf8411bb4ea200
  md5: c7f302fd11eeb0987a6a5e1f3aed6a21
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-only
  license_family: LGPL
  size: 491140
  timestamp: 1730581373280
- conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
  sha256: 7d7aa3fcd6f42b76bd711182f3776a02bef09a68c5f117d66b712a6d81368692
  md5: 3585aa87c43ab15b167b574cd73b057b
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 439705
  timestamp: 1733302781386
- conda: https://conda.anaconda.org/conda-forge/linux-64/msgspec-0.19.0-py312h66e93f0_1.conda
  sha256: 04e229900bdbb048bee290481626b8e9baebbde215609e42414dbd63454222c7
  md5: e4b5d3459269d8df14beec369cca66fd
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 211752
  timestamp: 1736368259541
- conda: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.6.0-py312h178313f_0.conda
  sha256: a51aad4f15e9719f930883548b86f9b054c8bbc1fd60d641a7f364bb102fbf09
  md5: 1f707aeb79342d79881d44552ddab8e2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  size: 96554
  timestamp: 1751089445335
- conda: https://conda.anaconda.org/conda-forge/linux-64/multiprocess-0.70.15-py312h98912ed_1.conda
  sha256: bb612a921fafda6375a2204ffebd8811db8dd3b8f25ac9886cc9bcbff7e3664e
  md5: 5a64b9f44790d9a187a85366dd0ffa8d
  depends:
  - dill >=0.3.6
  - libgcc-ng >=12
  - python >=3.12.0rc3,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 335666
  timestamp: 1695459025249
- conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
  sha256: 6ed158e4e5dd8f6a10ad9e525631e35cee8557718f83de7a4e3966b1f772c4b1
  md5: e9c622e0d00fa24a6292279af3ab6d06
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11766
  timestamp: 1745776666688
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.5-pyhe01879c_0.conda
  sha256: 02019191a2597865940394ff42418b37bc585a03a1c643d7cea9981774de2128
  md5: 16bff3d37a4f99e3aa089c36c2b8d650
  depends:
  - python >=3.11
  - python
  constrains:
  - numpy >=1.25
  - scipy >=1.11.2
  - matplotlib >=3.8
  - pandas >=2.0
  license: BSD-3-Clause
  license_family: BSD
  size: 1564462
  timestamp: 1749078300258
- conda: https://conda.anaconda.org/conda-forge/linux-64/ninja-1.13.0-h7aa8ee6_0.conda
  sha256: 8cf09470430b5aba5165c7aefed070d2c8f998f69fede01197ef838bf17fa81a
  md5: 2f67cb5c5ec172faeba94348ae8af444
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  license: Apache-2.0
  license_family: APACHE
  size: 180917
  timestamp: 1750273173789
- conda: https://conda.anaconda.org/conda-forge/linux-64/nlohmann_json-3.12.0-h3f2d84a_0.conda
  sha256: e2fc624d6f9b2f1b695b6be6b905844613e813aa180520e73365062683fe7b49
  md5: d76872d096d063e226482c99337209dc
  license: MIT
  license_family: MIT
  size: 135906
  timestamp: 1744445169928
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.0-py312h6cf2f7f_0.conda
  sha256: 59da92a150737e830c75e8de56c149d6dc4e42c9d38ba30d2f0d4787a0c43342
  md5: 8b4095ed29d1072f7e4badfbaf9e5851
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 8417476
  timestamp: 1749430957684
- conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
  sha256: 5bee706ea5ba453ed7fd9da7da8380dd88b865c8d30b5aaec14d2b6dd32dbc39
  md5: 9e5816bc95d285c115a3ebc2f8563564
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.44,<1.7.0a0
  - libstdcxx >=13
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 342988
  timestamp: 1733816638720
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
  sha256: b4491077c494dbf0b5eaa6d87738c22f2154e9277e5293175ec187634bd808a0
  md5: de356753cfdbffcde5bb1e86e3aa6cd0
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 3117410
  timestamp: 1746223723843
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-api-1.30.0-pyhd8ed1ab_0.conda
  sha256: ab5b873cc2542e305236e0439d323bf880d1b49eafe7a8a04d6863a5a409059d
  md5: 65caf9f399f67c1c16efc0fb76f3576c
  depends:
  - deprecated >=1.2.6
  - importlib-metadata <=8.5.0,>=6.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 44649
  timestamp: 1738813183426
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-otlp-proto-common-1.30.0-pyhd8ed1ab_0.conda
  sha256: 12294c0e75460bf773500d0b97d58701f6141a26ebc7507b7aefd045c3943801
  md5: 49cf930ab95d2abaabb17f0d40899628
  depends:
  - backoff >=1.10.0,<3.0.0
  - opentelemetry-proto 1.30.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 19155
  timestamp: 1738757443981
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-otlp-proto-http-1.30.0-pyhd8ed1ab_0.conda
  sha256: 4bdb101663f63cd9b9c5560b40cc4441fd766885a13a130c9a0b60addc493f7d
  md5: 19cd3caf8abaabe7210cb46dbde1932b
  depends:
  - deprecated >=1.2.6
  - googleapis-common-protos ~=1.52
  - opentelemetry-api ~=1.15
  - opentelemetry-exporter-otlp-proto-common 1.30.0
  - opentelemetry-proto 1.30.0
  - opentelemetry-sdk >=1.30.0,<1.31.dev0
  - python >=3.9
  - requests ~=2.7
  license: Apache-2.0
  license_family: APACHE
  size: 17129
  timestamp: 1738881064907
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-prometheus-0.51b0-pyh29332c3_0.conda
  sha256: 1b7420798c2e8589ea9c03a0b9e1db64a79dfbdf6d12937ad69fd70f9b2a6ec7
  md5: cbfa34f2b05a9f916b4e255416103082
  depends:
  - python >=3.9
  - opentelemetry-api >=1.12,<2.dev0
  - opentelemetry-sdk >=1.30.0,<1.31.dev0
  - prometheus_client >=0.5.0,<1.0.0
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 22659
  timestamp: 1740444975350
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-proto-1.30.0-pyhd8ed1ab_0.conda
  sha256: a7dfb1566daca6bd7aea78ccc69257324336fa945199081c9a9d378d1b59737b
  md5: 6b2133dbe9127cd09d3c20061c8f4faa
  depends:
  - protobuf <6.0,>=5.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 37366
  timestamp: 1738753488553
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-sdk-1.30.0-pyhd8ed1ab_0.conda
  sha256: 287fac5c7a0a0a565932b7a43b733036bbc08e2b11cd93fb64f29ededb89e9c6
  md5: 3667728a1b18fb9a32aef23036934d9f
  depends:
  - opentelemetry-api 1.30.0
  - opentelemetry-semantic-conventions 0.51b0
  - python >=3.9
  - typing-extensions >=3.7.4
  - typing_extensions >=3.7.4
  license: Apache-2.0
  license_family: APACHE
  size: 77853
  timestamp: 1738855492720
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-semantic-conventions-0.51b0-pyh3cfb1c2_0.conda
  sha256: 2618877c066a6608ea57a05d260ddd805d0f59b95076f63d816df139f648f25a
  md5: ff41e8547f2e7e0d0affc6c6e6e0e87f
  depends:
  - deprecated >=1.2.6
  - opentelemetry-api 1.30.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 91903
  timestamp: 1738850427906
- conda: https://conda.anaconda.org/conda-forge/linux-64/optree-0.16.0-py312h68727a3_0.conda
  sha256: 64f702420ed3642eb68026e8486beb3571cd853f14c58d2c6c7392391fecf171
  md5: 0d981a6b5671f1013ff2e682fee925c2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - typing-extensions >=4.6
  license: Apache-2.0
  license_family: Apache
  size: 425716
  timestamp: 1748442635056
- conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.1.2-h17f744e_0.conda
  sha256: f6ff644e27f42f2beb877773ba3adc1228dbb43530dbe9426dd672f3b847c7c5
  md5: ef7f9897a244b2023a066c22a1089ce4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - snappy >=1.2.1,<1.3.0a0
  - tzdata
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0
  license_family: Apache
  size: 1242887
  timestamp: 1746604310927
- conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
  sha256: 289861ed0c13a15d7bbb408796af4de72c2fe67e2bcb0de98f4c3fce259d7991
  md5: 58335b26c38bf4a20f399384c33cbcf9
  depends:
  - python >=3.8
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 62477
  timestamp: 1745345660407
- conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.0-py312hf9745cd_0.conda
  sha256: 44f5587c1e1a9f0257387dd18735bcf65a67a6089e723302dc7947be09d9affe
  md5: ac82ac336dbe61106e21fb2e11704459
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.19,<3
  - numpy >=1.22.4
  - python >=3.12,<3.13.0a0
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.12.* *_cp312
  - pytz >=2020.1
  constrains:
  - bottleneck >=1.3.6
  - blosc >=1.21.3
  - numba >=0.56.4
  - pyqt5 >=5.15.9
  - pyarrow >=10.0.1
  - gcsfs >=2022.11.0
  - xlsxwriter >=3.0.5
  - scipy >=1.10.0
  - beautifulsoup4 >=4.11.2
  - numexpr >=2.8.4
  - fastparquet >=2022.12.0
  - lxml >=4.9.2
  - xlrd >=2.0.1
  - openpyxl >=3.1.0
  - qtpy >=2.3.0
  - s3fs >=2022.11.0
  - pandas-gbq >=0.19.0
  - pytables >=3.8.0
  - python-calamine >=0.1.7
  - fsspec >=2022.11.0
  - psycopg2 >=2.9.6
  - xarray >=2022.12.0
  - matplotlib >=3.6.3
  - pyxlsb >=1.0.10
  - tabulate >=0.9.0
  - odfpy >=1.4.1
  - pyreadstat >=1.2.0
  - html5lib >=1.1
  - zstandard >=0.19.0
  - sqlalchemy >=2.0.0
  - tzdata >=2022.7
  license: BSD-3-Clause
  license_family: BSD
  size: 14958450
  timestamp: 1749100123120
- conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9f64009cdf5b8e529995f18e03665b03f5d07c0b17445b8badef45bde76249ee
  md5: 617f15191456cc6a13db418a275435e5
  depends:
  - python >=3.9
  license: MPL-2.0
  license_family: MOZILLA
  size: 41075
  timestamp: 1733233471940
- conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.2.1-py312h80c1187_0.conda
  sha256: 15f32ec89f3a7104fcb190546a2bc0fc279372d9073e5ec08a8d61a1c79af4c0
  md5: ca438bf57e4f2423d261987fe423a0dd
  depends:
  - __glibc >=2.17,<3.0.a0
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  size: 42506161
  timestamp: 1746646366556
- conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
  sha256: 0f48999a28019c329cd3f6fd2f01f09fc32cc832f7d6bbe38087ddac858feaa3
  md5: 424844562f5d337077b445ec6b1398a7
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 23531
  timestamp: 1746710438805
- conda: https://conda.anaconda.org/conda-forge/noarch/prometheus-async-25.1.0-pyh29332c3_0.conda
  sha256: 5c37398c07f9448f7b7e596afc396d22cca47757f13c675b942b7e38e966b34d
  md5: d6a627b93684a84fe079195ec25105fc
  depends:
  - python >=3.9
  - prometheus_client >=0.8.0
  - typing_extensions >=3.10.0
  - wrapt
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 25360
  timestamp: 1743449979148
- conda: https://conda.anaconda.org/conda-forge/linux-64/prometheus-cpp-1.3.0-ha5d0236_0.conda
  sha256: 013669433eb447548f21c3c6b16b2ed64356f726b5f77c1b39d5ba17a8a4b8bc
  md5: a83f6a2fdc079e643237887a37460668
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.10.1,<9.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: MIT
  license_family: MIT
  size: 199544
  timestamp: 1730769112346
- conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
  sha256: 454e2c0ef14accc888dd2cd2e8adb8c6a3a607d2d3c2f93962698b5718e6176d
  md5: c64b77ccab10b822722904d889fa83b5
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 52641
  timestamp: 1748896836631
- conda: https://conda.anaconda.org/conda-forge/linux-64/propcache-0.3.1-py312h178313f_0.conda
  sha256: d0ff67d89cf379a9f0367f563320621f0bc3969fe7f5c85e020f437de0927bb4
  md5: 0cf580c1b73146bb9ff1bbdb4d4c8cf9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: APACHE
  size: 54233
  timestamp: 1744525107433
- conda: https://conda.anaconda.org/conda-forge/linux-64/protobuf-5.29.3-py312h0f4f066_0.conda
  sha256: 8f896488bb5b21b47e72edb743c740fdc74d4d8bfc2178d07ff15f20d0d086df
  md5: 4c412df32064636d9ebac1be3dd4cdbf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - libprotobuf 5.29.3
  license: BSD-3-Clause
  license_family: BSD
  size: 478887
  timestamp: 1741125776561
- conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py312h66e93f0_0.conda
  sha256: 158047d7a80e588c846437566d0df64cec5b0284c7184ceb4f3c540271406888
  md5: 8e30db4239508a538e4a3b3cdf5b9616
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 466219
  timestamp: 1740663246825
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-20.0.0-py312h7900ff3_0.conda
  sha256: f7b08ff9ef4626e19a3cd08165ca1672675168fa9af9c2b0d2a5c104c71baf01
  md5: 57b626b4232b77ee6410c7c03a99774d
  depends:
  - libarrow-acero 20.0.0.*
  - libarrow-dataset 20.0.0.*
  - libarrow-substrait 20.0.0.*
  - libparquet 20.0.0.*
  - pyarrow-core 20.0.0 *_0_*
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: APACHE
  size: 25757
  timestamp: 1746001175919
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-core-20.0.0-py312h01725c0_0_cpu.conda
  sha256: afd636ecaea60e1ebb422b1a3e5a5b8f6f28da3311b7079cbd5caa4464a50a48
  md5: 9b1b453cdb91a2f24fb0257bbec798af
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0.* *cpu
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - apache-arrow-proc * cpu
  - numpy >=1.21,<3
  license: Apache-2.0
  license_family: APACHE
  size: 4658639
  timestamp: 1746000738593
- conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-2.13.6-pyhc790b64_3.conda
  sha256: d429f6f255fbe49f09b9ae1377aa8cbc4d9285b8b220c17ae2ad9c4894c91317
  md5: 1594696beebf1ecb6d29a1136f859a74
  depends:
  - pybind11-global 2.13.6 *_3
  - python >=3.9
  constrains:
  - pybind11-abi ==4
  license: BSD-3-Clause
  license_family: BSD
  size: 186821
  timestamp: 1747935138653
- conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-2.13.6-pyh217bc35_3.conda
  sha256: c044cfcbe6ef0062d0960e9f9f0de5f8818cec84ed901219ff9994b9a9e57237
  md5: 730a5284e26d6bdb73332dafb26aec82
  depends:
  - __unix
  - python >=3.9
  constrains:
  - pybind11-abi ==4
  license: BSD-3-Clause
  license_family: BSD
  size: 180116
  timestamp: 1747934418811
- conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
  sha256: 79db7928d13fab2d892592223d7570f5061c192f27b9febd1a418427b719acc6
  md5: 12c566707c80111f9799308d9e265aef
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 110100
  timestamp: 1733195786147
- conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-2.11.7-pyh3cfb1c2_0.conda
  sha256: ee7823e8bc227f804307169870905ce062531d36c1dcf3d431acd65c6e0bd674
  md5: 1b337e3d378cde62889bb735c024b7a2
  depends:
  - annotated-types >=0.6.0
  - pydantic-core 2.33.2
  - python >=3.9
  - typing-extensions >=4.6.1
  - typing-inspection >=0.4.0
  - typing_extensions >=4.12.2
  license: MIT
  license_family: MIT
  size: 307333
  timestamp: 1749927245525
- conda: https://conda.anaconda.org/conda-forge/linux-64/pydantic-core-2.33.2-py312h680f630_0.conda
  sha256: 4d14d7634c8f351ff1e63d733f6bb15cba9a0ec77e468b0de9102014a4ddc103
  md5: cfbd96e5a0182dfb4110fc42dda63e57
  depends:
  - python
  - typing-extensions >=4.6.0,!=4.7.0
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python_abi 3.12.* *_cp312
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  size: 1890081
  timestamp: 1746625309715
- conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-settings-2.10.1-pyh3cfb1c2_0.conda
  sha256: e56b9a0320e3cab58b88f62ccdcd4bf7cd89ec348c878e1843d4d22315bfced1
  md5: a5f9c3e867917c62d796c20dba792cbd
  depends:
  - pydantic >=2.7.0
  - python >=3.9
  - python-dotenv >=0.21.0
  - typing-inspection >=0.4.0
  license: MIT
  size: 38816
  timestamp: 1750801673349
- conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
  sha256: 5577623b9f6685ece2697c6eb7511b4c9ac5fb607c9babc2646c811b428fd46a
  md5: 6b6ece66ebcae2d5f326c77ef2c5a066
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  size: 889287
  timestamp: 1750615908735
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyinstrument-5.0.2-py312h66e93f0_0.conda
  sha256: 5c3a01095cb3dbb2acc6f218ba8c16cf6cee821220e985d92fa5e5e9f84aa2d5
  md5: ba356edae556ad919cfd14ea03fa4d4a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 184288
  timestamp: 1748126832356
- conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
  sha256: ba3b032fa52709ce0d9fd388f63d330a026754587a2f461117cac9ab73d8d0d8
  md5: 461219d1a5bd61342293efa2c0c90eac
  depends:
  - __unix
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 21085
  timestamp: 1733217331982
- conda: https://conda.anaconda.org/conda-forge/noarch/pysoundfile-0.13.1-pyhd8ed1ab_0.conda
  sha256: 09379ca52a8b119013acc325df3c609526094f923a9b3e3ac297404ff24dbba1
  md5: 41d15bdedf4987e95200d8a980294915
  depends:
  - cffi
  - libsndfile >=1.2
  - numpy
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 31155
  timestamp: 1737836367601
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.11-h9e4cc4f_0_cpython.conda
  sha256: 6cca004806ceceea9585d4d655059e951152fc774a471593d4f5138e6a54c81d
  md5: 94206474a5608243a10c92cefbe0908f
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  size: 31445023
  timestamp: 1749050216615
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
  sha256: d6a17ece93bbd5139e02d2bd7dbfa80bee1a4261dced63f65f679121686bf664
  md5: 5b8d21249ff20967101ffa321cab24e8
  depends:
  - python >=3.9
  - six >=1.5
  - python
  license: Apache-2.0
  size: 233310
  timestamp: 1751104122689
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dotenv-1.1.1-pyhe01879c_0.conda
  sha256: 9a90570085bedf4c6514bcd575456652c47918ff3d7b383349e26192a4805cc8
  md5: a245b3c04afa11e2e52a0db91550da7c
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  size: 26031
  timestamp: 1750789290754
- conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
  sha256: b8afeaefe409d61fa4b68513b25a66bb17f3ca430d67cfea51083c7bfbe098ef
  md5: 859c6bec94cd74119f12b961aba965a8
  depends:
  - cpython 3.12.11.*
  - python_abi * *_cp312
  license: Python-2.0
  size: 45836
  timestamp: 1749047798827
- conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
  sha256: 4790787fe1f4e8da616edca4acf6a4f8ed4e7c6967aa31b920208fc8f95efcca
  md5: a61bf9ec79426938ff785eb69dbb1960
  depends:
  - python >=3.6
  license: BSD-2-Clause
  license_family: BSD
  size: 13383
  timestamp: 1677079727691
- conda: https://conda.anaconda.org/conda-forge/noarch/python-multipart-0.0.20-pyhff2d567_0.conda
  sha256: 1b03678d145b1675b757cba165a0d9803885807792f7eb4495e48a38858c3cca
  md5: a28c984e0429aff3ab7386f7de56de6f
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 27913
  timestamp: 1734420869885
- conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
  sha256: e8392a8044d56ad017c08fec2b0eb10ae3d1235ac967d0aab8bd7b41c4a5eaf0
  md5: 88476ae6ebd24f39261e0854ac244f33
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 144160
  timestamp: 1742745254292
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.5.0-py312h66e93f0_2.conda
  sha256: b5950a737d200e2e3cf199ab7b474ac194fcf4d6bee13bcbdf32c5a5cca7eaf0
  md5: cc3f6c452697c1cf7e4e6e5f21861f96
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - xxhash >=0.8.3,<0.8.4.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 23216
  timestamp: 1740594909669
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-7_cp312.conda
  build_number: 7
  sha256: a1bbced35e0df66cc713105344263570e835625c28d1bdee8f748f482b2d7793
  md5: 0dfcdc155cf23812a0c9deada86fb723
  constrains:
  - python 3.12.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  size: 6971
  timestamp: 1745258861359
- conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-2.7.1-cpu_mkl_py312_h6a7998d_100.conda
  sha256: 102df2421e1c6ee63b8f96dd763aa516760480135ec27b31adb2eb91c4c388f3
  md5: a3d2654966425bb60085bb59d9d9d27a
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - filelock
  - fsspec
  - jinja2
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libblas * *mkl
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libtorch 2.7.1 cpu_mkl_hb1c5dc7_100
  - libuv >=1.51.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.7
  - mkl >=2024.2.2,<2025.0a0
  - networkx
  - numpy >=1.19,<3
  - optree >=0.13.0
  - pybind11
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - setuptools
  - sleef >=3.8,<4.0a0
  - sympy >=1.13.3
  - typing_extensions >=4.10.0
  constrains:
  - pytorch-gpu <0.0a0
  - pytorch-cpu 2.7.1
  license: BSD-3-Clause
  license_family: BSD
  size: 28938244
  timestamp: 1750208726017
- conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
  sha256: 8d2a8bf110cc1fc3df6904091dead158ba3e614d8402a83e51ed3a8aa93cdeb0
  md5: bc8e3267d44011051f2eb14d22fb0960
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 189015
  timestamp: 1742920947249
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py312h178313f_2.conda
  sha256: 159cba13a93b3fe084a1eb9bda0a07afc9148147647f0d437c3c3da60980503b
  md5: cf2485f39740de96e2a7f2bb18ed2fee
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 206903
  timestamp: 1737454910324
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py312hbf22597_0.conda
  sha256: 8564a7beb906476813a59a81a814d00e8f9697c155488dbc59a5c6e950d5f276
  md5: 4b9a9cda3292668831cf47257ade22a6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 378610
  timestamp: 1749898590652
- conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2025.06.26-h9925aae_0.conda
  sha256: 7a0b82cb162229e905f500f18e32118ef581e1fd182036f3298510b8e8663134
  md5: 2b4249747a9091608dbff2bd22afde44
  depends:
  - libre2-11 2025.06.26 hba17884_0
  license: BSD-3-Clause
  size: 27330
  timestamp: 1751053087063
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/linux-64/regex-2024.11.6-py312h66e93f0_0.conda
  sha256: fcb5687d3ec5fff580b64b8fb649d9d65c999a91a5c3108a313ecdd2de99f06b
  md5: 647770db979b43f9c9ca25dcfa7dc4e4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  license_family: PSF
  size: 402821
  timestamp: 1730952378415
- conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
  sha256: 9866aaf7a13c6cfbe665ec7b330647a0fb10a81e6f9b8fee33642232a1920e18
  md5: f6082eae112814f1447b56a5e1f6ed05
  depends:
  - certifi >=2017.4.17
  - charset-normalizer >=2,<4
  - idna >=2.5,<4
  - python >=3.9
  - urllib3 >=1.21.1,<3
  constrains:
  - chardet >=3.0.2,<6
  license: Apache-2.0
  license_family: APACHE
  size: 59407
  timestamp: 1749498221996
- conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.0.0-pyh29332c3_0.conda
  sha256: d10e2b66a557ec6296844e04686db87818b0df87d73c06388f2332fda3f7d2d5
  md5: 202f08242192ce3ed8bdb439ba40c0fe
  depends:
  - markdown-it-py >=2.2.0
  - pygments >=2.13.0,<3.0.0
  - python >=3.9
  - typing_extensions >=4.0.0,<5.0.0
  - python
  license: MIT
  license_family: MIT
  size: 200323
  timestamp: 1743371105291
- conda: https://conda.anaconda.org/conda-forge/noarch/rich-toolkit-0.11.3-pyh29332c3_0.conda
  sha256: e558f8c254a9ff9164d069110da162fc79497d70c60f2c09a5d3d0d7101c5628
  md5: 4ba15ae9388b67d09782798347481f69
  depends:
  - python >=3.9
  - rich >=13.7.1
  - click >=8.1.7
  - typing_extensions >=4.12.2
  - python
  license: MIT
  license_family: MIT
  size: 17357
  timestamp: 1733750834072
- conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.5.21-h7ab7c64_0.conda
  sha256: c8b252398b502a5cc6ea506fd2fafe7e102e7c9e2ef48b7813566e8a72ce2205
  md5: 28b5a7895024a754249b2ad7de372faa
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 358164
  timestamp: 1749095480268
- conda: https://conda.anaconda.org/conda-forge/linux-64/safetensors-0.5.3-py312h12e396e_0.conda
  sha256: 23dec8105d34e51cc2269a79680a666351233e2dc171ff14c46d3455d2c22080
  md5: fd1fc1f1e6ceee16d9a58d3ff5a57c7f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 431388
  timestamp: 1740651706122
- conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.15.2-py312ha707e6e_0.conda
  sha256: b9faaa024b77a3678a988c5a490f02c4029c0d5903998b585100e05bc7d4ff36
  md5: 00b999c5f9d01fb633db819d79186bd4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libgfortran
  - libgfortran5 >=13.3.0
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - numpy <2.5
  - numpy >=1.19,<3
  - numpy >=1.23.5
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 17064784
  timestamp: 1739791925628
- conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-0.2.0-hc8f76dd_11.conda
  sha256: ade8fd245a073c99944d68b0f4f947eee8d0b2011d856336aa5732a1ffe6d00a
  md5: c74d391cfa68c10683bcd951c1cdea11
  depends:
  - libsentencepiece 0.2.0 he636bdd_11
  - python_abi 3.12.* *_cp312
  - sentencepiece-python 0.2.0 py312hb957f94_11
  - sentencepiece-spm 0.2.0 he636bdd_11
  license: Apache-2.0
  license_family: Apache
  size: 19758
  timestamp: 1741898803291
- conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-python-0.2.0-py312hb957f94_11.conda
  sha256: 7868b7f289fa8d99b6c8296e0f2102c7dbcceb8229cb58db1402c1ac8ae6e8f0
  md5: ccacc7c72aad7676df09615577a1054f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libsentencepiece 0.2.0 he636bdd_11
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: Apache
  size: 2497019
  timestamp: 1741898360084
- conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-spm-0.2.0-he636bdd_11.conda
  sha256: 5e791dab9c19798cbaced8545a7c82d11ee3b3b7a93b5c9384df1dd41ad245e9
  md5: 3f6cd9203b68213cc85cb71541cc66b2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libsentencepiece 0.2.0 he636bdd_11
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 90818
  timestamp: 1741898789485
- conda: https://conda.anaconda.org/conda-forge/noarch/sentinel-1.0.0-pyh29332c3_0.conda
  sha256: 87f7d70b0a29627ce7f78a463c6609156aaee638cca917b5f6c6c70828446acf
  md5: 82e861633beabad680253e03918ec4a2
  depends:
  - python >=3.9
  - python
  constrains:
  - varname >=0.1
  license: MIT
  license_family: MIT
  size: 13828
  timestamp: 1740445106343
- conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
  sha256: 972560fcf9657058e3e1f97186cc94389144b46dbdf58c807ce62e83f977e863
  md5: 4de79c071274a53dcaf2a8c749d1499e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 748788
  timestamp: 1748804951958
- conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
  sha256: 0557c090913aa63cdbe821dbdfa038a321b488e22bc80196c4b3b1aace4914ef
  md5: 7c3c2a0f3ebdea2bbc35538d162b43bf
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 14462
  timestamp: 1733301007770
- conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
  sha256: 41db0180680cc67c3fa76544ffd48d6a5679d96f4b71d7498a759e94edc9a2db
  md5: a451d576819089b0d672f18768be0f65
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 16385
  timestamp: 1733381032766
- conda: https://conda.anaconda.org/conda-forge/linux-64/sleef-3.8-h1b44611_0.conda
  sha256: c998d5a29848ce9ff1c53ba506e7d01bbd520c39bbe72e2fb7cdf5a53bad012f
  md5: aec4dba5d4c2924730088753f6fa164b
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libstdcxx >=13
  license: BSL-1.0
  size: 1920152
  timestamp: 1738089391074
- conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
  sha256: ec91e86eeb2c6bbf09d51351b851e945185d70661d2ada67204c9a6419d282d3
  md5: 3b3e64af585eadfb52bb90b553db5edf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 42739
  timestamp: 1733501881851
- conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
  sha256: c2248418c310bdd1719b186796ae50a8a77ce555228b6acd32768e2543a15012
  md5: bf7a226e58dfb8346c70df36065d86c9
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 15019
  timestamp: 1733244175724
- conda: https://conda.anaconda.org/conda-forge/noarch/sse-starlette-2.1.3-pyhd8ed1ab_0.conda
  sha256: 6d671a66333410ec7e5e7858a252565a9001366726d1fe3c3a506d7156169085
  md5: 3918255c942c242ed5599e10329e8d0e
  depends:
  - anyio
  - python >=3.8
  - starlette
  - uvicorn
  license: BSD-3-Clause
  license_family: BSD
  size: 14712
  timestamp: 1722520112550
- conda: https://conda.anaconda.org/conda-forge/noarch/starlette-0.41.2-pyha770c72_0.conda
  sha256: 02206e5369944e0fd29e4f5c8e9b51dd926a74a46b621a73323669ad404f1081
  md5: 287492bb6e159da4357a10a2bd05c13c
  depends:
  - anyio >=3.4.0,<5
  - python >=3.8
  - typing_extensions >=3.10.0
  license: BSD-3-Clause
  license_family: BSD
  size: 59059
  timestamp: 1730305803101
- conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
  sha256: 09d3b6ac51d437bc996ad006d9f749ca5c645c1900a854a6c8f193cbd13f03a8
  md5: 8c09fac3785696e1c477156192d64b91
  depends:
  - __unix
  - cpython
  - gmpy2 >=2.0.8
  - mpmath >=0.19
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 4616621
  timestamp: 1745946173026
- conda: https://conda.anaconda.org/conda-forge/noarch/taskgroup-0.2.2-pyhd8ed1ab_0.conda
  sha256: 6f8db6da8de445930de55b708e6a5d3ab5f076bc14a39578db0190b2a9b8e437
  md5: 9fa69537fb68a095fbac139210575bad
  depends:
  - exceptiongroup
  - python >=3.9
  - typing_extensions >=4.12.2,<5
  license: MIT
  license_family: MIT
  size: 17330
  timestamp: 1736003478648
- conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.13.0-hceb3a55_1.conda
  sha256: 65463732129899770d54b1fbf30e1bb82fdebda9d7553caf08d23db4590cd691
  md5: ba7726b8df7b9d34ea80e82b097a4893
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libhwloc >=2.11.2,<2.11.3.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  size: 175954
  timestamp: 1732982638805
- conda: https://conda.anaconda.org/conda-forge/linux-64/tiktoken-0.9.0-py312h14ff09d_0.conda
  sha256: aba3affdd0f87e198185ddc0986aa59cb067832dc88ffa6dedbe127da4f8d7bf
  md5: 0f116f56298be1450a9db6b45bd2d9a1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - regex >=2022.1.18
  - requests >=2.26.0
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  size: 968542
  timestamp: 1739550580537
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3285204
  timestamp: 1748387766691
- conda: https://conda.anaconda.org/conda-forge/linux-64/tokenizers-0.21.2-py312h8360d73_0.conda
  sha256: a54dcbed5910e0e94f7d14ec4dd0cf137a835a8c069846a9f3fc638d76a8fe52
  md5: f311d7f63df2ab7069a98f5a89f9d358
  depends:
  - __glibc >=2.17,<3.0.a0
  - huggingface_hub >=0.16.4,<1.0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.5.0,<4.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 2374175
  timestamp: 1750798318498
- conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
  sha256: 11e2c85468ae9902d24a27137b6b39b4a78099806e551d390e394a8c34b48e40
  md5: 9efbfdc37242619130ea42b1cc4ed861
  depends:
  - colorama
  - python >=3.9
  license: MPL-2.0 or MIT
  size: 89498
  timestamp: 1735661472632
- conda: https://conda.anaconda.org/conda-forge/noarch/transformers-4.53.0-pyhd8ed1ab_0.conda
  sha256: d3526ea2617dc8650a2c7fd01d7568cda7a709472eb6881e08a4d8e4d68124db
  md5: 42c5cc096057a22b882b8fa92c5e8883
  depends:
  - datasets !=2.5.0
  - filelock
  - huggingface_hub >=0.30.0,<1.0
  - numpy >=1.17
  - packaging >=20.0
  - python >=3.9
  - pyyaml >=5.1
  - regex !=2019.12.17
  - requests
  - safetensors >=0.4.1
  - tokenizers >=0.21,<0.22
  - tqdm >=4.27
  license: Apache-2.0
  license_family: APACHE
  size: 3916160
  timestamp: 1750964780590
- conda: https://conda.anaconda.org/conda-forge/linux-64/tree-2.2.1-hb9d3cd8_0.conda
  sha256: 737f906d21a3ac03d317a0aaa86f91afd0b87f077f83bcfcc7f3b5466f523690
  md5: dab87bfc7ce750a1c2d8a68dc2dad2e8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-2.0-only
  license_family: GPL
  size: 52264
  timestamp: 1733474145469
- conda: https://conda.anaconda.org/conda-forge/linux-64/triton-3.3.1-cuda126py312hebffaa9_1.conda
  sha256: e87c11148ae599bf195b8f5d670ac63b7902b0de3b8c64b73450fb0f388bfc97
  md5: 8131fb1ca6f47e8b3639af406413dc4a
  depends:
  - python
  - setuptools
  - cuda-nvcc-tools
  - cuda-cuobjdump
  - cuda-cudart
  - cuda-cupti
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - cuda-version >=12.6,<13
  - libzlib >=1.3.1,<2.0a0
  - cuda-cupti >=12.6.80,<13.0a0
  - python_abi 3.12.* *_cp312
  - zstd >=1.5.7,<1.6.0a0
  license: MIT
  license_family: MIT
  size: 163152313
  timestamp: 1750544259463
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
  sha256: 1ca70f0c0188598f9425a947afb74914a068bee4b7c4586eabb1c3b02fbf669f
  md5: 985cc086b73bda52b2f8d66dcda460a1
  depends:
  - typer-slim-standard ==0.16.0 hf964461_0
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 77232
  timestamp: 1748304246569
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
  sha256: 54f859ddf5d3216fb602f54990c3ccefc65a30d1d98c400b998e520310630df3
  md5: 0d0a6c08daccb968c8c8fa93070658e2
  depends:
  - python >=3.9
  - click >=8.0.0
  - typing_extensions >=3.7.4.3
  - python
  constrains:
  - typer 0.16.0.*
  - rich >=10.11.0
  - shellingham >=1.3.0
  license: MIT
  license_family: MIT
  size: 46798
  timestamp: 1748304246569
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
  sha256: c35a0b232e9751ac871b733d4236eee887f64c3b1539ba86aecf175c3ac3dc02
  md5: c8fb6ddb4f5eb567d049f85b3f0c8019
  depends:
  - typer-slim ==0.16.0 pyhe01879c_0
  - rich
  - shellingham
  license: MIT
  license_family: MIT
  size: 5271
  timestamp: 1748304246569
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.0-h32cad80_0.conda
  sha256: b8cabfa54432b0f124c0af6b6facdf8110892914fa841ac2e80ab65ac52c1ba4
  md5: a1cdd40fc962e2f7944bc19e01c7e584
  depends:
  - typing_extensions ==4.14.0 pyhe01879c_0
  license: PSF-2.0
  license_family: PSF
  size: 90310
  timestamp: 1748959427551
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-inspection-0.4.1-pyhd8ed1ab_0.conda
  sha256: 4259a7502aea516c762ca8f3b8291b0d4114e094bdb3baae3171ccc0900e722f
  md5: e0c3cd765dc15751ee2f0b03cd015712
  depends:
  - python >=3.9
  - typing_extensions >=4.12.0
  license: MIT
  license_family: MIT
  size: 18809
  timestamp: 1747870776989
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
  sha256: 8561db52f278c5716b436da6d4ee5521712a49e8f3c70fcae5350f5ebb4be41c
  md5: 2adcd9bb86f656d3d43bf84af59a1faf
  depends:
  - python >=3.9
  - python
  license: PSF-2.0
  license_family: PSF
  size: 50978
  timestamp: 1748959427551
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
  sha256: 4fb9789154bd666ca74e428d973df81087a697dbb987775bc3198d2215f240f8
  md5: 436c165519e140cb08d246a4472a9d6a
  depends:
  - brotli-python >=1.0.9
  - h2 >=4,<5
  - pysocks >=1.5.6,<2.0,!=1.5.7
  - python >=3.9
  - zstandard >=0.18.0
  license: MIT
  license_family: MIT
  size: 101735
  timestamp: 1750271478254
- conda: https://conda.anaconda.org/conda-forge/noarch/uvicorn-0.34.3-pyh31011fe_0.conda
  sha256: 4edebc7b6b96ebf92db8b5488c4b39594982eab79db44b267d8a3502e12b051b
  md5: 1520c1396715d45d02f5aa045854a65c
  depends:
  - __unix
  - click >=7.0
  - h11 >=0.8
  - python >=3.9
  - typing_extensions >=4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 49042
  timestamp: 1748779747595
- conda: https://conda.anaconda.org/conda-forge/noarch/uvicorn-standard-0.34.3-h31011fe_0.conda
  sha256: 7de45d9cc878424f65b6ae936f35314e8abc031b60c464258c0a7ef65778c548
  md5: 6d80b382cafd45723e75dccef6496c67
  depends:
  - __unix
  - httptools >=0.6.3
  - python-dotenv >=0.13
  - pyyaml >=5.1
  - uvicorn 0.34.3 pyh31011fe_0
  - uvloop >=0.14.0,!=0.15.0,!=0.15.1
  - watchfiles >=0.13
  - websockets >=10.4
  license: BSD-3-Clause
  license_family: BSD
  size: 7613
  timestamp: 1748779748439
- conda: https://conda.anaconda.org/conda-forge/linux-64/uvloop-0.21.0-py312h66e93f0_1.conda
  sha256: 9337a80165fcf70b06b9d6ba920dad702260ca966419ae77560a15540e41ab72
  md5: 998e481e17c1b6a74572e73b06f2df08
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libuv >=1.49.2,<2.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT OR Apache-2.0
  size: 701355
  timestamp: 1730214506716
- conda: https://conda.anaconda.org/conda-forge/linux-64/watchfiles-1.1.0-py312h12e396e_0.conda
  sha256: 3393493e5fba867ddd062bebe6c371d5bd7cc3e081bfd49de8498537d23c06ac
  md5: 34ded0fc4def76a526a6f0dccb95d7f3
  depends:
  - __glibc >=2.17,<3.0.a0
  - anyio >=3.0.0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  size: 420196
  timestamp: 1750054006450
- conda: https://conda.anaconda.org/conda-forge/linux-64/websockets-15.0.1-py312h66e93f0_0.conda
  sha256: d55c82992553720a4c2f49d383ce8260a4ce1fa39df0125edb71f78ff2ee3682
  md5: b986da7551224417af6b7da4021d8050
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 265549
  timestamp: 1741285580597
- conda: https://conda.anaconda.org/conda-forge/linux-64/wrapt-1.17.2-py312h66e93f0_0.conda
  sha256: ed3a1700ecc5d38c7e7dc7d2802df1bc1da6ba3d6f6017448b8ded0affb4ae00
  md5: 669e63af87710f8d52fdec9d4d63b404
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-2-Clause
  license_family: BSD
  size: 63590
  timestamp: 1736869574299
- conda: https://conda.anaconda.org/conda-forge/linux-64/xgrammar-0.1.19-py312he346f12_0.conda
  sha256: 8805e1c82c17b8721bd43f90eec1eda4b863315ac64a5bd2a10d74542410b8cd
  md5: 113121a0cbf37538bdf921d8e527cf63
  depends:
  - python
  - numpy >=1.26.0
  - ninja
  - pydantic
  - sentencepiece
  - tiktoken
  - pytorch >=1.10.0
  - transformers >=4.38.0
  - triton
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - python_abi 3.12.* *_cp312
  license: Apache-2.0 AND BSD-4-Clause
  size: 6644883
  timestamp: 1748972753695
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.3-hb47aa4a_0.conda
  sha256: 08e12f140b1af540a6de03dd49173c0e5ae4ebc563cabdd35ead0679835baf6f
  md5: 607e13a8caac17f9a664bcab5302ce06
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 108219
  timestamp: 1746457673761
- conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
  sha256: a4e34c710eeb26945bdbdaba82d3d74f60a78f54a874ec10d373811a5d217535
  md5: 4cb3ad778ec2d5a7acbdf254eb1c42ae
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 89141
  timestamp: 1641346969816
- conda: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.20.1-py312h178313f_0.conda
  sha256: f5c2c572423fac9ea74512f96a7c002c81fd2eb260608cfa1edfaeda4d81582e
  md5: 3b3fa80c71d6a8d0380e9e790f5a4a8a
  depends:
  - __glibc >=2.17,<3.0.a0
  - idna >=2.0
  - libgcc >=13
  - multidict >=4.0
  - propcache >=0.2.1
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: Apache
  size: 149496
  timestamp: 1749555225039
- conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
  sha256: a4dc72c96848f764bb5a5176aa93dd1e9b9e52804137b99daeebba277b31ea10
  md5: 3947a35e916fcc6b9825449affbf4214
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  size: 335400
  timestamp: 1731585026517
- conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
  sha256: 7560d21e1b021fd40b65bfb72f67945a3fcb83d78ad7ccf37b8b3165ec3b68ad
  md5: df5e78d904988eb55042c0c97446079f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 22963
  timestamp: 1749421737203
- conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
  sha256: 5d7c0e5f0005f74112a34a7425179f4eb6e73c92f5d109e6af4ddeca407c92ab
  md5: c9f075ab2f33b3bbee9e62d4ad0a6cd8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib 1.3.1 hb9d3cd8_2
  license: Zlib
  license_family: Other
  size: 92286
  timestamp: 1727963153079
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py312h66e93f0_2.conda
  sha256: ff62d2e1ed98a3ec18de7e5cf26c0634fd338cb87304cf03ad8cbafe6fe674ba
  md5: 630db208bc7bbb96725ce9832c7423bb
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.11
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 732224
  timestamp: 1745869780524
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 567578
  timestamp: 1742433379869
