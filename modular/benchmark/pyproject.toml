[project]
authors = [{ name = "Modular", email = "<EMAIL>" }]
dependencies = [
    "aiohttp>=3.11.10,<4",
    "datasets",
    "huggingface-hub>=0.26.5,<0.27",
    "hf-transfer>=0.1.8,<0.2",
    "numpy>=2.2.0,<3",
    "tqdm>=4.67.1,<5",
    "transformers>=4.47.0,<5",
    "nvitop>=1.3.2,<2",
    "jinja2>=3.1.4,<4",
    "Pillow",
]
description = "MAX Serve benchmarking tools"
name = "benchmarking"
requires-python = ">=3.9,<3.14"
version = "0.1.0"

[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.pixi.project]
channels = ["conda-forge", "https://conda.modular.com/max"]
platforms = ["osx-arm64", "linux-64", "linux-aarch64"]

[tool.pixi.pypi-dependencies]
benchmarking = { path = ".", editable = true }

[tool.pixi.tasks]
