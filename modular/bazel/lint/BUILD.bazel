load("@aspect_bazel_lib//lib:directory_path.bzl", "directory_path")
load("@aspect_rules_js//js:defs.bzl", "js_binary")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@rules_multirun//:defs.bzl", "multirun", command = "command_force_opt")
load("@rules_shell//shell:sh_binary.bzl", "sh_binary")

npm_link_all_packages(
    name = "node_modules",
)

directory_path(
    name = "entry_point",
    directory = ":node_modules/markdownlint-cli/dir",
    path = "markdownlint.js",
)

js_binary(
    name = "markdownlint",
    data = [
        ":node_modules/deep-extend",
        ":node_modules/markdownlint-cli",
    ],
    entry_point = ":entry_point",
)

sh_binary(
    name = "markdownlint-wrapper",
    srcs = ["markdownlint-wrapper.sh"],
    data = [":markdownlint"],
)

command(
    name = "markdownlint.check",
    command = "markdownlint-wrapper",
    visibility = ["//visibility:public"],
)

command(
    name = "markdownlint.fix",
    arguments = ["--fix"],
    command = "markdownlint-wrapper",
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "ruff-wrapper",
    srcs = ["ruff-wrapper.sh"],
    data = select({
        "//:linux_x86_64": ["@ruff-x86_64-unknown-linux-gnu//:ruff"],
        "//:linux_aarch64": ["@ruff-aarch64-unknown-linux-gnu//:ruff"],
        "@platforms//os:macos": ["@ruff-aarch64-apple-darwin//:ruff"],
    }),
    visibility = ["//visibility:public"],
)

command(
    name = "ruff-lint.check",
    arguments = [
        "check",
        "--quiet",
    ],
    command = ":ruff-wrapper",
    visibility = ["//visibility:public"],
)

command(
    name = "ruff-lint.fix",
    arguments = [
        "check",
        "--fix",
    ],
    command = ":ruff-wrapper",
    visibility = ["//visibility:public"],
)

command(
    name = "ruff-format.check",
    arguments = [
        "format",
        "--check",
        "--quiet",
        "--diff",
    ],
    command = ":ruff-wrapper",
    visibility = ["//visibility:public"],
)

command(
    name = "ruff-format.fix",
    arguments = ["format"],
    command = ":ruff-wrapper",
    visibility = ["//visibility:public"],
)

multirun(
    name = "lint",
    buffer_output = True,
    commands = [
        ":markdownlint.check",
        ":ruff-format.check",
        ":ruff-lint.check",
    ],
    jobs = 0,
    visibility = ["//visibility:public"],
)

multirun(
    name = "fix",
    buffer_output = True,
    commands = [
        ":markdownlint.fix",
        ":ruff-format.fix",
        ":ruff-lint.fix",
    ],
    jobs = 0,
    visibility = ["//visibility:public"],
)
