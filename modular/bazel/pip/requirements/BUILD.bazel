load("@module_versions//:config.bzl", "TORCH_DEFAULT_VERSION")
load("@rules_multirun//:defs.bzl", "multirun")
load("@rules_uv//uv:pip.bzl", "pip_compile")

exports_files(glob(["**/*-requirements*.txt"]))

[
    pip_compile(
        name = "generate_{}_gpu_requirements".format(gpu_vendor),
        args = [
            "--no-config",
            "--emit-index-url",
            "--index-strategy=unsafe-best-match",  # NOTE: required because torch's index contains typing-extensions and that is preferred over pypi
            "--no-strip-markers",
            "--no-strip-extras",
            "--universal",
            "--config-file",
            "bazel/pip/requirements/gpu-uv.toml",
        ] + ([
            "--no-emit-package",
            "triton",
        ] if gpu_vendor == "amd" else []),
        data = [
            "gpu-uv.toml",
            "requirements.in",
        ],
        exec_properties = {"dockerNetwork": "bridge"},
        py3_runtime = "@python_3_9//:py3_runtime",
        requirements_in = "{}-gpu-requirements.in".format(gpu_vendor),
        requirements_txt = "{}-gpu-requirements.txt".format(gpu_vendor),
    )
    for gpu_vendor in [
        "amd",
        "nvidia",
    ]
]

pip_compile(
    name = "generate_requirements",
    args = [
        "--no-config",
        "--emit-index-url",
        "--index-strategy=unsafe-best-match",  # NOTE: required because torch's index contains requests and that is preferred over pypi
        "--no-strip-extras",
        "--universal",
        "--config-file",
        "bazel/pip/requirements/default-uv.toml",
    ],
    data = [
        "default-uv.toml",
        "requirements.in",
    ],
    exec_properties = {"dockerNetwork": "bridge"},
    py3_runtime = "@python_3_9//:py3_runtime",
    requirements_in = "torch/requirements-{}.in".format(TORCH_DEFAULT_VERSION),
    requirements_txt = "torch/requirements-{}.txt".format(TORCH_DEFAULT_VERSION),
)

multirun(
    name = "update_requirements",
    commands = [
        ":generate_amd_gpu_requirements",
        ":generate_nvidia_gpu_requirements",
        ":generate_requirements",
    ],
)
