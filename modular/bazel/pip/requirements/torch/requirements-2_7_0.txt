# This file was autogenerated by uv via the following command:
#    bazel run @@//bazel/pip/requirements:generate_requirements
--index-url https://pypi.org/simple
--extra-index-url https://download.pytorch.org/whl/cpu

absl-py==2.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   rouge-score
accelerate==1.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   lm-eval
    #   peft
aiofiles==24.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
aiohttp==3.9.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   datasets
    #   fsspec
    #   lm-eval
aiosignal==1.3.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via aiohttp
alabaster==0.7.16 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sphinx
annotated-types==0.7.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pydantic
antlr4-python3-runtime==4.11.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
anyio==4.4.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   httpx
    #   jupyter-server
    #   openai
    #   sse-starlette
    #   starlette
appnope==0.1.4 ; sys_platform == 'darwin'
    # via ipykernel
argon2-cffi==23.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyter-server
argon2-cffi-bindings==21.2.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via argon2-cffi
arrow==1.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via isoduration
asgiref==3.8.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
asttokens==2.4.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via stack-data
async-asgi-testclient==1.4.11 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
async-lru==2.0.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyterlab
async-timeout==4.0.3 ; (python_full_version < '3.11' and sys_platform == 'darwin') or (python_full_version < '3.11' and sys_platform == 'linux')
    # via aiohttp
attrs==23.2.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   aiohttp
    #   hypothesis
    #   jsonlines
    #   jsonschema
    #   referencing
audioread==3.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via librosa
av==14.2.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   faster-whisper
    #   qwen-vl-utils
babel==2.15.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jupyterlab-server
    #   sphinx
beautifulsoup4==4.12.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nbconvert
bleach==6.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nbconvert
blinker==1.8.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via flask
boto3==1.34.128 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
botocore==1.34.128 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   boto3
    #   s3transfer
brotli==1.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via geventhttpclient
cachetools==5.4.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   google-auth
    #   nvitop
certifi==2024.6.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   geventhttpclient
    #   httpcore
    #   httpx
    #   requests
cffi==1.16.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   argon2-cffi-bindings
    #   pyzmq
    #   soundfile
    #   zstandard
cfgv==3.4.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pre-commit
chardet==5.2.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via mbstrdecoder
charset-normalizer==3.3.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via requests
click==8.1.7 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   flask
    #   jiwer
    #   nltk
    #   uvicorn
colorama==0.4.6 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   sacrebleu
    #   tqdm-multiprocess
coloredlogs==15.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via onnxruntime
comm==0.2.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via ipykernel
configargparse==1.7.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via locust
contextlib2==21.6.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via schema
contourpy==1.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via matplotlib
ctranslate2==4.6.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via faster-whisper
cycler==0.12.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via matplotlib
dataproperty==1.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   pytablewriter
    #   tabledata
datasets==2.19.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   evaluate
    #   lm-eval
    #   mteb
debugpy==1.8.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via ipykernel
decorator==5.1.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   ipython
    #   librosa
defusedxml==0.7.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nbconvert
deprecated==1.2.14 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   opentelemetry-api
    #   opentelemetry-exporter-otlp-proto-http
    #   opentelemetry-semantic-conventions
device-smi==0.4.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
dill==0.3.8 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   datasets
    #   evaluate
    #   lm-eval
    #   multiprocess
distlib==0.3.9 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via virtualenv
distro==1.9.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via openai
docutils==0.20.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   sphinx
einops==0.8.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
einx==0.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
eval-type-backport==0.2.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via mteb
evaluate==0.4.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
exceptiongroup==1.2.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   anyio
    #   hypothesis
    #   ipython
    #   pytest
    #   taskgroup
execnet==2.1.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pytest-xdist
executing==2.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via stack-data
fastapi==0.115.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
faster-whisper==1.1.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
fastjsonschema==2.20.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nbformat
filelock==3.15.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   datasets
    #   huggingface-hub
    #   torch
    #   transformers
    #   virtualenv
fire==0.7.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
flask==3.0.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   flask-basicauth
    #   flask-cors
    #   locust
flask-basicauth==0.2.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via locust
flask-cors==5.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via locust
flatbuffers==25.2.10 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via onnxruntime
fonttools==4.53.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via matplotlib
fqdn==1.5.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jsonschema
frozendict==2.4.6 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via einx
frozenlist==1.4.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   aiohttp
    #   aiosignal
fsspec[http]==2024.3.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   datasets
    #   evaluate
    #   huggingface-hub
    #   torch
gevent==24.2.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   geventhttpclient
    #   locust
geventhttpclient==2.0.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via locust
gguf==0.17.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
google-api-core[grpc]==2.19.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   google-cloud-bigquery
    #   google-cloud-core
google-auth==2.29.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-core
google-cloud-bigquery==3.22.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
google-cloud-core==2.4.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via google-cloud-bigquery
google-crc32c==1.5.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via google-resumable-media
google-resumable-media==2.7.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via google-cloud-bigquery
googleapis-common-protos==1.63.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   google-api-core
    #   grpcio-status
    #   opentelemetry-exporter-otlp-proto-http
greenlet==3.1.1 ; (platform_python_implementation == 'CPython' and sys_platform == 'darwin') or (platform_python_implementation == 'CPython' and sys_platform == 'linux')
    # via gevent
grpcio==1.68.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   google-api-core
    #   grpcio-reflection
    #   grpcio-status
    #   grpcio-tools
grpcio-reflection==1.57.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
grpcio-status==1.57.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via google-api-core
grpcio-tools==1.68.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
h11==0.14.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   httpcore
    #   uvicorn
hf-transfer==0.1.8 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
httpcore==1.0.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via httpx
httpx==0.27.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   jupyterlab
    #   openai
huggingface-hub==0.30.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   accelerate
    #   datasets
    #   evaluate
    #   faster-whisper
    #   optimum
    #   peft
    #   sentence-transformers
    #   timm
    #   tokenizers
    #   transformers
humanfriendly==10.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via coloredlogs
hypothesis==6.108.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
identify==2.6.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pre-commit
idna==3.7 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   anyio
    #   httpx
    #   jsonschema
    #   requests
    #   yarl
imagesize==1.4.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sphinx
immutabledict==4.2.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
importlib-metadata==7.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   flask
    #   jupyter-client
    #   jupyter-lsp
    #   jupyterlab
    #   jupyterlab-server
    #   nbconvert
    #   opentelemetry-api
    #   sphinx
importlib-resources==6.4.5 ; (python_full_version < '3.10' and sys_platform == 'darwin') or (python_full_version < '3.10' and sys_platform == 'linux')
    # via matplotlib
iniconfig==2.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pytest
ipykernel==6.29.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyterlab
ipython==8.18.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   ipykernel
isoduration==20.11.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jsonschema
itsdangerous==2.2.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via flask
jedi==0.19.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via ipython
jinja2==3.1.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   flask
    #   jupyter-server
    #   jupyterlab
    #   jupyterlab-server
    #   nbconvert
    #   sphinx
    #   torch
jiter==0.6.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via openai
jiwer==3.0.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
jmespath==1.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   boto3
    #   botocore
joblib==1.4.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   librosa
    #   nltk
    #   scikit-learn
json5==0.9.25 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyterlab-server
jsonlines==4.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
jsonpointer==3.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jsonschema
jsonschema[format-nongpl]==4.22.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jupyter-events
    #   jupyterlab-server
    #   nbformat
jsonschema-specifications==2023.12.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jsonschema
jupyter-client==8.6.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   ipykernel
    #   jupyter-server
    #   nbclient
jupyter-core==5.7.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-server
    #   jupyterlab
    #   nbclient
    #   nbconvert
    #   nbformat
jupyter-events==0.10.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyter-server
jupyter-lsp==2.2.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyterlab
jupyter-server==2.14.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jupyter-lsp
    #   jupyterlab
    #   jupyterlab-server
    #   notebook
    #   notebook-shim
jupyter-server-terminals==0.5.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyter-server
jupyterlab==4.2.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via notebook
jupyterlab-pygments==0.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nbconvert
jupyterlab-server==2.27.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jupyterlab
    #   notebook
kaleido==0.2.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
kiwisolver==1.4.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via matplotlib
langdetect==1.0.9 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
lazy-loader==0.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via librosa
librosa==0.10.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
lightning-utilities==0.14.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via torchmetrics
llvmlite==0.43.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via numba
lm-eval[api, ifeval, math]==0.4.7 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
locust==2.18.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
logbar==0.0.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
lxml==5.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sacrebleu
markdown-it-py==3.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via rich
markupsafe==2.1.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   jinja2
    #   nbconvert
    #   werkzeug
matplotlib==3.9.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
matplotlib-inline==0.1.7 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   ipykernel
    #   ipython
mbstrdecoder==1.1.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   dataproperty
    #   pytablewriter
    #   typepy
mdurl==0.1.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via markdown-it-py
mistune==3.0.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nbconvert
more-itertools==10.5.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
mpmath==1.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sympy
msgpack==1.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   librosa
    #   locust
msgspec==0.18.6 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
mteb==1.29.12 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
multidict==6.0.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   aiohttp
    #   async-asgi-testclient
    #   yarl
multiprocess==0.70.16 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   datasets
    #   evaluate
munch==4.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
mypy==1.13.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
mypy-extensions==1.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   mypy
narwhals==1.39.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via plotly
nbclient==0.10.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nbconvert
nbconvert==7.16.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyter-server
nbformat==5.10.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jupyter-server
    #   nbclient
    #   nbconvert
nest-asyncio==1.6.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via ipykernel
networkx==3.2.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via torch
nltk==3.9.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   lm-eval
    #   rouge-score
nodeenv==1.9.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pre-commit
notebook==7.2.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
notebook-shim==0.2.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jupyterlab
    #   notebook
numba==0.60.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via librosa
numexpr==2.10.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
numpy==2.0.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   accelerate
    #   contourpy
    #   ctranslate2
    #   datasets
    #   einx
    #   evaluate
    #   gguf
    #   librosa
    #   matplotlib
    #   mteb
    #   numba
    #   numexpr
    #   onnxruntime
    #   optimum
    #   pandas
    #   peft
    #   pyarrow
    #   rouge-score
    #   sacrebleu
    #   scikit-learn
    #   scipy
    #   soxr
    #   torchmetrics
    #   torchvision
    #   transformers
nvidia-ml-py==12.535.161 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nvitop
nvitop==1.4.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
onnxruntime==1.19.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   faster-whisper
openai==1.52.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
opentelemetry-api==1.28.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   opentelemetry-exporter-otlp-proto-http
    #   opentelemetry-exporter-prometheus
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-exporter-otlp-proto-common==1.28.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via opentelemetry-exporter-otlp-proto-http
opentelemetry-exporter-otlp-proto-http==1.28.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
opentelemetry-exporter-prometheus==0.49b2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
opentelemetry-proto==1.28.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   opentelemetry-exporter-otlp-proto-common
    #   opentelemetry-exporter-otlp-proto-http
opentelemetry-sdk==1.28.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   opentelemetry-exporter-otlp-proto-http
    #   opentelemetry-exporter-prometheus
opentelemetry-semantic-conventions==0.49b2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via opentelemetry-sdk
optimum==1.24.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
overrides==7.7.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyter-server
packaging==24.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   accelerate
    #   datasets
    #   evaluate
    #   google-cloud-bigquery
    #   huggingface-hub
    #   ipykernel
    #   jupyter-server
    #   jupyterlab
    #   jupyterlab-server
    #   lazy-loader
    #   lightning-utilities
    #   matplotlib
    #   nbconvert
    #   onnxruntime
    #   optimum
    #   peft
    #   plotly
    #   pooch
    #   pytest
    #   qwen-vl-utils
    #   sphinx
    #   torchmetrics
    #   transformers
    #   typepy
pandas==2.2.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   datasets
    #   evaluate
pandocfilters==1.5.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nbconvert
parso==0.8.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jedi
pathspec==0.12.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
pathvalidate==3.2.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pytablewriter
peft==0.13.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
pexpect==4.9.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via ipython
pillow==10.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   matplotlib
    #   qwen-vl-utils
    #   sentence-transformers
    #   torchvision
pip==24.1.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
platformdirs==4.2.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   jupyter-core
    #   pooch
    #   virtualenv
plotly==6.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
pluggy==1.5.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pytest
polars==1.20.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via mteb
pooch==1.8.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via librosa
portalocker==2.10.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sacrebleu
pre-commit==4.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
prometheus-async==22.2.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
prometheus-client==0.20.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   jupyter-server
    #   opentelemetry-exporter-prometheus
    #   prometheus-async
prompt-toolkit==3.0.47 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via ipython
proto-plus==1.24.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via google-api-core
protobuf==5.29.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   google-api-core
    #   googleapis-common-protos
    #   grpcio-reflection
    #   grpcio-status
    #   grpcio-tools
    #   onnxruntime
    #   opentelemetry-proto
    #   proto-plus
psutil==5.9.8 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   accelerate
    #   ipykernel
    #   locust
    #   nvitop
    #   peft
ptyprocess==0.7.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   pexpect
    #   terminado
pure-eval==0.2.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via stack-data
py-cpuinfo==9.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   pytest-benchmark
pyarrow==17.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   datasets
pyarrow-hotfix==0.6 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via datasets
pyasn1==0.6.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via google-auth
pybind11==2.10.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
pycparser==2.22 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via cffi
pydantic==2.8.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   fastapi
    #   mteb
    #   openai
    #   pydantic-settings
pydantic-core==2.20.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pydantic
pydantic-settings==2.3.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
pygame==2.6.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
pygments==2.18.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   ipython
    #   nbconvert
    #   rich
    #   sphinx
pyinstrument==4.7.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
pyparsing==3.1.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via matplotlib
pytablewriter==1.2.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
pytest==8.2.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   pytest-asyncio
    #   pytest-benchmark
    #   pytest-grpc
    #   pytest-mock
    #   pytest-xdist
pytest-asyncio==0.23.7 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
pytest-benchmark==4.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
pytest-grpc==0.8.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
pytest-mock==3.14.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
pytest-xdist==3.6.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
python-dateutil==2.9.0.post0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   arrow
    #   botocore
    #   google-cloud-bigquery
    #   jupyter-client
    #   matplotlib
    #   pandas
    #   typepy
python-dotenv==1.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pydantic-settings
python-json-logger==2.0.7 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   jupyter-events
pytrec-eval-terrier==0.5.6 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via mteb
pytz==2024.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   pandas
    #   typepy
pyyaml==6.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   accelerate
    #   ctranslate2
    #   datasets
    #   gguf
    #   huggingface-hub
    #   jupyter-events
    #   peft
    #   pre-commit
    #   responses
    #   timm
    #   transformers
pyzmq==26.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   ipykernel
    #   jupyter-client
    #   jupyter-server
    #   locust
qwen-vl-utils==0.0.10 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
rapidfuzz==3.13.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jiwer
referencing==0.35.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jsonschema
    #   jsonschema-specifications
    #   jupyter-events
regex==2024.5.15 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   nltk
    #   sacrebleu
    #   tiktoken
    #   transformers
requests==2.32.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   async-asgi-testclient
    #   datasets
    #   evaluate
    #   google-api-core
    #   google-cloud-bigquery
    #   huggingface-hub
    #   jupyterlab-server
    #   lm-eval
    #   locust
    #   mteb
    #   opentelemetry-exporter-otlp-proto-http
    #   pooch
    #   qwen-vl-utils
    #   responses
    #   sphinx
    #   tiktoken
    #   transformers
responses==0.25.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
rfc3339-validator==0.1.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jsonschema
    #   jupyter-events
rfc3986-validator==0.1.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jsonschema
    #   jupyter-events
rich==13.7.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   mteb
rouge-score==0.1.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
roundrobin==0.0.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via locust
rpds-py==0.18.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jsonschema
    #   referencing
rsa==4.9 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via google-auth
ruamel-yaml==0.18.6 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
ruamel-yaml-clib==0.2.8 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   ruamel-yaml
s3transfer==0.10.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via boto3
sacrebleu==2.4.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
safetensors==0.4.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   accelerate
    #   peft
    #   timm
    #   transformers
schema==0.7.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
scikit-learn==1.5.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   librosa
    #   lm-eval
    #   mteb
    #   sentence-transformers
scipy==1.13.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   librosa
    #   mteb
    #   scikit-learn
    #   sentence-transformers
send2trash==1.8.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyter-server
sentence-transformers==3.3.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   mteb
sentinel==1.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
setuptools==70.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   ctranslate2
    #   grpcio-tools
    #   jupyterlab
    #   lightning-utilities
    #   pytablewriter
    #   torch
    #   zope-event
    #   zope-interface
six==1.16.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   asttokens
    #   bleach
    #   geventhttpclient
    #   langdetect
    #   python-dateutil
    #   rfc3339-validator
    #   rouge-score
sniffio==1.3.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   anyio
    #   httpx
    #   openai
snowballstemmer==2.2.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sphinx
sortedcontainers==2.4.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via hypothesis
soundfile==0.12.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   librosa
soupsieve==2.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via beautifulsoup4
soxr==0.5.0.post1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via librosa
sphinx==7.4.7 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
sphinxcontrib-applehelp==2.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sphinx
sphinxcontrib-devhelp==2.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sphinx
sphinxcontrib-htmlhelp==2.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sphinx
sphinxcontrib-jsmath==1.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sphinx
sphinxcontrib-qthelp==2.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sphinx
sphinxcontrib-serializinghtml==2.0.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via sphinx
sqlitedict==2.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
sse-starlette==2.1.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
sseclient-py==1.8.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
stack-data==0.6.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via ipython
stack-pr==0.1.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
starlette==0.41.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   fastapi
    #   sse-starlette
sympy==1.13.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   einx
    #   lm-eval
    #   onnxruntime
    #   torch
tabledata==1.3.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pytablewriter
tabulate==0.9.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   sacrebleu
taskgroup==0.2.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
tcolorpy==0.1.6 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pytablewriter
tenacity==8.4.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
termcolor==2.5.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   fire
    #   nvitop
terminado==0.18.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   jupyter-server
    #   jupyter-server-terminals
threadpoolctl==3.5.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   scikit-learn
tiktoken==0.8.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
timm==1.0.15 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
tinycss2==1.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via nbconvert
tokenicer==0.0.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
tokenizers==0.21.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   faster-whisper
    #   transformers
tomli==2.0.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   jupyterlab
    #   mypy
    #   pytest
    #   sphinx
torch==2.7.0 ; platform_machine != 'x86_64' and sys_platform == 'darwin'
    # via
    #   -r bazel/pip/requirements/torch/requirements-2_7_0.in
    #   accelerate
    #   lm-eval
    #   mteb
    #   optimum
    #   peft
    #   sentence-transformers
    #   timm
    #   torchaudio
    #   torchmetrics
    #   torchvision
torch==2.7.0+cpu ; (platform_machine == 'x86_64' and sys_platform == 'darwin') or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/requirements-2_7_0.in
    #   accelerate
    #   lm-eval
    #   mteb
    #   optimum
    #   peft
    #   sentence-transformers
    #   timm
    #   torchaudio
    #   torchmetrics
    #   torchvision
torchaudio==2.7.0 ; (platform_machine != 'x86_64' and sys_platform == 'darwin') or (platform_machine != 'x86_64' and sys_platform == 'linux')
    # via -r bazel/pip/requirements/torch/requirements-2_7_0.in
torchaudio==2.7.0+cpu ; (platform_machine == 'x86_64' and sys_platform == 'darwin') or (platform_machine == 'x86_64' and sys_platform == 'linux')
    # via -r bazel/pip/requirements/torch/requirements-2_7_0.in
torchmetrics==1.7.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
torchvision==0.22.0 ; (platform_machine != 'x86_64' and sys_platform == 'darwin') or (platform_machine != 'x86_64' and sys_platform == 'linux')
    # via
    #   -r bazel/pip/requirements/torch/requirements-2_7_0.in
    #   timm
torchvision==0.22.0+cpu ; (platform_machine == 'x86_64' and sys_platform == 'darwin') or (platform_machine == 'x86_64' and sys_platform == 'linux')
    # via
    #   -r bazel/pip/requirements/torch/requirements-2_7_0.in
    #   timm
tornado==6.4.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-server
    #   jupyterlab
    #   notebook
    #   terminado
tqdm==4.66.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   datasets
    #   evaluate
    #   faster-whisper
    #   gguf
    #   huggingface-hub
    #   lm-eval
    #   mteb
    #   nltk
    #   openai
    #   peft
    #   sentence-transformers
    #   tqdm-multiprocess
    #   transformers
tqdm-multiprocess==0.0.11 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
traitlets==5.14.3 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   comm
    #   ipykernel
    #   ipython
    #   jupyter-client
    #   jupyter-core
    #   jupyter-events
    #   jupyter-server
    #   jupyterlab
    #   matplotlib-inline
    #   nbclient
    #   nbconvert
    #   nbformat
transformers==4.52.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   lm-eval
    #   optimum
    #   peft
    #   sentence-transformers
    #   tokenicer
typepy[datetime]==1.3.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   dataproperty
    #   pytablewriter
    #   tabledata
types-protobuf==5.28.3.20241030 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
types-python-dateutil==2.9.0.20240316 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via arrow
types-pyyaml==6.0.12.20240917 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
types-setuptools==75.5.0.20241121 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
types-tabulate==0.9.0.20240106 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
typing-extensions==4.12.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   anyio
    #   asgiref
    #   async-lru
    #   fastapi
    #   huggingface-hub
    #   ipython
    #   librosa
    #   lightning-utilities
    #   mteb
    #   mypy
    #   openai
    #   opentelemetry-sdk
    #   prometheus-async
    #   pydantic
    #   pydantic-core
    #   starlette
    #   taskgroup
    #   torch
    #   uvicorn
tzdata==2024.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pandas
uri-template==1.3.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jsonschema
urllib3==1.26.19 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   botocore
    #   requests
    #   responses
uvicorn==0.30.5 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   -r bazel/pip/requirements/torch/../requirements.in
    #   sse-starlette
uvloop==0.21.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via -r bazel/pip/requirements/torch/../requirements.in
virtualenv==20.28.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via pre-commit
wcwidth==0.2.13 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via prompt-toolkit
webcolors==24.6.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jsonschema
webencodings==0.5.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   bleach
    #   tinycss2
websocket-client==1.8.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via jupyter-server
werkzeug==3.0.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   flask
    #   locust
word2number==1.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
wrapt==1.16.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   deprecated
    #   prometheus-async
xxhash==3.4.1 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   datasets
    #   evaluate
yarl==1.9.4 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via aiohttp
zipp==3.19.2 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via
    #   importlib-metadata
    #   importlib-resources
zope-event==5.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via gevent
zope-interface==7.1.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via gevent
zstandard==0.23.0 ; sys_platform == 'darwin' or sys_platform == 'linux'
    # via lm-eval
