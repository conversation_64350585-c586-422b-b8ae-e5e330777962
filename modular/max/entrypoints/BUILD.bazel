load("//bazel:api.bzl", "modular_py_binary", "modular_py_library", "pkg_filegroup", "pkg_files", "requirement")

package(default_visibility = ["//visibility:public"])

modular_py_library(
    name = "entrypoints",
    srcs = glob(
        ["**/*.py"],
        exclude = [
            "_package_root.py",
            "mojo.py",
            "gpu_query.py",
            "pipelines.py",
            "replay_recording.py",
        ],
    ),
    visibility = ["//visibility:public"],
    deps = [
        "//SDK/lib/API/python/max/pipelines",
        "//SDK/lib/API/python/max/serve",
        "//SDK/lib/API/python/max/serve/kvcache_agent",
        "//SDK/lib/API/python/max/serve/pipelines",
        requirement("click"),
        requirement("gguf"),
        requirement("huggingface-hub"),
        requirement("numpy"),
        requirement("opentelemetry-api"),
        requirement("opentelemetry-sdk"),
        requirement("pillow"),
        requirement("psutil"),
        requirement("requests"),
        requirement("soundfile"),
        requirement("tokenizers"),
        requirement("tqdm"),
        requirement("transformers"),
        requirement("uvicorn"),
        requirement("uvloop"),
    ],
)

modular_py_library(
    name = "_package_root",
    srcs = ["_package_root.py"],
    visibility = ["//visibility:public"],
)

modular_py_library(
    name = "mojo",
    srcs = ["mojo.py"],
    data = [
        "//KGEN/tools/mojo",
        "@mojo//:stdlib",
    ],
    visibility = ["//visibility:public"],
    deps = [":_package_root"],
)

modular_py_binary(
    name = "pipelines",
    srcs = [
        "pipelines.py",
    ],
    data = [
        "@daolabs_flash_attn",
    ] + select({
        "//:linux_x86_64": ["//AsyncRT:plugin_ucx"],
        "//conditions:default": [],
    }),
    env = {
        "OTEL_EXPORTER_OTLP_METRICS_DEFAULT_HISTOGRAM_AGGREGATION": "base2_exponential_bucket_histogram",
        "DAOLABS_FLASH_ATTENTION_LIBRARY": "$(location @daolabs_flash_attn)",
    } | select({
        "//:linux_x86_64": {"MODULAR_NIXL_PLUGIN_DIR": "$(location //AsyncRT:plugin_ucx)/../"},
        "//conditions:default": {},
    }),
    deps = [
        "//SDK/lib/API/python/max/entrypoints",
        "//SDK/lib/API/python/max/nn",
        "//SDK/lib/API/python/max/pipelines",
        "//SDK/lib/API/python/max/pipelines/architectures",
        "//SDK/lib/API/python/max/serve",
        requirement("click"),
        requirement("huggingface-hub"),
        requirement("pillow"),
        requirement("transformers"),
        requirement("uvicorn"),
    ],
)

modular_py_binary(
    name = "replay_recording",
    srcs = ["replay_recording.py"],
    deps = [
        "//SDK/lib/API/python/max/serve/recordreplay",
        requirement("click"),
        requirement("httpx"),
    ],
)

pkg_files(
    name = "python_files",
    srcs = glob(
        [
            "**/*.py",
        ],
    ),
    # Don't strip the prefix of subdirectories.
    strip_prefix = "",
)

pkg_filegroup(
    name = "entrypoints_python",
    srcs = [
        ":python_files",
        "//open-source/max/benchmark:python_files",
    ],
)

modular_py_library(
    name = "gpu-query",
    srcs = ["gpu_query.py"],
    visibility = ["//visibility:public"],
    deps = [":_package_root"],
)
