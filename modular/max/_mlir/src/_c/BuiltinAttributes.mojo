# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:26.954244 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #


from .AffineMap import *
from .ffi import MLIR_func
from .IntegerSet import *
from .IR import *
from .Support import *

# ===-- mlir-c/BuiltinAttributes.h - C API for Builtin Attributes -*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//
#
#  This header declares the C interface to MLIR Builtin attributes.
#
# ===----------------------------------------------------------------------===//


fn mlirAttributeGetNull() -> MlirAttribute:
    """Returns an empty attribute."""
    return MLIR_func["mlirAttributeGetNull", MlirAttribute]()


# ===----------------------------------------------------------------------===//
#  Location attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsALocation(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsALocation", Bool](attr)


# ===----------------------------------------------------------------------===//
#  Affine map attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAAffineMap(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is an affine map attribute."""
    return MLIR_func["mlirAttributeIsAAffineMap", Bool](attr)


fn mlirAffineMapAttrGet(map: MlirAffineMap) -> MlirAttribute:
    """Creates an affine map attribute wrapping the given map. The attribute
    belongs to the same context as the affine map."""
    return MLIR_func["mlirAffineMapAttrGet", MlirAttribute](map)


fn mlirAffineMapAttrGetValue(attr: MlirAttribute) -> MlirAffineMap:
    """Returns the affine map wrapped in the given affine map attribute."""
    return MLIR_func["mlirAffineMapAttrGetValue", MlirAffineMap](attr)


fn mlirAffineMapAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of an AffineMap attribute."""
    return MLIR_func["mlirAffineMapAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  Array attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAArray(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is an array attribute."""
    return MLIR_func["mlirAttributeIsAArray", Bool](attr)


fn mlirArrayAttrGet(
    ctx: MlirContext, num_elements: Int, elements: UnsafePointer[MlirAttribute]
) -> MlirAttribute:
    """Creates an array element containing the given list of elements in the given
    context."""
    return MLIR_func["mlirArrayAttrGet", MlirAttribute](
        ctx, num_elements, elements
    )


fn mlirArrayAttrGetNumElements(attr: MlirAttribute) -> Int:
    """Returns the number of elements stored in the given array attribute."""
    return MLIR_func["mlirArrayAttrGetNumElements", Int](attr)


fn mlirArrayAttrGetElement(attr: MlirAttribute, pos: Int) -> MlirAttribute:
    """Returns pos-th element stored in the given array attribute."""
    return MLIR_func["mlirArrayAttrGetElement", MlirAttribute](attr, pos)


fn mlirArrayAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of an Array attribute."""
    return MLIR_func["mlirArrayAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  Dictionary attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsADictionary(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a dictionary attribute."""
    return MLIR_func["mlirAttributeIsADictionary", Bool](attr)


fn mlirDictionaryAttrGet(
    ctx: MlirContext,
    num_elements: Int,
    elements: UnsafePointer[MlirNamedAttribute],
) -> MlirAttribute:
    """Creates a dictionary attribute containing the given list of elements in the
    provided context."""
    return MLIR_func["mlirDictionaryAttrGet", MlirAttribute](
        ctx, num_elements, elements
    )


fn mlirDictionaryAttrGetNumElements(attr: MlirAttribute) -> Int:
    """Returns the number of attributes contained in a dictionary attribute."""
    return MLIR_func["mlirDictionaryAttrGetNumElements", Int](attr)


fn mlirDictionaryAttrGetElement(
    attr: MlirAttribute, pos: Int
) -> MlirNamedAttribute:
    """Returns pos-th element of the given dictionary attribute."""
    return MLIR_func["mlirDictionaryAttrGetElement", MlirNamedAttribute](
        attr, pos
    )


fn mlirDictionaryAttrGetElementByName(
    attr: MlirAttribute, name: MlirStringRef
) -> MlirAttribute:
    """Returns the dictionary attribute element with the given name or NULL if the
    given name does not exist in the dictionary."""
    return MLIR_func["mlirDictionaryAttrGetElementByName", MlirAttribute](
        attr, name
    )


fn mlirDictionaryAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of a Dictionary attribute."""
    return MLIR_func["mlirDictionaryAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  Floating point attribute.
# ===----------------------------------------------------------------------===//

#  TODO: add support for APFloat and APInt to LLVM IR C API, then expose the
#  relevant functions here.


fn mlirAttributeIsAFloat(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a floating point attribute."""
    return MLIR_func["mlirAttributeIsAFloat", Bool](attr)


fn mlirFloatAttrDoubleGet(
    ctx: MlirContext, type: MlirType, value: Float64
) -> MlirAttribute:
    """Creates a floating point attribute in the given context with the given
    double value and double-precision FP semantics."""
    return MLIR_func["mlirFloatAttrDoubleGet", MlirAttribute](ctx, type, value)


fn mlirFloatAttrDoubleGetChecked(
    loc: MlirLocation, type: MlirType, value: Float64
) -> MlirAttribute:
    """Same as "mlirFloatAttrDoubleGet", but if the type is not valid for a
    construction of a FloatAttr, returns a null MlirAttribute."""
    return MLIR_func["mlirFloatAttrDoubleGetChecked", MlirAttribute](
        loc, type, value
    )


fn mlirFloatAttrGetValueDouble(attr: MlirAttribute) -> Float64:
    """Returns the value stored in the given floating point attribute, interpreting
    the value as double."""
    return MLIR_func["mlirFloatAttrGetValueDouble", Float64](attr)


fn mlirFloatAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of a Float attribute."""
    return MLIR_func["mlirFloatAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  Integer attribute.
# ===----------------------------------------------------------------------===//

#  TODO: add support for APFloat and APInt to LLVM IR C API, then expose the
#  relevant functions here.


fn mlirAttributeIsAInteger(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is an integer attribute."""
    return MLIR_func["mlirAttributeIsAInteger", Bool](attr)


fn mlirIntegerAttrGet(type: MlirType, value: Int64) -> MlirAttribute:
    """Creates an integer attribute of the given type with the given integer
    value."""
    return MLIR_func["mlirIntegerAttrGet", MlirAttribute](type, value)


fn mlirIntegerAttrGetValueInt(attr: MlirAttribute) -> Int64:
    """Returns the value stored in the given integer attribute, assuming the value
    is of signless type and fits into a signed 64-bit integer."""
    return MLIR_func["mlirIntegerAttrGetValueInt", Int64](attr)


fn mlirIntegerAttrGetValueSInt(attr: MlirAttribute) -> Int64:
    """Returns the value stored in the given integer attribute, assuming the value
    is of signed type and fits into a signed 64-bit integer."""
    return MLIR_func["mlirIntegerAttrGetValueSInt", Int64](attr)


fn mlirIntegerAttrGetValueUInt(attr: MlirAttribute) -> UInt64:
    """Returns the value stored in the given integer attribute, assuming the value
    is of unsigned type and fits into an unsigned 64-bit integer."""
    return MLIR_func["mlirIntegerAttrGetValueUInt", UInt64](attr)


fn mlirIntegerAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of an Integer attribute."""
    return MLIR_func["mlirIntegerAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  Bool attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsABool(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a bool attribute."""
    return MLIR_func["mlirAttributeIsABool", Bool](attr)


fn mlirBoolAttrGet(ctx: MlirContext, value: Int16) -> MlirAttribute:
    """Creates a bool attribute in the given context with the given value."""
    return MLIR_func["mlirBoolAttrGet", MlirAttribute](ctx, value)


fn mlirBoolAttrGetValue(attr: MlirAttribute) -> Bool:
    """Returns the value stored in the given bool attribute."""
    return MLIR_func["mlirBoolAttrGetValue", Bool](attr)


# ===----------------------------------------------------------------------===//
#  Integer set attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAIntegerSet(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is an integer set attribute."""
    return MLIR_func["mlirAttributeIsAIntegerSet", Bool](attr)


fn mlirIntegerSetAttrGet(set: MlirIntegerSet) -> MlirAttribute:
    """Creates an integer set attribute wrapping the given set. The attribute
    belongs to the same context as the integer set."""
    return MLIR_func["mlirIntegerSetAttrGet", MlirAttribute](set)


fn mlirIntegerSetAttrGetValue(attr: MlirAttribute) -> MlirIntegerSet:
    """Returns the integer set wrapped in the given integer set attribute."""
    return MLIR_func["mlirIntegerSetAttrGetValue", MlirIntegerSet](attr)


fn mlirIntegerSetAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of an IntegerSet attribute."""
    return MLIR_func["mlirIntegerSetAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  Opaque attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAOpaque(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is an opaque attribute."""
    return MLIR_func["mlirAttributeIsAOpaque", Bool](attr)


fn mlirOpaqueAttrGet(
    ctx: MlirContext,
    dialect_namespace: MlirStringRef,
    data_length: Int,
    data: UnsafePointer[Int8],
    type: MlirType,
) -> MlirAttribute:
    """Creates an opaque attribute in the given context associated with the dialect
    identified by its namespace. The attribute contains opaque byte data of the
    specified length (data need not be null-terminated)."""
    return MLIR_func["mlirOpaqueAttrGet", MlirAttribute](
        ctx, dialect_namespace, data_length, data, type
    )


fn mlirOpaqueAttrGetDialectNamespace(attr: MlirAttribute) -> MlirStringRef:
    """Returns the namespace of the dialect with which the given opaque attribute
    is associated. The namespace string is owned by the context."""
    return MLIR_func["mlirOpaqueAttrGetDialectNamespace", MlirStringRef](attr)


fn mlirOpaqueAttrGetData(attr: MlirAttribute) -> MlirStringRef:
    """Returns the raw data as a string reference. The data remains live as long as
    the context in which the attribute lives."""
    return MLIR_func["mlirOpaqueAttrGetData", MlirStringRef](attr)


fn mlirOpaqueAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of an Opaque attribute."""
    return MLIR_func["mlirOpaqueAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  String attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAString(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a string attribute."""
    return MLIR_func["mlirAttributeIsAString", Bool](attr)


#  Creates a string attribute in the given context containing the given string.


fn mlirStringAttrGet(ctx: MlirContext, str: MlirStringRef) -> MlirAttribute:
    """Creates a string attribute in the given context containing the given string.
    """
    return MLIR_func["mlirStringAttrGet", MlirAttribute](ctx, str)


fn mlirStringAttrTypedGet(type: MlirType, str: MlirStringRef) -> MlirAttribute:
    """Creates a string attribute in the given context containing the given string.
    Additionally, the attribute has the given type."""
    return MLIR_func["mlirStringAttrTypedGet", MlirAttribute](type, str)


fn mlirStringAttrGetValue(attr: MlirAttribute) -> MlirStringRef:
    """Returns the attribute values as a string reference. The data remains live as
    long as the context in which the attribute lives."""
    return MLIR_func["mlirStringAttrGetValue", MlirStringRef](attr)


fn mlirStringAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of a String attribute."""
    return MLIR_func["mlirStringAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  SymbolRef attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsASymbolRef(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a symbol reference attribute."""
    return MLIR_func["mlirAttributeIsASymbolRef", Bool](attr)


fn mlirSymbolRefAttrGet(
    ctx: MlirContext,
    symbol: MlirStringRef,
    num_references: Int,
    references: UnsafePointer[MlirAttribute],
) -> MlirAttribute:
    """Creates a symbol reference attribute in the given context referencing a
    symbol identified by the given string inside a list of nested references.
    Each of the references in the list must not be nested."""
    return MLIR_func["mlirSymbolRefAttrGet", MlirAttribute](
        ctx, symbol, num_references, references
    )


fn mlirSymbolRefAttrGetRootReference(attr: MlirAttribute) -> MlirStringRef:
    """Returns the string reference to the root referenced symbol. The data remains
    live as long as the context in which the attribute lives."""
    return MLIR_func["mlirSymbolRefAttrGetRootReference", MlirStringRef](attr)


fn mlirSymbolRefAttrGetLeafReference(attr: MlirAttribute) -> MlirStringRef:
    """Returns the string reference to the leaf referenced symbol. The data remains
    live as long as the context in which the attribute lives."""
    return MLIR_func["mlirSymbolRefAttrGetLeafReference", MlirStringRef](attr)


fn mlirSymbolRefAttrGetNumNestedReferences(attr: MlirAttribute) -> Int:
    """Returns the number of references nested in the given symbol reference
    attribute."""
    return MLIR_func["mlirSymbolRefAttrGetNumNestedReferences", Int](attr)


fn mlirSymbolRefAttrGetNestedReference(
    attr: MlirAttribute, pos: Int
) -> MlirAttribute:
    """Returns pos-th reference nested in the given symbol reference attribute.
    """
    return MLIR_func["mlirSymbolRefAttrGetNestedReference", MlirAttribute](
        attr, pos
    )


fn mlirSymbolRefAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of an SymbolRef attribute."""
    return MLIR_func["mlirSymbolRefAttrGetTypeID", MlirTypeID]()


fn mlirDisctinctAttrCreate(referenced_attr: MlirAttribute) -> MlirAttribute:
    """Creates a DisctinctAttr with the referenced attribute."""
    return MLIR_func["mlirDisctinctAttrCreate", MlirAttribute](referenced_attr)


# ===----------------------------------------------------------------------===//
#  Flat SymbolRef attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAFlatSymbolRef(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a flat symbol reference attribute.
    """
    return MLIR_func["mlirAttributeIsAFlatSymbolRef", Bool](attr)


fn mlirFlatSymbolRefAttrGet(
    ctx: MlirContext, symbol: MlirStringRef
) -> MlirAttribute:
    """Creates a flat symbol reference attribute in the given context referencing a
    symbol identified by the given string."""
    return MLIR_func["mlirFlatSymbolRefAttrGet", MlirAttribute](ctx, symbol)


fn mlirFlatSymbolRefAttrGetValue(attr: MlirAttribute) -> MlirStringRef:
    """Returns the referenced symbol as a string reference. The data remains live
    as long as the context in which the attribute lives."""
    return MLIR_func["mlirFlatSymbolRefAttrGetValue", MlirStringRef](attr)


# ===----------------------------------------------------------------------===//
#  Type attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAType(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a type attribute."""
    return MLIR_func["mlirAttributeIsAType", Bool](attr)


fn mlirTypeAttrGet(type: MlirType) -> MlirAttribute:
    """Creates a type attribute wrapping the given type in the same context as the
    type."""
    return MLIR_func["mlirTypeAttrGet", MlirAttribute](type)


fn mlirTypeAttrGetValue(attr: MlirAttribute) -> MlirType:
    """Returns the type stored in the given type attribute."""
    return MLIR_func["mlirTypeAttrGetValue", MlirType](attr)


fn mlirTypeAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of a Type attribute."""
    return MLIR_func["mlirTypeAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  Unit attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAUnit(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a unit attribute."""
    return MLIR_func["mlirAttributeIsAUnit", Bool](attr)


fn mlirUnitAttrGet(ctx: MlirContext) -> MlirAttribute:
    """Creates a unit attribute in the given context."""
    return MLIR_func["mlirUnitAttrGet", MlirAttribute](ctx)


fn mlirUnitAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of a Unit attribute."""
    return MLIR_func["mlirUnitAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  Elements attributes.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAElements(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is an elements attribute."""
    return MLIR_func["mlirAttributeIsAElements", Bool](attr)


fn mlirElementsAttrGetValue(
    attr: MlirAttribute, rank: Int, idxs: UnsafePointer[UInt64]
) -> MlirAttribute:
    """Returns the element at the given rank-dimensional index."""
    return MLIR_func["mlirElementsAttrGetValue", MlirAttribute](
        attr, rank, idxs
    )


fn mlirElementsAttrIsValidIndex(
    attr: MlirAttribute, rank: Int, idxs: UnsafePointer[UInt64]
) -> Bool:
    """Checks whether the given rank-dimensional index is valid in the given
    elements attribute."""
    return MLIR_func["mlirElementsAttrIsValidIndex", Bool](attr, rank, idxs)


fn mlirElementsAttrGetNumElements(attr: MlirAttribute) -> Int64:
    """Gets the total number of elements in the given elements attribute. In order
    to iterate over the attribute, obtain its type, which must be a statically
    shaped type and use its sizes to build a multi-dimensional index."""
    return MLIR_func["mlirElementsAttrGetNumElements", Int64](attr)


# ===----------------------------------------------------------------------===//
#  Dense array attribute.
# ===----------------------------------------------------------------------===//


fn mlirDenseArrayAttrGetTypeID() -> MlirTypeID:
    return MLIR_func["mlirDenseArrayAttrGetTypeID", MlirTypeID]()


fn mlirAttributeIsADenseBoolArray(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a dense array attribute."""
    return MLIR_func["mlirAttributeIsADenseBoolArray", Bool](attr)


fn mlirAttributeIsADenseI8Array(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsADenseI8Array", Bool](attr)


fn mlirAttributeIsADenseI16Array(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsADenseI16Array", Bool](attr)


fn mlirAttributeIsADenseI32Array(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsADenseI32Array", Bool](attr)


fn mlirAttributeIsADenseI64Array(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsADenseI64Array", Bool](attr)


fn mlirAttributeIsADenseF32Array(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsADenseF32Array", Bool](attr)


fn mlirAttributeIsADenseF64Array(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsADenseF64Array", Bool](attr)


fn mlirDenseBoolArrayGet(
    ctx: MlirContext, size: Int, values: UnsafePointer[Int16]
) -> MlirAttribute:
    """Create a dense array attribute with the given elements."""
    return MLIR_func["mlirDenseBoolArrayGet", MlirAttribute](ctx, size, values)


fn mlirDenseI8ArrayGet(
    ctx: MlirContext, size: Int, values: UnsafePointer[Int8]
) -> MlirAttribute:
    return MLIR_func["mlirDenseI8ArrayGet", MlirAttribute](ctx, size, values)


fn mlirDenseI16ArrayGet(
    ctx: MlirContext, size: Int, values: UnsafePointer[Int16]
) -> MlirAttribute:
    return MLIR_func["mlirDenseI16ArrayGet", MlirAttribute](ctx, size, values)


fn mlirDenseI32ArrayGet(
    ctx: MlirContext, size: Int, values: UnsafePointer[Int32]
) -> MlirAttribute:
    return MLIR_func["mlirDenseI32ArrayGet", MlirAttribute](ctx, size, values)


fn mlirDenseI64ArrayGet(
    ctx: MlirContext, size: Int, values: UnsafePointer[Int64]
) -> MlirAttribute:
    return MLIR_func["mlirDenseI64ArrayGet", MlirAttribute](ctx, size, values)


fn mlirDenseF32ArrayGet(
    ctx: MlirContext, size: Int, values: UnsafePointer[Float32]
) -> MlirAttribute:
    return MLIR_func["mlirDenseF32ArrayGet", MlirAttribute](ctx, size, values)


fn mlirDenseF64ArrayGet(
    ctx: MlirContext, size: Int, values: UnsafePointer[Float64]
) -> MlirAttribute:
    return MLIR_func["mlirDenseF64ArrayGet", MlirAttribute](ctx, size, values)


fn mlirDenseArrayGetNumElements(attr: MlirAttribute) -> Int:
    """Get the size of a dense array."""
    return MLIR_func["mlirDenseArrayGetNumElements", Int](attr)


fn mlirDenseBoolArrayGetElement(attr: MlirAttribute, pos: Int) -> Bool:
    """Get an element of a dense array."""
    return MLIR_func["mlirDenseBoolArrayGetElement", Bool](attr, pos)


fn mlirDenseI8ArrayGetElement(attr: MlirAttribute, pos: Int) -> Int8:
    return MLIR_func["mlirDenseI8ArrayGetElement", Int8](attr, pos)


fn mlirDenseI16ArrayGetElement(attr: MlirAttribute, pos: Int) -> Int16:
    return MLIR_func["mlirDenseI16ArrayGetElement", Int16](attr, pos)


fn mlirDenseI32ArrayGetElement(attr: MlirAttribute, pos: Int) -> Int32:
    return MLIR_func["mlirDenseI32ArrayGetElement", Int32](attr, pos)


fn mlirDenseI64ArrayGetElement(attr: MlirAttribute, pos: Int) -> Int64:
    return MLIR_func["mlirDenseI64ArrayGetElement", Int64](attr, pos)


fn mlirDenseF32ArrayGetElement(attr: MlirAttribute, pos: Int) -> Float32:
    return MLIR_func["mlirDenseF32ArrayGetElement", Float32](attr, pos)


fn mlirDenseF64ArrayGetElement(attr: MlirAttribute, pos: Int) -> Float64:
    return MLIR_func["mlirDenseF64ArrayGetElement", Float64](attr, pos)


# ===----------------------------------------------------------------------===//
#  Dense elements attribute.
# ===----------------------------------------------------------------------===//

#  TODO: decide on the interface and add support for complex elements.
#  TODO: add support for APFloat and APInt to LLVM IR C API, then expose the
#  relevant functions here.


fn mlirAttributeIsADenseElements(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a dense elements attribute."""
    return MLIR_func["mlirAttributeIsADenseElements", Bool](attr)


fn mlirAttributeIsADenseIntElements(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsADenseIntElements", Bool](attr)


fn mlirAttributeIsADenseFPElements(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsADenseFPElements", Bool](attr)


fn mlirDenseIntOrFPElementsAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of an DenseIntOrFPElements attribute."""
    return MLIR_func["mlirDenseIntOrFPElementsAttrGetTypeID", MlirTypeID]()


fn mlirDenseElementsAttrGet(
    shaped_type: MlirType,
    num_elements: Int,
    elements: UnsafePointer[MlirAttribute],
) -> MlirAttribute:
    """Creates a dense elements attribute with the given Shaped type and elements
    in the same context as the type."""
    return MLIR_func["mlirDenseElementsAttrGet", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrRawBufferGet(
    shaped_type: MlirType,
    raw_buffer_size: Int,
    raw_buffer: OpaquePointer,
) -> MlirAttribute:
    """Creates a dense elements attribute with the given Shaped type and elements
    populated from a packed, row-major opaque buffer of contents.

    The format of the raw buffer is a densely packed array of values that
    can be bitcast to the storage format of the element type specified.
    Types that are not byte aligned will be:
      - For bitwidth > 1: Rounded up to the next byte.
      - For bitwidth = 1: Packed into 8bit bytes with bits corresponding to
        the linear order of the shape type from MSB to LSB, padded to on the
        right.

    A raw buffer of a single element (or for 1-bit, a byte of value 0 or 255)
    will be interpreted as a splat. User code should be prepared for additional,
    conformant patterns to be identified as splats in the future."""
    return MLIR_func["mlirDenseElementsAttrRawBufferGet", MlirAttribute](
        shaped_type, raw_buffer_size, raw_buffer
    )


fn mlirDenseElementsAttrSplatGet(
    shaped_type: MlirType, element: MlirAttribute
) -> MlirAttribute:
    """Creates a dense elements attribute with the given Shaped type containing a
    single replicated element (splat)."""
    return MLIR_func["mlirDenseElementsAttrSplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrBoolSplatGet(
    shaped_type: MlirType, element: Bool
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrBoolSplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrUInt8SplatGet(
    shaped_type: MlirType, element: UInt8
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrUInt8SplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrInt8SplatGet(
    shaped_type: MlirType, element: Int8
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrInt8SplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrUInt32SplatGet(
    shaped_type: MlirType, element: UInt32
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrUInt32SplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrInt32SplatGet(
    shaped_type: MlirType, element: Int32
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrInt32SplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrUInt64SplatGet(
    shaped_type: MlirType, element: UInt64
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrUInt64SplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrInt64SplatGet(
    shaped_type: MlirType, element: Int64
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrInt64SplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrFloatSplatGet(
    shaped_type: MlirType, element: Float32
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrFloatSplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrDoubleSplatGet(
    shaped_type: MlirType, element: Float64
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrDoubleSplatGet", MlirAttribute](
        shaped_type, element
    )


fn mlirDenseElementsAttrBoolGet(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[Int16]
) -> MlirAttribute:
    """Creates a dense elements attribute with the given shaped type from elements
    of a specific type. Expects the element type of the shaped type to match the
    data element type."""
    return MLIR_func["mlirDenseElementsAttrBoolGet", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrUInt8Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[UInt8]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrUInt8Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrInt8Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[Int8]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrInt8Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrUInt16Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[UInt16]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrUInt16Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrInt16Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[Int16]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrInt16Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrUInt32Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[UInt32]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrUInt32Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrInt32Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[Int32]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrInt32Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrUInt64Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[UInt64]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrUInt64Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrInt64Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[Int64]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrInt64Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrFloatGet(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[Float32]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrFloatGet", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrDoubleGet(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[Float64]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrDoubleGet", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrBFloat16Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[UInt16]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrBFloat16Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrFloat16Get(
    shaped_type: MlirType, num_elements: Int, elements: UnsafePointer[UInt16]
) -> MlirAttribute:
    return MLIR_func["mlirDenseElementsAttrFloat16Get", MlirAttribute](
        shaped_type, num_elements, elements
    )


fn mlirDenseElementsAttrStringGet(
    shaped_type: MlirType, num_elements: Int, strs: UnsafePointer[MlirStringRef]
) -> MlirAttribute:
    """Creates a dense elements attribute with the given shaped type from string
    elements."""
    return MLIR_func["mlirDenseElementsAttrStringGet", MlirAttribute](
        shaped_type, num_elements, strs
    )


fn mlirDenseElementsAttrReshapeGet(
    attr: MlirAttribute, shaped_type: MlirType
) -> MlirAttribute:
    """Creates a dense elements attribute that has the same data as the given dense
    elements attribute and a different shaped type. The new type must have the
    same total number of elements."""
    return MLIR_func["mlirDenseElementsAttrReshapeGet", MlirAttribute](
        attr, shaped_type
    )


fn mlirDenseElementsAttrIsSplat(attr: MlirAttribute) -> Bool:
    """Checks whether the given dense elements attribute contains a single
    replicated value (splat)."""
    return MLIR_func["mlirDenseElementsAttrIsSplat", Bool](attr)


fn mlirDenseElementsAttrGetSplatValue(attr: MlirAttribute) -> MlirAttribute:
    """Returns the single replicated value (splat) of a specific type contained by
    the given dense elements attribute."""
    return MLIR_func["mlirDenseElementsAttrGetSplatValue", MlirAttribute](attr)


fn mlirDenseElementsAttrGetBoolSplatValue(attr: MlirAttribute) -> Int16:
    return MLIR_func["mlirDenseElementsAttrGetBoolSplatValue", Int16](attr)


fn mlirDenseElementsAttrGetInt8SplatValue(attr: MlirAttribute) -> Int8:
    return MLIR_func["mlirDenseElementsAttrGetInt8SplatValue", Int8](attr)


fn mlirDenseElementsAttrGetUInt8SplatValue(attr: MlirAttribute) -> UInt8:
    return MLIR_func["mlirDenseElementsAttrGetUInt8SplatValue", UInt8](attr)


fn mlirDenseElementsAttrGetInt32SplatValue(attr: MlirAttribute) -> Int32:
    return MLIR_func["mlirDenseElementsAttrGetInt32SplatValue", Int32](attr)


fn mlirDenseElementsAttrGetUInt32SplatValue(attr: MlirAttribute) -> UInt32:
    return MLIR_func["mlirDenseElementsAttrGetUInt32SplatValue", UInt32](attr)


fn mlirDenseElementsAttrGetInt64SplatValue(attr: MlirAttribute) -> Int64:
    return MLIR_func["mlirDenseElementsAttrGetInt64SplatValue", Int64](attr)


fn mlirDenseElementsAttrGetUInt64SplatValue(attr: MlirAttribute) -> UInt64:
    return MLIR_func["mlirDenseElementsAttrGetUInt64SplatValue", UInt64](attr)


fn mlirDenseElementsAttrGetFloatSplatValue(attr: MlirAttribute) -> Float32:
    return MLIR_func["mlirDenseElementsAttrGetFloatSplatValue", Float32](attr)


fn mlirDenseElementsAttrGetDoubleSplatValue(attr: MlirAttribute) -> Float64:
    return MLIR_func["mlirDenseElementsAttrGetDoubleSplatValue", Float64](attr)


fn mlirDenseElementsAttrGetStringSplatValue(
    attr: MlirAttribute,
) -> MlirStringRef:
    return MLIR_func["mlirDenseElementsAttrGetStringSplatValue", MlirStringRef](
        attr
    )


fn mlirDenseElementsAttrGetBoolValue(attr: MlirAttribute, pos: Int) -> Bool:
    """Returns the pos-th value (flat contiguous indexing) of a specific type
    contained by the given dense elements attribute."""
    return MLIR_func["mlirDenseElementsAttrGetBoolValue", Bool](attr, pos)


fn mlirDenseElementsAttrGetInt8Value(attr: MlirAttribute, pos: Int) -> Int8:
    return MLIR_func["mlirDenseElementsAttrGetInt8Value", Int8](attr, pos)


fn mlirDenseElementsAttrGetUInt8Value(attr: MlirAttribute, pos: Int) -> UInt8:
    return MLIR_func["mlirDenseElementsAttrGetUInt8Value", UInt8](attr, pos)


fn mlirDenseElementsAttrGetInt16Value(attr: MlirAttribute, pos: Int) -> Int16:
    return MLIR_func["mlirDenseElementsAttrGetInt16Value", Int16](attr, pos)


fn mlirDenseElementsAttrGetUInt16Value(attr: MlirAttribute, pos: Int) -> UInt16:
    return MLIR_func["mlirDenseElementsAttrGetUInt16Value", UInt16](attr, pos)


fn mlirDenseElementsAttrGetInt32Value(attr: MlirAttribute, pos: Int) -> Int32:
    return MLIR_func["mlirDenseElementsAttrGetInt32Value", Int32](attr, pos)


fn mlirDenseElementsAttrGetUInt32Value(attr: MlirAttribute, pos: Int) -> UInt32:
    return MLIR_func["mlirDenseElementsAttrGetUInt32Value", UInt32](attr, pos)


fn mlirDenseElementsAttrGetInt64Value(attr: MlirAttribute, pos: Int) -> Int64:
    return MLIR_func["mlirDenseElementsAttrGetInt64Value", Int64](attr, pos)


fn mlirDenseElementsAttrGetUInt64Value(attr: MlirAttribute, pos: Int) -> UInt64:
    return MLIR_func["mlirDenseElementsAttrGetUInt64Value", UInt64](attr, pos)


fn mlirDenseElementsAttrGetFloatValue(attr: MlirAttribute, pos: Int) -> Float32:
    return MLIR_func["mlirDenseElementsAttrGetFloatValue", Float32](attr, pos)


fn mlirDenseElementsAttrGetDoubleValue(
    attr: MlirAttribute, pos: Int
) -> Float64:
    return MLIR_func["mlirDenseElementsAttrGetDoubleValue", Float64](attr, pos)


fn mlirDenseElementsAttrGetStringValue(
    attr: MlirAttribute, pos: Int
) -> MlirStringRef:
    return MLIR_func["mlirDenseElementsAttrGetStringValue", MlirStringRef](
        attr, pos
    )


fn mlirDenseElementsAttrGetRawData(
    attr: MlirAttribute,
) -> OpaquePointer:
    """Returns the raw data of the given dense elements attribute."""
    return MLIR_func["mlirDenseElementsAttrGetRawData", OpaquePointer](attr)


# ===----------------------------------------------------------------------===//
#  Resource blob attributes.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsADenseResourceElements(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsADenseResourceElements", Bool](attr)


fn mlirUnmanagedDenseResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    data: OpaquePointer,
    data_length: Int,
    data_alignment: Int,
    data_is_mutable: Bool,
    deleter: fn (OpaquePointer, OpaquePointer, Int32, Int32) -> None,
    user_data: OpaquePointer,
) -> MlirAttribute:
    """Unlike the typed accessors below, constructs the attribute with a raw
    data buffer and no type/alignment checking. Use a more strongly typed
    accessor if possible. If dataIsMutable is false, then an immutable
    AsmResourceBlob will be created and that passed data contents will be
    treated as const.
    If the deleter is non NULL, then it will be called when the data buffer
    can no longer be accessed (passing userData to it)."""
    return MLIR_func[
        "mlirUnmanagedDenseResourceElementsAttrGet", MlirAttribute
    ](
        shaped_type,
        name,
        data,
        data_length,
        data_alignment,
        data_is_mutable,
        deleter,
        user_data,
    )


fn mlirUnmanagedDenseBoolResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[Int16],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseBoolResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseUInt8ResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[UInt8],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseUInt8ResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseInt8ResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[Int8],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseInt8ResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseUInt16ResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[UInt16],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseUInt16ResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseInt16ResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[Int16],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseInt16ResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseUInt32ResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[UInt32],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseUInt32ResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseInt32ResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[Int32],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseInt32ResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseUInt64ResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[UInt64],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseUInt64ResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseInt64ResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[Int64],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseInt64ResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseFloatResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[Float32],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseFloatResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirUnmanagedDenseDoubleResourceElementsAttrGet(
    shaped_type: MlirType,
    name: MlirStringRef,
    num_elements: Int,
    elements: UnsafePointer[Float64],
) -> MlirAttribute:
    return MLIR_func[
        "mlirUnmanagedDenseDoubleResourceElementsAttrGet", MlirAttribute
    ](shaped_type, name, num_elements, elements)


fn mlirDenseBoolResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> Bool:
    """Returns the pos-th value (flat contiguous indexing) of a specific type
    contained by the given dense resource elements attribute."""
    return MLIR_func["mlirDenseBoolResourceElementsAttrGetValue", Bool](
        attr, pos
    )


fn mlirDenseInt8ResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> Int8:
    return MLIR_func["mlirDenseInt8ResourceElementsAttrGetValue", Int8](
        attr, pos
    )


fn mlirDenseUInt8ResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> UInt8:
    return MLIR_func["mlirDenseUInt8ResourceElementsAttrGetValue", UInt8](
        attr, pos
    )


fn mlirDenseInt16ResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> Int16:
    return MLIR_func["mlirDenseInt16ResourceElementsAttrGetValue", Int16](
        attr, pos
    )


fn mlirDenseUInt16ResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> UInt16:
    return MLIR_func["mlirDenseUInt16ResourceElementsAttrGetValue", UInt16](
        attr, pos
    )


fn mlirDenseInt32ResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> Int32:
    return MLIR_func["mlirDenseInt32ResourceElementsAttrGetValue", Int32](
        attr, pos
    )


fn mlirDenseUInt32ResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> UInt32:
    return MLIR_func["mlirDenseUInt32ResourceElementsAttrGetValue", UInt32](
        attr, pos
    )


fn mlirDenseInt64ResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> Int64:
    return MLIR_func["mlirDenseInt64ResourceElementsAttrGetValue", Int64](
        attr, pos
    )


fn mlirDenseUInt64ResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> UInt64:
    return MLIR_func["mlirDenseUInt64ResourceElementsAttrGetValue", UInt64](
        attr, pos
    )


fn mlirDenseFloatResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> Float32:
    return MLIR_func["mlirDenseFloatResourceElementsAttrGetValue", Float32](
        attr, pos
    )


fn mlirDenseDoubleResourceElementsAttrGetValue(
    attr: MlirAttribute, pos: Int
) -> Float64:
    return MLIR_func["mlirDenseDoubleResourceElementsAttrGetValue", Float64](
        attr, pos
    )


# ===----------------------------------------------------------------------===//
#  Sparse elements attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsASparseElements(attr: MlirAttribute) -> Bool:
    """Checks whether the given attribute is a sparse elements attribute."""
    return MLIR_func["mlirAttributeIsASparseElements", Bool](attr)


fn mlirSparseElementsAttribute(
    shaped_type: MlirType,
    dense_indices: MlirAttribute,
    dense_values: MlirAttribute,
) -> MlirAttribute:
    """Creates a sparse elements attribute of the given shape from a list of
    indices and a list of associated values. Both lists are expected to be dense
    elements attributes with the same number of elements. The list of indices is
    expected to contain 64-bit integers. The attribute is created in the same
    context as the type."""
    return MLIR_func["mlirSparseElementsAttribute", MlirAttribute](
        shaped_type, dense_indices, dense_values
    )


fn mlirSparseElementsAttrGetIndices(attr: MlirAttribute) -> MlirAttribute:
    """Returns the dense elements attribute containing 64-bit integer indices of
    non-null elements in the given sparse elements attribute."""
    return MLIR_func["mlirSparseElementsAttrGetIndices", MlirAttribute](attr)


fn mlirSparseElementsAttrGetValues(attr: MlirAttribute) -> MlirAttribute:
    """Returns the dense elements attribute containing the non-null elements in the
    given sparse elements attribute."""
    return MLIR_func["mlirSparseElementsAttrGetValues", MlirAttribute](attr)


fn mlirSparseElementsAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of a SparseElements attribute."""
    return MLIR_func["mlirSparseElementsAttrGetTypeID", MlirTypeID]()


# ===----------------------------------------------------------------------===//
#  Strided layout attribute.
# ===----------------------------------------------------------------------===//


fn mlirAttributeIsAStridedLayout(attr: MlirAttribute) -> Bool:
    return MLIR_func["mlirAttributeIsAStridedLayout", Bool](attr)


fn mlirStridedLayoutAttrGet(
    ctx: MlirContext,
    offset: Int64,
    num_strides: Int,
    strides: UnsafePointer[Int64],
) -> MlirAttribute:
    return MLIR_func["mlirStridedLayoutAttrGet", MlirAttribute](
        ctx, offset, num_strides, strides
    )


fn mlirStridedLayoutAttrGetOffset(attr: MlirAttribute) -> Int64:
    return MLIR_func["mlirStridedLayoutAttrGetOffset", Int64](attr)


fn mlirStridedLayoutAttrGetNumStrides(attr: MlirAttribute) -> Int:
    return MLIR_func["mlirStridedLayoutAttrGetNumStrides", Int](attr)


fn mlirStridedLayoutAttrGetStride(attr: MlirAttribute, pos: Int) -> Int64:
    return MLIR_func["mlirStridedLayoutAttrGetStride", Int64](attr, pos)


fn mlirStridedLayoutAttrGetTypeID() -> MlirTypeID:
    """Returns the typeID of a StridedLayout attribute."""
    return MLIR_func["mlirStridedLayoutAttrGetTypeID", MlirTypeID]()
