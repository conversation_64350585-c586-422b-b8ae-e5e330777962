# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:28.327728 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #


from utils.write import _WriteBufferStack

from .AffineExpr import *
from .ffi import MLIR_func

# ===-- mlir-c/IntegerSet.h - C API for MLIR Affine maps ----------*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//


# ===----------------------------------------------------------------------===//
#  Opaque type declarations.
#
#  Types are exposed to C bindings as structs containing opaque pointers. They
#  are not supposed to be inspected from C. This allows the underlying
#  representation to change without affecting the API users. The use of structs
#  instead of typedefs enables some type safety as structs are not implicitly
#  convertible to each other.
#
#  Instances of these types may or may not own the underlying object. The
#  ownership semantics is defined by how an instance of the type was obtained.
# ===----------------------------------------------------------------------===//


@register_passable("trivial")
struct MlirIntegerSet:
    var ptr: OpaquePointer


fn mlirIntegerSetGetContext(set: MlirIntegerSet) -> MlirContext:
    """Gets the context in which the given integer set lives."""
    return MLIR_func["mlirIntegerSetGetContext", MlirContext](set)


# FIXME(codegen): static function mlirIntegerSetIsNull


fn mlirIntegerSetEqual(s1: MlirIntegerSet, s2: MlirIntegerSet) -> Bool:
    """Checks if two integer set objects are equal. This is a "shallow" comparison
    of two objects. Only the sets with some small number of constraints are
    uniqued and compare equal here. Set objects that represent the same integer
    set with different constraints may be considered non-equal by this check.
    Set difference followed by an (expensive) emptiness check should be used to
    check equivalence of the underlying integer sets."""
    return MLIR_func["mlirIntegerSetEqual", Bool](s1, s2)


fn mlirIntegerSetPrint[W: Writer](mut writer: W, set: MlirIntegerSet):
    """Prints an integer set by sending chunks of the string representation and
    forwarding `userData to `callback`. Note that the callback may be called
    several times with consecutive chunks of the string."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirIntegerSetPrint", NoneType._mlir_type](
        set, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


fn mlirIntegerSetDump(set: MlirIntegerSet) -> None:
    """Prints an integer set to the standard error stream."""
    return MLIR_func["mlirIntegerSetDump", NoneType._mlir_type](set)


fn mlirIntegerSetEmptyGet(
    context: MlirContext, num_dims: Int, num_symbols: Int
) -> MlirIntegerSet:
    """Gets or creates a new canonically empty integer set with the give number of
    dimensions and symbols in the given context."""
    return MLIR_func["mlirIntegerSetEmptyGet", MlirIntegerSet](
        context, num_dims, num_symbols
    )


fn mlirIntegerSetGet(
    context: MlirContext,
    num_dims: Int,
    num_symbols: Int,
    num_constraints: Int,
    constraints: UnsafePointer[MlirAffineExpr],
    eq_flags: UnsafePointer[Bool],
) -> MlirIntegerSet:
    """Gets or creates a new integer set in the given context. The set is defined
    by a list of affine constraints, with the given number of input dimensions
    and symbols, which are treated as either equalities (eqFlags is 1) or
    inequalities (eqFlags is 0). Both `constraints` and `eqFlags` are expected
    to point to at least `numConstraint` consecutive values."""
    return MLIR_func["mlirIntegerSetGet", MlirIntegerSet](
        context, num_dims, num_symbols, num_constraints, constraints, eq_flags
    )


fn mlirIntegerSetReplaceGet(
    set: MlirIntegerSet,
    dim_replacements: UnsafePointer[MlirAffineExpr],
    symbol_replacements: UnsafePointer[MlirAffineExpr],
    num_result_dims: Int,
    num_result_symbols: Int,
) -> MlirIntegerSet:
    """Gets or creates a new integer set in which the values and dimensions of the
    given set are replaced with the given affine expressions. `dimReplacements`
    and `symbolReplacements` are expected to point to at least as many
    consecutive expressions as the given set has dimensions and symbols,
    respectively. The new set will have `numResultDims` and `numResultSymbols`
    dimensions and symbols, respectively."""
    return MLIR_func["mlirIntegerSetReplaceGet", MlirIntegerSet](
        set,
        dim_replacements,
        symbol_replacements,
        num_result_dims,
        num_result_symbols,
    )


fn mlirIntegerSetIsCanonicalEmpty(set: MlirIntegerSet) -> Bool:
    """Checks whether the given set is a canonical empty set, e.g., the set
    returned by mlirIntegerSetEmptyGet."""
    return MLIR_func["mlirIntegerSetIsCanonicalEmpty", Bool](set)


fn mlirIntegerSetGetNumDims(set: MlirIntegerSet) -> Int:
    """Returns the number of dimensions in the given set."""
    return MLIR_func["mlirIntegerSetGetNumDims", Int](set)


fn mlirIntegerSetGetNumSymbols(set: MlirIntegerSet) -> Int:
    """Returns the number of symbols in the given set."""
    return MLIR_func["mlirIntegerSetGetNumSymbols", Int](set)


fn mlirIntegerSetGetNumInputs(set: MlirIntegerSet) -> Int:
    """Returns the number of inputs (dimensions + symbols) in the given set."""
    return MLIR_func["mlirIntegerSetGetNumInputs", Int](set)


fn mlirIntegerSetGetNumConstraints(set: MlirIntegerSet) -> Int:
    """Returns the number of constraints (equalities + inequalities) in the given
    set."""
    return MLIR_func["mlirIntegerSetGetNumConstraints", Int](set)


fn mlirIntegerSetGetNumEqualities(set: MlirIntegerSet) -> Int:
    """Returns the number of equalities in the given set."""
    return MLIR_func["mlirIntegerSetGetNumEqualities", Int](set)


fn mlirIntegerSetGetNumInequalities(set: MlirIntegerSet) -> Int:
    """Returns the number of inequalities in the given set."""
    return MLIR_func["mlirIntegerSetGetNumInequalities", Int](set)


fn mlirIntegerSetGetConstraint(set: MlirIntegerSet, pos: Int) -> MlirAffineExpr:
    """Returns `pos`-th constraint of the set."""
    return MLIR_func["mlirIntegerSetGetConstraint", MlirAffineExpr](set, pos)


fn mlirIntegerSetIsConstraintEq(set: MlirIntegerSet, pos: Int) -> Bool:
    """Returns `true` of the `pos`-th constraint of the set is an equality
    constraint, `false` otherwise."""
    return MLIR_func["mlirIntegerSetIsConstraintEq", Bool](set, pos)
