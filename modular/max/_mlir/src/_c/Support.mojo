# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:28.470710 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #

from collections.string import StaticString


from .ffi import MLIR_func

# ===-- mlir-c/Support.h - Helpers for C API to Core MLIR ---------*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//
#
#  This header declares the auxiliary data structures used in C APIs to core
#  MLIR functionality.
#
# ===----------------------------------------------------------------------===//

# ===----------------------------------------------------------------------===//
#  Visibility annotations.
#  Use MLIR_CAPI_EXPORTED for exported functions.
#
#  On Windows, if MLIR_CAPI_ENABLE_WINDOWS_DLL_DECLSPEC is defined, then
#  __declspec(dllexport) and __declspec(dllimport) will be generated. This
#  can only be enabled if actually building DLLs. It is generally, mutually
#  exclusive with the use of other mechanisms for managing imports/exports
#  (i.e. CMake's WINDOWS_EXPORT_ALL_SYMBOLS feature).
# ===----------------------------------------------------------------------===//


@register_passable("trivial")
struct MlirLlvmThreadPool:
    """Re-export llvm::ThreadPool so as to avoid including the LLVM C API directly.
    """

    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirTypeID:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirTypeIDAllocator:
    var ptr: OpaquePointer


# ===----------------------------------------------------------------------===//
#  MlirStringRef.
# ===----------------------------------------------------------------------===//

#  A pointer to a sized fragment of a string, not necessarily null-terminated.
#  Does not own the underlying string. This is equivalent to llvm::StringRef.

# A pointer to a sized fragment of a string, not necessarily null-terminated.
# Does not own the underlying string. This is equivalent to llvm::StringRef.
# Note that since `StringSlice` has the same ABI as `llvm::StringRef`,
# then `StringSlice[StaticConstantOrigin]` is morally equivalent.
alias MlirStringRef = StaticString

#  Constructs a string reference from the pointer and length. The pointer need
#  not reference to a null-terminated string.

# FIXME(codegen): static function mlirStringRefCreate


fn mlirStringRefCreateFromCString(str: UnsafePointer[Int8]) -> MlirStringRef:
    """Constructs a string reference from a null-terminated C string. Prefer
    mlirStringRefCreate if the length of the string is known."""
    return MLIR_func["mlirStringRefCreateFromCString", MlirStringRef](str)


fn mlirStringRefEqual(string: MlirStringRef, other: MlirStringRef) -> Bool:
    """Returns true if two string references are equal, false otherwise."""
    return MLIR_func["mlirStringRefEqual", Bool](string, other)


# ===----------------------------------------------------------------------===//
#  MlirLogicalResult.
# ===----------------------------------------------------------------------===//


@fieldwise_init
@register_passable("trivial")
struct MlirLogicalResult(Copyable, Movable):
    """A logical result value, essentially a boolean with named states. LLVM
    convention for using boolean values to designate success or failure of an
    operation is a moving target, so MLIR opted for an explicit class.
    Instances of MlirLogicalResult must only be inspected using the associated
    functions."""

    var value: Int8


# FIXME(codegen): static function mlirLogicalResultIsSuccess

# FIXME(codegen): static function mlirLogicalResultIsFailure

# FIXME(codegen): static function mlirLogicalResultSuccess

# FIXME(codegen): static function mlirLogicalResultFailure

# ===----------------------------------------------------------------------===//
#  MlirLlvmThreadPool.
# ===----------------------------------------------------------------------===//


fn mlirLlvmThreadPoolCreate() -> MlirLlvmThreadPool:
    """Create an LLVM thread pool. This is reexported here to avoid directly
    pulling in the LLVM headers directly."""
    return MLIR_func["mlirLlvmThreadPoolCreate", MlirLlvmThreadPool]()


fn mlirLlvmThreadPoolDestroy(pool: MlirLlvmThreadPool) -> None:
    """Destroy an LLVM thread pool."""
    return MLIR_func["mlirLlvmThreadPoolDestroy", NoneType._mlir_type](pool)


# ===----------------------------------------------------------------------===//
#  TypeID API.
# ===----------------------------------------------------------------------===//


fn mlirTypeIDCreate(ptr: OpaquePointer) -> MlirTypeID:
    """`ptr` must be 8 byte aligned and unique to a type valid for the duration of
    the returned type id's usage."""
    return MLIR_func["mlirTypeIDCreate", MlirTypeID](ptr)


# FIXME(codegen): static function mlirTypeIDIsNull


fn mlirTypeIDEqual(type_id1: MlirTypeID, type_id2: MlirTypeID) -> Bool:
    """Checks if two type ids are equal."""
    return MLIR_func["mlirTypeIDEqual", Bool](type_id1, type_id2)


fn mlirTypeIDHashValue(type_id: MlirTypeID) -> Int:
    """Returns the hash value of the type id."""
    return MLIR_func["mlirTypeIDHashValue", Int](type_id)


# ===----------------------------------------------------------------------===//
#  TypeIDAllocator API.
# ===----------------------------------------------------------------------===//


fn mlirTypeIDAllocatorCreate() -> MlirTypeIDAllocator:
    """Creates a type id allocator for dynamic type id creation."""
    return MLIR_func["mlirTypeIDAllocatorCreate", MlirTypeIDAllocator]()


fn mlirTypeIDAllocatorDestroy(allocator: MlirTypeIDAllocator) -> None:
    """Deallocates the allocator and all allocated type ids."""
    return MLIR_func["mlirTypeIDAllocatorDestroy", NoneType._mlir_type](
        allocator
    )


fn mlirTypeIDAllocatorAllocateTypeID(
    allocator: MlirTypeIDAllocator,
) -> MlirTypeID:
    """Allocates a type id that is valid for the lifetime of the allocator."""
    return MLIR_func["mlirTypeIDAllocatorAllocateTypeID", MlirTypeID](allocator)
