# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:27.404337 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #


from .ffi import MLIR_func
from .IR import *

# ===-- mlir-c/RegisterEverything.h - Register all MLIR entities --*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//
#  This header contains registration entry points for MLIR upstream dialects
#  and passes. Downstream projects typically will not want to use this unless
#  if they don't care about binary size or build bloat and just wish access
#  to the entire set of upstream facilities. For those that do care, they
#  should use registration functions specific to their project.
# ===----------------------------------------------------------------------===//


fn mlirRegisterAllDialects(registry: MlirDialectRegistry) -> None:
    """Appends all upstream dialects and extensions to the dialect registry."""
    return MLIR_func["mlirRegisterAllDialects", NoneType._mlir_type](registry)


fn mlirRegisterAllLLVMTranslations(context: MlirContext) -> None:
    """Register all translations to LLVM IR for dialects that can support it."""
    return MLIR_func["mlirRegisterAllLLVMTranslations", NoneType._mlir_type](
        context
    )


fn mlirRegisterAllPasses() -> None:
    """Register all compiler passes of MLIR."""
    return MLIR_func["mlirRegisterAllPasses", NoneType._mlir_type]()
