# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:27.707789 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #


from .ffi import MLIR_func
from .IR import *
from .Support import *

# ===-- mlir-c/Rewrite.h - Helpers for C API to Rewrites ----------*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//
#
#  This header declares the registration and creation method for
#  rewrite patterns.
#
# ===----------------------------------------------------------------------===//


# ===----------------------------------------------------------------------===//
#  Opaque type declarations (see mlir-c/IR.h for more details).
# ===----------------------------------------------------------------------===//


@register_passable("trivial")
struct MlirRewriterBase:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirFrozenRewritePatternSet:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirGreedyRewriteDriverConfig:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirRewritePatternSet:
    var ptr: OpaquePointer


# ===----------------------------------------------------------------------===//
#  RewriterBase API inherited from OpBuilder
# ===----------------------------------------------------------------------===//


fn mlirRewriterBaseGetContext(rewriter: MlirRewriterBase) -> MlirContext:
    """Get the MLIR context referenced by the rewriter."""
    return MLIR_func["mlirRewriterBaseGetContext", MlirContext](rewriter)


# ===----------------------------------------------------------------------===//
#  Insertion points methods

#  These do not include functions using Block::iterator or Region::iterator, as
#  they are not exposed by the C API yet. Similarly for methods using
#  `InsertPoint` directly.


fn mlirRewriterBaseClearInsertionPoint(rewriter: MlirRewriterBase) -> None:
    """Reset the insertion point to no location.  Creating an operation without a
    set insertion point is an error, but this can still be useful when the
    current insertion point a builder refers to is being removed."""
    return MLIR_func[
        "mlirRewriterBaseClearInsertionPoint", NoneType._mlir_type
    ](rewriter)


fn mlirRewriterBaseSetInsertionPointBefore(
    rewriter: MlirRewriterBase, op: MlirOperation
) -> None:
    """Sets the insertion point to the specified operation, which will cause
    subsequent insertions to go right before it."""
    return MLIR_func[
        "mlirRewriterBaseSetInsertionPointBefore", NoneType._mlir_type
    ](rewriter, op)


fn mlirRewriterBaseSetInsertionPointAfter(
    rewriter: MlirRewriterBase, op: MlirOperation
) -> None:
    """Sets the insertion point to the node after the specified operation, which
    will cause subsequent insertions to go right after it."""
    return MLIR_func[
        "mlirRewriterBaseSetInsertionPointAfter", NoneType._mlir_type
    ](rewriter, op)


fn mlirRewriterBaseSetInsertionPointAfterValue(
    rewriter: MlirRewriterBase, value: MlirValue
) -> None:
    """Sets the insertion point to the node after the specified value. If value
    has a defining operation, sets the insertion point to the node after such
    defining operation. This will cause subsequent insertions to go right
    after it. Otherwise, value is a BlockArgument. Sets the insertion point to
    the start of its block."""
    return MLIR_func[
        "mlirRewriterBaseSetInsertionPointAfterValue", NoneType._mlir_type
    ](rewriter, value)


fn mlirRewriterBaseSetInsertionPointToStart(
    rewriter: MlirRewriterBase, block: MlirBlock
) -> None:
    """Sets the insertion point to the start of the specified block."""
    return MLIR_func[
        "mlirRewriterBaseSetInsertionPointToStart", NoneType._mlir_type
    ](rewriter, block)


fn mlirRewriterBaseSetInsertionPointToEnd(
    rewriter: MlirRewriterBase, block: MlirBlock
) -> None:
    """Sets the insertion point to the end of the specified block."""
    return MLIR_func[
        "mlirRewriterBaseSetInsertionPointToEnd", NoneType._mlir_type
    ](rewriter, block)


fn mlirRewriterBaseGetInsertionBlock(rewriter: MlirRewriterBase) -> MlirBlock:
    """Return the block the current insertion point belongs to.  Note that the
    insertion point is not necessarily the end of the block."""
    return MLIR_func["mlirRewriterBaseGetInsertionBlock", MlirBlock](rewriter)


fn mlirRewriterBaseGetBlock(rewriter: MlirRewriterBase) -> MlirBlock:
    """Returns the current block of the rewriter."""
    return MLIR_func["mlirRewriterBaseGetBlock", MlirBlock](rewriter)


# ===----------------------------------------------------------------------===//
#  Block and operation creation/insertion/cloning

#  These functions do not include the IRMapper, as it is not yet exposed by the
#  C API.


fn mlirRewriterBaseCreateBlockBefore(
    rewriter: MlirRewriterBase,
    insert_before: MlirBlock,
    n_arg_types: Int,
    arg_types: UnsafePointer[MlirType],
    locations: UnsafePointer[MlirLocation],
) -> MlirBlock:
    """Add new block with 'argTypes' arguments and set the insertion point to the
    end of it. The block is placed before 'insertBefore'. `locs` contains the
    locations of the inserted arguments, and should match the size of
    `argTypes`."""
    return MLIR_func["mlirRewriterBaseCreateBlockBefore", MlirBlock](
        rewriter, insert_before, n_arg_types, arg_types, locations
    )


fn mlirRewriterBaseInsert(
    rewriter: MlirRewriterBase, op: MlirOperation
) -> MlirOperation:
    """Insert the given operation at the current insertion point and return it.
    """
    return MLIR_func["mlirRewriterBaseInsert", MlirOperation](rewriter, op)


fn mlirRewriterBaseClone(
    rewriter: MlirRewriterBase, op: MlirOperation
) -> MlirOperation:
    """Creates a deep copy of the specified operation."""
    return MLIR_func["mlirRewriterBaseClone", MlirOperation](rewriter, op)


fn mlirRewriterBaseCloneWithoutRegions(
    rewriter: MlirRewriterBase, op: MlirOperation
) -> MlirOperation:
    """Creates a deep copy of this operation but keep the operation regions
    empty."""
    return MLIR_func["mlirRewriterBaseCloneWithoutRegions", MlirOperation](
        rewriter, op
    )


fn mlirRewriterBaseCloneRegionBefore(
    rewriter: MlirRewriterBase, region: MlirRegion, before: MlirBlock
) -> None:
    """Clone the blocks that belong to "region" before the given position in
    another region "parent"."""
    return MLIR_func["mlirRewriterBaseCloneRegionBefore", NoneType._mlir_type](
        rewriter, region, before
    )


# ===----------------------------------------------------------------------===//
#  RewriterBase API
# ===----------------------------------------------------------------------===//


fn mlirRewriterBaseInlineRegionBefore(
    rewriter: MlirRewriterBase, region: MlirRegion, before: MlirBlock
) -> None:
    """Move the blocks that belong to "region" before the given position in
    another region "parent". The two regions must be different. The caller
    is responsible for creating or updating the operation transferring flow
    of control to the region and passing it the correct block arguments."""
    return MLIR_func["mlirRewriterBaseInlineRegionBefore", NoneType._mlir_type](
        rewriter, region, before
    )


fn mlirRewriterBaseReplaceOpWithValues(
    rewriter: MlirRewriterBase,
    op: MlirOperation,
    n_values: Int,
    values: UnsafePointer[MlirValue],
) -> None:
    """Replace the results of the given (original) operation with the specified
    list of values (replacements). The result types of the given op and the
    replacements must match. The original op is erased."""
    return MLIR_func[
        "mlirRewriterBaseReplaceOpWithValues", NoneType._mlir_type
    ](rewriter, op, n_values, values)


fn mlirRewriterBaseReplaceOpWithOperation(
    rewriter: MlirRewriterBase, op: MlirOperation, new_op: MlirOperation
) -> None:
    """Replace the results of the given (original) operation with the specified
    new op (replacement). The result types of the two ops must match. The
    original op is erased."""
    return MLIR_func[
        "mlirRewriterBaseReplaceOpWithOperation", NoneType._mlir_type
    ](rewriter, op, new_op)


fn mlirRewriterBaseEraseOp(
    rewriter: MlirRewriterBase, op: MlirOperation
) -> None:
    """Erases an operation that is known to have no uses."""
    return MLIR_func["mlirRewriterBaseEraseOp", NoneType._mlir_type](
        rewriter, op
    )


fn mlirRewriterBaseEraseBlock(
    rewriter: MlirRewriterBase, block: MlirBlock
) -> None:
    """Erases a block along with all operations inside it."""
    return MLIR_func["mlirRewriterBaseEraseBlock", NoneType._mlir_type](
        rewriter, block
    )


fn mlirRewriterBaseInlineBlockBefore(
    rewriter: MlirRewriterBase,
    source: MlirBlock,
    op: MlirOperation,
    n_arg_values: Int,
    arg_values: UnsafePointer[MlirValue],
) -> None:
    """Inline the operations of block 'source' before the operation 'op'. The
    source block will be deleted and must have no uses. 'argValues' is used to
    replace the block arguments of 'source'.

    The source block must have no successors. Otherwise, the resulting IR
    would have unreachable operations."""
    return MLIR_func["mlirRewriterBaseInlineBlockBefore", NoneType._mlir_type](
        rewriter, source, op, n_arg_values, arg_values
    )


fn mlirRewriterBaseMergeBlocks(
    rewriter: MlirRewriterBase,
    source: MlirBlock,
    dest: MlirBlock,
    n_arg_values: Int,
    arg_values: UnsafePointer[MlirValue],
) -> None:
    """Inline the operations of block 'source' into the end of block 'dest'. The
    source block will be deleted and must have no uses. 'argValues' is used to
    replace the block arguments of 'source'.

    The dest block must have no successors. Otherwise, the resulting IR would
    have unreachable operation."""
    return MLIR_func["mlirRewriterBaseMergeBlocks", NoneType._mlir_type](
        rewriter, source, dest, n_arg_values, arg_values
    )


fn mlirRewriterBaseMoveOpBefore(
    rewriter: MlirRewriterBase, op: MlirOperation, existing_op: MlirOperation
) -> None:
    """Unlink this operation from its current block and insert it right before
    `existingOp` which may be in the same or another block in the same
    function."""
    return MLIR_func["mlirRewriterBaseMoveOpBefore", NoneType._mlir_type](
        rewriter, op, existing_op
    )


fn mlirRewriterBaseMoveOpAfter(
    rewriter: MlirRewriterBase, op: MlirOperation, existing_op: MlirOperation
) -> None:
    """Unlink this operation from its current block and insert it right after
    `existingOp` which may be in the same or another block in the same
    function."""
    return MLIR_func["mlirRewriterBaseMoveOpAfter", NoneType._mlir_type](
        rewriter, op, existing_op
    )


fn mlirRewriterBaseMoveBlockBefore(
    rewriter: MlirRewriterBase, block: MlirBlock, existing_block: MlirBlock
) -> None:
    """Unlink this block and insert it right before `existingBlock`."""
    return MLIR_func["mlirRewriterBaseMoveBlockBefore", NoneType._mlir_type](
        rewriter, block, existing_block
    )


fn mlirRewriterBaseStartOpModification(
    rewriter: MlirRewriterBase, op: MlirOperation
) -> None:
    """This method is used to notify the rewriter that an in-place operation
    modification is about to happen. A call to this function *must* be
    followed by a call to either `finalizeOpModification` or
    `cancelOpModification`. This is a minor efficiency win (it avoids creating
    a new operation and removing the old one) but also often allows simpler
    code in the client."""
    return MLIR_func[
        "mlirRewriterBaseStartOpModification", NoneType._mlir_type
    ](rewriter, op)


fn mlirRewriterBaseFinalizeOpModification(
    rewriter: MlirRewriterBase, op: MlirOperation
) -> None:
    """This method is used to signal the end of an in-place modification of the
    given operation. This can only be called on operations that were provided
    to a call to `startOpModification`."""
    return MLIR_func[
        "mlirRewriterBaseFinalizeOpModification", NoneType._mlir_type
    ](rewriter, op)


fn mlirRewriterBaseCancelOpModification(
    rewriter: MlirRewriterBase, op: MlirOperation
) -> None:
    """This method cancels a pending in-place modification. This can only be
    called on operations that were provided to a call to
    `startOpModification`."""
    return MLIR_func[
        "mlirRewriterBaseCancelOpModification", NoneType._mlir_type
    ](rewriter, op)


fn mlirRewriterBaseReplaceAllUsesWith(
    rewriter: MlirRewriterBase, `from`: MlirValue, to: MlirValue
) -> None:
    """Find uses of `from` and replace them with `to`. Also notify the listener
    about every in-place op modification (for every use that was replaced)."""
    return MLIR_func["mlirRewriterBaseReplaceAllUsesWith", NoneType._mlir_type](
        rewriter, `from`, to
    )


fn mlirRewriterBaseReplaceAllValueRangeUsesWith(
    rewriter: MlirRewriterBase,
    n_values: Int,
    `from`: UnsafePointer[MlirValue],
    to: UnsafePointer[MlirValue],
) -> None:
    """Find uses of `from` and replace them with `to`. Also notify the listener
    about every in-place op modification (for every use that was replaced)."""
    return MLIR_func[
        "mlirRewriterBaseReplaceAllValueRangeUsesWith", NoneType._mlir_type
    ](rewriter, n_values, `from`, to)


fn mlirRewriterBaseReplaceAllOpUsesWithValueRange(
    rewriter: MlirRewriterBase,
    `from`: MlirOperation,
    n_to: Int,
    to: UnsafePointer[MlirValue],
) -> None:
    """Find uses of `from` and replace them with `to`. Also notify the listener
    about every in-place op modification (for every use that was replaced)
    and that the `from` operation is about to be replaced."""
    return MLIR_func[
        "mlirRewriterBaseReplaceAllOpUsesWithValueRange", NoneType._mlir_type
    ](rewriter, `from`, n_to, to)


fn mlirRewriterBaseReplaceAllOpUsesWithOperation(
    rewriter: MlirRewriterBase, `from`: MlirOperation, to: MlirOperation
) -> None:
    """Find uses of `from` and replace them with `to`. Also notify the listener
    about every in-place op modification (for every use that was replaced)
    and that the `from` operation is about to be replaced."""
    return MLIR_func[
        "mlirRewriterBaseReplaceAllOpUsesWithOperation", NoneType._mlir_type
    ](rewriter, `from`, to)


fn mlirRewriterBaseReplaceOpUsesWithinBlock(
    rewriter: MlirRewriterBase,
    op: MlirOperation,
    n_new_values: Int,
    new_values: UnsafePointer[MlirValue],
    block: MlirBlock,
) -> None:
    """Find uses of `from` within `block` and replace them with `to`. Also notify
    the listener about every in-place op modification (for every use that was
    replaced). The optional `allUsesReplaced` flag is set to "true" if all
    uses were replaced."""
    return MLIR_func[
        "mlirRewriterBaseReplaceOpUsesWithinBlock", NoneType._mlir_type
    ](rewriter, op, n_new_values, new_values, block)


fn mlirRewriterBaseReplaceAllUsesExcept(
    rewriter: MlirRewriterBase,
    `from`: MlirValue,
    to: MlirValue,
    excepted_user: MlirOperation,
) -> None:
    """Find uses of `from` and replace them with `to` except if the user is
    `exceptedUser`. Also notify the listener about every in-place op
    modification (for every use that was replaced)."""
    return MLIR_func[
        "mlirRewriterBaseReplaceAllUsesExcept", NoneType._mlir_type
    ](rewriter, `from`, to, excepted_user)


# ===----------------------------------------------------------------------===//
#  IRRewriter API
# ===----------------------------------------------------------------------===//


fn mlirIRRewriterCreate(context: MlirContext) -> MlirRewriterBase:
    """Create an IRRewriter and transfer ownership to the caller."""
    return MLIR_func["mlirIRRewriterCreate", MlirRewriterBase](context)


fn mlirIRRewriterCreateFromOp(op: MlirOperation) -> MlirRewriterBase:
    """Create an IRRewriter and transfer ownership to the caller. Additionally
    set the insertion point before the operation."""
    return MLIR_func["mlirIRRewriterCreateFromOp", MlirRewriterBase](op)


fn mlirIRRewriterDestroy(rewriter: MlirRewriterBase) -> None:
    """Takes an IRRewriter owned by the caller and destroys it. It is the
    responsibility of the user to only pass an IRRewriter class."""
    return MLIR_func["mlirIRRewriterDestroy", NoneType._mlir_type](rewriter)


# ===----------------------------------------------------------------------===//
#  FrozenRewritePatternSet API
# ===----------------------------------------------------------------------===//


fn mlirFreezeRewritePattern(
    op: MlirRewritePatternSet,
) -> MlirFrozenRewritePatternSet:
    """FrozenRewritePatternSet API."""
    return MLIR_func["mlirFreezeRewritePattern", MlirFrozenRewritePatternSet](
        op
    )


fn mlirFrozenRewritePatternSetDestroy(op: MlirFrozenRewritePatternSet) -> None:
    return MLIR_func["mlirFrozenRewritePatternSetDestroy", NoneType._mlir_type](
        op
    )


fn mlirApplyPatternsAndFoldGreedily(
    op: MlirModule,
    patterns: MlirFrozenRewritePatternSet,
    a: MlirGreedyRewriteDriverConfig,
) -> MlirLogicalResult:
    return MLIR_func["mlirApplyPatternsAndFoldGreedily", MlirLogicalResult](
        op, patterns, a
    )


# ===----------------------------------------------------------------------===//
#  PDLPatternModule API
# ===----------------------------------------------------------------------===//


fn mlirPDLPatternModuleFromModule(op: MlirModule) -> MlirPDLPatternModule:
    return MLIR_func["mlirPDLPatternModuleFromModule", MlirPDLPatternModule](op)


fn mlirPDLPatternModuleDestroy(op: MlirPDLPatternModule) -> None:
    return MLIR_func["mlirPDLPatternModuleDestroy", NoneType._mlir_type](op)


fn mlirRewritePatternSetFromPDLPatternModule(
    op: MlirPDLPatternModule,
) -> MlirRewritePatternSet:
    return MLIR_func[
        "mlirRewritePatternSetFromPDLPatternModule", MlirRewritePatternSet
    ](op)


# ===----------------------------------------------------------------------=== #
#     Codegen: Remaining symbols
# ===----------------------------------------------------------------------=== #


@register_passable("trivial")
struct MlirPDLPatternModule:
    var ptr: OpaquePointer
