# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:25.773223 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #


from .ffi import MLIR_func
from .Support import *

# ===-- mlir-c/Debug.h - C API for MLIR/LLVM debugging functions --*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//


fn mlirEnableGlobalDebug(enable: Bool) -> None:
    """Sets the global debugging flag."""
    return MLIR_func["mlirEnableGlobalDebug", NoneType._mlir_type](enable)


fn mlirIsGlobalDebugEnabled() -> Bool:
    """Returns `true` if the global debugging flag is set, false otherwise."""
    return MLIR_func["mlirIsGlobalDebugEnabled", Bool]()


fn mlirSetGlobalDebugType(type: UnsafePointer[Int8]) -> None:
    """Sets the current debug type, similarly to `-debug-only=type` in the
    command-line tools. Note that global debug should be enabled for any output
    to be produced."""
    return MLIR_func["mlirSetGlobalDebugType", NoneType._mlir_type](type)


fn mlirSetGlobalDebugTypes(
    types: UnsafePointer[UnsafePointer[Int8]], n: Int
) -> None:
    """Sets multiple current debug types, similarly to `-debug-only=type1,type2" in
    the command-line tools. Note that global debug should be enabled for any
    output to be produced."""
    return MLIR_func["mlirSetGlobalDebugTypes", NoneType._mlir_type](types, n)


fn mlirIsCurrentDebugType(type: UnsafePointer[Int8]) -> Bool:
    """Checks if `type` is set as the current debug type."""
    return MLIR_func["mlirIsCurrentDebugType", Bool](type)
