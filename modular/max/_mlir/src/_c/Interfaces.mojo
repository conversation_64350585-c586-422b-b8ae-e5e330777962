# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:25.595288 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #


from .ffi import MLIR_func
from .IR import *
from .Support import *

# ===-- mlir-c/Interfaces.h - C API to Core MLIR IR interfaces ----*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//
#
#  This header declares the C interface to MLIR interface classes. It is
#  intended to contain interfaces defined in lib/Interfaces.
#
# ===----------------------------------------------------------------------===//


fn mlirOperationImplementsInterface(
    operation: MlirOperation, interface_type_id: MlirTypeID
) -> Bool:
    """Returns `true` if the given operation implements an interface identified by
    its TypeID."""
    return MLIR_func["mlirOperationImplementsInterface", Bool](
        operation, interface_type_id
    )


fn mlirOperationImplementsInterfaceStatic(
    operation_name: MlirStringRef,
    context: MlirContext,
    interface_type_id: MlirTypeID,
) -> Bool:
    """Returns `true` if the operation identified by its canonical string name
    implements the interface identified by its TypeID in the given context.
    Note that interfaces may be attached to operations in some contexts and not
    others."""
    return MLIR_func["mlirOperationImplementsInterfaceStatic", Bool](
        operation_name, context, interface_type_id
    )


# ===----------------------------------------------------------------------===//
#  InferTypeOpInterface.
# ===----------------------------------------------------------------------===//


fn mlirInferTypeOpInterfaceTypeID() -> MlirTypeID:
    """Returns the interface TypeID of the InferTypeOpInterface."""
    return MLIR_func["mlirInferTypeOpInterfaceTypeID", MlirTypeID]()


# These callbacks are used to return multiple types from functions while
# transferring ownership to the caller. The first argument is the number of
# consecutive elements pointed to by the second argument. The third argument
# is an opaque pointer forwarded to the callback by the caller.
alias MlirTypesCallback = fn (
    Int32, UnsafePointer[MlirType], OpaquePointer
) -> None


fn mlirInferTypeOpInterfaceInferReturnTypes(
    op_name: MlirStringRef,
    context: MlirContext,
    location: MlirLocation,
    n_operands: Int,
    operands: UnsafePointer[MlirValue],
    attributes: MlirAttribute,
    properties: OpaquePointer,
    n_regions: Int,
    regions: UnsafePointer[MlirRegion],
    callback: MlirTypesCallback,
    user_data: OpaquePointer,
) -> MlirLogicalResult:
    """Infers the return types of the operation identified by its canonical given
    the arguments that will be supplied to its generic builder. Calls `callback`
    with the types of inferred arguments, potentially several times, on success.
    Returns failure otherwise."""
    return MLIR_func[
        "mlirInferTypeOpInterfaceInferReturnTypes", MlirLogicalResult
    ](
        op_name,
        context,
        location,
        n_operands,
        operands,
        attributes,
        properties,
        n_regions,
        regions,
        callback,
        user_data,
    )


# ===----------------------------------------------------------------------===//
#  InferShapedTypeOpInterface.
# ===----------------------------------------------------------------------===//


fn mlirInferShapedTypeOpInterfaceTypeID() -> MlirTypeID:
    """Returns the interface TypeID of the InferShapedTypeOpInterface."""
    return MLIR_func["mlirInferShapedTypeOpInterfaceTypeID", MlirTypeID]()


# These callbacks are used to return multiple shaped type components from
# functions while transferring ownership to the caller. The first argument is
# the 'has rank' boolean followed by the rank and a pointer to the shape
# (if applicable). The next argument is the element type, then the attribute.
# The last argument is an opaque pointer forwarded to the callback by the
# caller. This callback will be called potentially multiple times for each
# shaped type components.
alias MlirShapedTypeComponentsCallback = fn (
    Bool,
    Int32,
    UnsafePointer[Int64],
    MlirType,
    MlirAttribute,
    OpaquePointer,
) -> None


fn mlirInferShapedTypeOpInterfaceInferReturnTypes(
    op_name: MlirStringRef,
    context: MlirContext,
    location: MlirLocation,
    n_operands: Int,
    operands: UnsafePointer[MlirValue],
    attributes: MlirAttribute,
    properties: OpaquePointer,
    n_regions: Int,
    regions: UnsafePointer[MlirRegion],
    callback: MlirShapedTypeComponentsCallback,
    user_data: OpaquePointer,
) -> MlirLogicalResult:
    """Infers the return shaped type components of the operation. Calls `callback`
    with the types of inferred arguments on success. Returns failure otherwise.
    """
    return MLIR_func[
        "mlirInferShapedTypeOpInterfaceInferReturnTypes", MlirLogicalResult
    ](
        op_name,
        context,
        location,
        n_operands,
        operands,
        attributes,
        properties,
        n_regions,
        regions,
        callback,
        user_data,
    )
