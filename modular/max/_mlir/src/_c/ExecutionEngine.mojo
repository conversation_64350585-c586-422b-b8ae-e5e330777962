# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:25.900408 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #


from .ffi import MLIR_func
from .IR import *
from .Support import *

# ===-- mlir-c/ExecutionEngine.h - Execution engine management ---*- C -*-====//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//
#
#  This header provides basic access to the MLIR JIT. This is minimalist and
#  experimental at the moment.
#
# ===----------------------------------------------------------------------===//


@register_passable("trivial")
struct MlirExecutionEngine:
    var ptr: OpaquePointer


fn mlirExecutionEngineCreate(
    op: MlirModule,
    opt_level: Int16,
    num_paths: Int16,
    shared_lib_paths: UnsafePointer[MlirStringRef],
    enable_object_dump: Bool,
) -> MlirExecutionEngine:
    """Creates an ExecutionEngine for the provided ModuleOp. The ModuleOp is
    expected to be "translatable" to LLVM IR (only contains operations in
    dialects that implement the `LLVMTranslationDialectInterface`). The module
    ownership stays with the client and can be destroyed as soon as the call
    returns. `optLevel` is the optimization level to be used for transformation
    and code generation. LLVM passes at `optLevel` are run before code
    generation. The number and array of paths corresponding to shared libraries
    that will be loaded are specified via `numPaths` and `sharedLibPaths`
    respectively.
    TODO: figure out other options."""
    return MLIR_func["mlirExecutionEngineCreate", MlirExecutionEngine](
        op, opt_level, num_paths, shared_lib_paths, enable_object_dump
    )


fn mlirExecutionEngineDestroy(jit: MlirExecutionEngine) -> None:
    """Destroy an ExecutionEngine instance."""
    return MLIR_func["mlirExecutionEngineDestroy", NoneType._mlir_type](jit)


# FIXME(codegen): static function mlirExecutionEngineIsNull


fn mlirExecutionEngineInvokePacked(
    jit: MlirExecutionEngine,
    name: MlirStringRef,
    arguments: UnsafePointer[OpaquePointer],
) -> MlirLogicalResult:
    """Invoke a native function in the execution engine by name with the arguments
    and result of the invoked function passed as an array of pointers. The
    function must have been tagged with the `llvm.emit_c_interface` attribute.
    Returns a failure if the execution fails for any reason (the function name
    can't be resolved for instance)."""
    return MLIR_func["mlirExecutionEngineInvokePacked", MlirLogicalResult](
        jit, name, arguments
    )


fn mlirExecutionEngineLookupPacked(
    jit: MlirExecutionEngine, name: MlirStringRef
) -> OpaquePointer:
    """Lookup the wrapper of the native function in the execution engine with the
    given name, returns nullptr if the function can't be looked-up."""
    return MLIR_func["mlirExecutionEngineLookupPacked", OpaquePointer](
        jit, name
    )


fn mlirExecutionEngineLookup(
    jit: MlirExecutionEngine, name: MlirStringRef
) -> OpaquePointer:
    """Lookup a native function in the execution engine by name, returns nullptr
    if the name can't be looked-up."""
    return MLIR_func["mlirExecutionEngineLookup", OpaquePointer](jit, name)


fn mlirExecutionEngineRegisterSymbol(
    jit: MlirExecutionEngine, name: MlirStringRef, sym: OpaquePointer
) -> None:
    """Register a symbol with the jit: this symbol will be accessible to the jitted
    code."""
    return MLIR_func["mlirExecutionEngineRegisterSymbol", NoneType._mlir_type](
        jit, name, sym
    )


fn mlirExecutionEngineDumpToObjectFile(
    jit: MlirExecutionEngine, file_name: MlirStringRef
) -> None:
    """Dump as an object in `fileName`."""
    return MLIR_func[
        "mlirExecutionEngineDumpToObjectFile", NoneType._mlir_type
    ](jit, file_name)
