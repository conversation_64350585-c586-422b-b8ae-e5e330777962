# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:27.539788 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #


from utils.write import _WriteBufferStack

from .AffineExpr import *
from .ffi import MLIR_func
from .IR import *

# ===-- mlir-c/AffineMap.h - C API for MLIR Affine maps -----------*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//


# ===----------------------------------------------------------------------===//
#  Opaque type declarations.
#
#  Types are exposed to C bindings as structs containing opaque pointers. They
#  are not supposed to be inspected from C. This allows the underlying
#  representation to change without affecting the API users. The use of structs
#  instead of typedefs enables some type safety as structs are not implicitly
#  convertible to each other.
#
#  Instances of these types may or may not own the underlying object. The
#  ownership semantics is defined by how an instance of the type was obtained.
# ===----------------------------------------------------------------------===//


@register_passable("trivial")
struct MlirAffineMap:
    var ptr: OpaquePointer


fn mlirAffineMapGetContext(affine_map: MlirAffineMap) -> MlirContext:
    """Gets the context that the given affine map was created with."""
    return MLIR_func["mlirAffineMapGetContext", MlirContext](affine_map)


# FIXME(codegen): static function mlirAffineMapIsNull


fn mlirAffineMapEqual(a1: MlirAffineMap, a2: MlirAffineMap) -> Bool:
    """Checks if two affine maps are equal."""
    return MLIR_func["mlirAffineMapEqual", Bool](a1, a2)


fn mlirAffineMapPrint[W: Writer](mut writer: W, affine_map: MlirAffineMap):
    """Prints an affine map by sending chunks of the string representation and
    forwarding `userData to `callback`. Note that the callback may be called
    several times with consecutive chunks of the string."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirAffineMapPrint", NoneType._mlir_type](
        affine_map, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


fn mlirAffineMapDump(affine_map: MlirAffineMap) -> None:
    """Prints the affine map to the standard error stream."""
    return MLIR_func["mlirAffineMapDump", NoneType._mlir_type](affine_map)


fn mlirAffineMapEmptyGet(ctx: MlirContext) -> MlirAffineMap:
    """Creates a zero result affine map with no dimensions or symbols in the
    context. The affine map is owned by the context."""
    return MLIR_func["mlirAffineMapEmptyGet", MlirAffineMap](ctx)


fn mlirAffineMapZeroResultGet(
    ctx: MlirContext, dim_count: Int, symbol_count: Int
) -> MlirAffineMap:
    """Creates a zero result affine map of the given dimensions and symbols in the
    context. The affine map is owned by the context."""
    return MLIR_func["mlirAffineMapZeroResultGet", MlirAffineMap](
        ctx, dim_count, symbol_count
    )


#  Creates an affine map with results defined by the given list of affine
#  expressions. The map resulting map also has the requested number of input
#  dimensions and symbols, regardless of them being used in the results.


fn mlirAffineMapGet(
    ctx: MlirContext,
    dim_count: Int,
    symbol_count: Int,
    n_affine_exprs: Int,
    affine_exprs: UnsafePointer[MlirAffineExpr],
) -> MlirAffineMap:
    """Creates an affine map with results defined by the given list of affine
    expressions. The map resulting map also has the requested number of input
    dimensions and symbols, regardless of them being used in the results."""
    return MLIR_func["mlirAffineMapGet", MlirAffineMap](
        ctx, dim_count, symbol_count, n_affine_exprs, affine_exprs
    )


fn mlirAffineMapConstantGet(ctx: MlirContext, val: Int64) -> MlirAffineMap:
    """Creates a single constant result affine map in the context. The affine map
    is owned by the context."""
    return MLIR_func["mlirAffineMapConstantGet", MlirAffineMap](ctx, val)


fn mlirAffineMapMultiDimIdentityGet(
    ctx: MlirContext, num_dims: Int
) -> MlirAffineMap:
    """Creates an affine map with 'numDims' identity in the context. The affine map
    is owned by the context."""
    return MLIR_func["mlirAffineMapMultiDimIdentityGet", MlirAffineMap](
        ctx, num_dims
    )


fn mlirAffineMapMinorIdentityGet(
    ctx: MlirContext, dims: Int, results: Int
) -> MlirAffineMap:
    """Creates an identity affine map on the most minor dimensions in the context.
    The affine map is owned by the context. The function asserts that the number
    of dimensions is greater or equal to the number of results."""
    return MLIR_func["mlirAffineMapMinorIdentityGet", MlirAffineMap](
        ctx, dims, results
    )


fn mlirAffineMapPermutationGet(
    ctx: MlirContext, size: Int, permutation: UnsafePointer[Int16]
) -> MlirAffineMap:
    """Creates an affine map with a permutation expression and its size in the
    context. The permutation expression is a non-empty vector of integers.
    The elements of the permutation vector must be continuous from 0 and cannot
    be repeated (i.e. `[1,2,0]` is a valid permutation. `[2,0]` or `[1,1,2]` is
    an invalid permutation.) The affine map is owned by the context."""
    return MLIR_func["mlirAffineMapPermutationGet", MlirAffineMap](
        ctx, size, permutation
    )


fn mlirAffineMapIsIdentity(affine_map: MlirAffineMap) -> Bool:
    """Checks whether the given affine map is an identity affine map. The function
    asserts that the number of dimensions is greater or equal to the number of
    results."""
    return MLIR_func["mlirAffineMapIsIdentity", Bool](affine_map)


fn mlirAffineMapIsMinorIdentity(affine_map: MlirAffineMap) -> Bool:
    """Checks whether the given affine map is a minor identity affine map."""
    return MLIR_func["mlirAffineMapIsMinorIdentity", Bool](affine_map)


fn mlirAffineMapIsEmpty(affine_map: MlirAffineMap) -> Bool:
    """Checks whether the given affine map is an empty affine map."""
    return MLIR_func["mlirAffineMapIsEmpty", Bool](affine_map)


fn mlirAffineMapIsSingleConstant(affine_map: MlirAffineMap) -> Bool:
    """Checks whether the given affine map is a single result constant affine
    map."""
    return MLIR_func["mlirAffineMapIsSingleConstant", Bool](affine_map)


fn mlirAffineMapGetSingleConstantResult(affine_map: MlirAffineMap) -> Int64:
    """Returns the constant result of the given affine map. The function asserts
    that the map has a single constant result."""
    return MLIR_func["mlirAffineMapGetSingleConstantResult", Int64](affine_map)


fn mlirAffineMapGetNumDims(affine_map: MlirAffineMap) -> Int:
    """Returns the number of dimensions of the given affine map."""
    return MLIR_func["mlirAffineMapGetNumDims", Int](affine_map)


fn mlirAffineMapGetNumSymbols(affine_map: MlirAffineMap) -> Int:
    """Returns the number of symbols of the given affine map."""
    return MLIR_func["mlirAffineMapGetNumSymbols", Int](affine_map)


fn mlirAffineMapGetNumResults(affine_map: MlirAffineMap) -> Int:
    """Returns the number of results of the given affine map."""
    return MLIR_func["mlirAffineMapGetNumResults", Int](affine_map)


fn mlirAffineMapGetResult(
    affine_map: MlirAffineMap, pos: Int
) -> MlirAffineExpr:
    """Returns the result at the given position."""
    return MLIR_func["mlirAffineMapGetResult", MlirAffineExpr](affine_map, pos)


fn mlirAffineMapGetNumInputs(affine_map: MlirAffineMap) -> Int:
    """Returns the number of inputs (dimensions + symbols) of the given affine
    map."""
    return MLIR_func["mlirAffineMapGetNumInputs", Int](affine_map)


fn mlirAffineMapIsProjectedPermutation(affine_map: MlirAffineMap) -> Bool:
    """Checks whether the given affine map represents a subset of a symbol-less
    permutation map."""
    return MLIR_func["mlirAffineMapIsProjectedPermutation", Bool](affine_map)


fn mlirAffineMapIsPermutation(affine_map: MlirAffineMap) -> Bool:
    """Checks whether the given affine map represents a symbol-less permutation
    map."""
    return MLIR_func["mlirAffineMapIsPermutation", Bool](affine_map)


fn mlirAffineMapGetSubMap(
    affine_map: MlirAffineMap, size: Int, result_pos: UnsafePointer[Int]
) -> MlirAffineMap:
    """Returns the affine map consisting of the `resultPos` subset."""
    return MLIR_func["mlirAffineMapGetSubMap", MlirAffineMap](
        affine_map, size, result_pos
    )


fn mlirAffineMapGetMajorSubMap(
    affine_map: MlirAffineMap, num_results: Int
) -> MlirAffineMap:
    """Returns the affine map consisting of the most major `numResults` results.
    Returns the null AffineMap if the `numResults` is equal to zero.
    Returns the `affineMap` if `numResults` is greater or equals to number of
    results of the given affine map."""
    return MLIR_func["mlirAffineMapGetMajorSubMap", MlirAffineMap](
        affine_map, num_results
    )


fn mlirAffineMapGetMinorSubMap(
    affine_map: MlirAffineMap, num_results: Int
) -> MlirAffineMap:
    """Returns the affine map consisting of the most minor `numResults` results.
    Returns the null AffineMap if the `numResults` is equal to zero.
    Returns the `affineMap` if `numResults` is greater or equals to number of
    results of the given affine map."""
    return MLIR_func["mlirAffineMapGetMinorSubMap", MlirAffineMap](
        affine_map, num_results
    )


fn mlirAffineMapReplace(
    affine_map: MlirAffineMap,
    expression: MlirAffineExpr,
    replacement: MlirAffineExpr,
    num_result_dims: Int,
    num_result_syms: Int,
) -> MlirAffineMap:
    """Apply AffineExpr::replace(`map`) to each of the results and return a new
    new AffineMap with the new results and the specified number of dims and
    symbols."""
    return MLIR_func["mlirAffineMapReplace", MlirAffineMap](
        affine_map, expression, replacement, num_result_dims, num_result_syms
    )


fn mlirAffineMapCompressUnusedSymbols(
    affine_maps: UnsafePointer[MlirAffineMap],
    size: Int,
    result: OpaquePointer,
    populate_result: fn (OpaquePointer, Int32, MlirAffineMap) -> None,
) -> None:
    """Returns the simplified affine map resulting from dropping the symbols that
    do not appear in any of the individual maps in `affineMaps`.
    Asserts that all maps in `affineMaps` are normalized to the same number of
    dims and symbols.
    Takes a callback `populateResult` to fill the `res` container with value
    `m` at entry `idx`. This allows returning without worrying about ownership
    considerations."""
    return MLIR_func["mlirAffineMapCompressUnusedSymbols", NoneType._mlir_type](
        affine_maps, size, result, populate_result
    )
