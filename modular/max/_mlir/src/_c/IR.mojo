# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:26.311549 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #

from collections.string import StaticString


from utils.write import _WriteBufferStack

from .ffi import MLIR_func
from .Support import *

# ===-- mlir-c/IR.h - C API to Core MLIR IR classes ---------------*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//
#
#  This header declares the C interface to MLIR core IR classes.
#
#  Many exotic languages can interoperate with C code but have a harder time
#  with C++ due to name mangling. So in addition to C, this interface enables
#  tools written in such languages.
#
# ===----------------------------------------------------------------------===//


# ===----------------------------------------------------------------------===//
#  Opaque type declarations.
#
#  Types are exposed to C bindings as structs containing opaque pointers. They
#  are not supposed to be inspected from C. This allows the underlying
#  representation to change without affecting the API users. The use of structs
#  instead of typedefs enables some type safety as structs are not implicitly
#  convertible to each other.
#
#  Instances of these types may or may not own the underlying object (most
#  often only point to an IR fragment without owning it). The ownership
#  semantics is defined by how an instance of the type was obtained.

# ===----------------------------------------------------------------------===//


@register_passable("trivial")
struct MlirAsmState:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirBytecodeWriterConfig:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirContext:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirDialect:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirDialectRegistry:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirOperation:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirOpOperand:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirOpPrintingFlags:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirBlock:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirRegion:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirSymbolTable:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirAttribute:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirIdentifier:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirLocation:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirModule:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirType:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirValue:
    var ptr: OpaquePointer


@fieldwise_init
@register_passable("trivial")
struct MlirNamedAttribute(Copyable, Movable):
    """Named MLIR attribute.

    A named attribute is essentially a (name, attribute) pair where the name is
    a string."""

    var name: MlirIdentifier
    var attribute: MlirAttribute


# ===----------------------------------------------------------------------===//
#  write_buffered_callback.
# ===----------------------------------------------------------------------===//
# This is used by functions that write a string representation of
# the type being passed in:
#  - an MlirStringRef representing the current portion of the string
#  - a pointer to a buffer for any mutable `Writer` type.
fn write_buffered_callback[W: Writer](chunk: StaticString, data: OpaquePointer):
    var buffer = data.bitcast[_WriteBufferStack[origin=MutableAnyOrigin, W=W]]()
    buffer[].write(chunk)


# ===----------------------------------------------------------------------===//
#  Context API.
# ===----------------------------------------------------------------------===//


fn mlirContextCreate() -> MlirContext:
    """Creates an MLIR context and transfers its ownership to the caller.
    This sets the default multithreading option (enabled)."""
    return MLIR_func["mlirContextCreate", MlirContext]()


fn mlirContextCreateWithThreading(threading_enabled: Bool) -> MlirContext:
    """Creates an MLIR context with an explicit setting of the multithreading
    setting and transfers its ownership to the caller."""
    return MLIR_func["mlirContextCreateWithThreading", MlirContext](
        threading_enabled
    )


fn mlirContextCreateWithRegistry(
    registry: MlirDialectRegistry, threading_enabled: Bool
) -> MlirContext:
    """Creates an MLIR context, setting the multithreading setting explicitly and
    pre-loading the dialects from the provided DialectRegistry."""
    return MLIR_func["mlirContextCreateWithRegistry", MlirContext](
        registry, threading_enabled
    )


fn mlirContextEqual(ctx1: MlirContext, ctx2: MlirContext) -> Bool:
    """Checks if two contexts are equal."""
    return MLIR_func["mlirContextEqual", Bool](ctx1, ctx2)


# FIXME(codegen): static function mlirContextIsNull


fn mlirContextDestroy(context: MlirContext) -> None:
    """Takes an MLIR context owned by the caller and destroys it."""
    return MLIR_func["mlirContextDestroy", NoneType._mlir_type](context)


fn mlirContextSetAllowUnregisteredDialects(
    context: MlirContext, allow: Bool
) -> None:
    """Sets whether unregistered dialects are allowed in this context."""
    return MLIR_func[
        "mlirContextSetAllowUnregisteredDialects", NoneType._mlir_type
    ](context, allow)


fn mlirContextGetAllowUnregisteredDialects(context: MlirContext) -> Bool:
    """Returns whether the context allows unregistered dialects."""
    return MLIR_func["mlirContextGetAllowUnregisteredDialects", Bool](context)


fn mlirContextGetNumRegisteredDialects(context: MlirContext) -> Int:
    """Returns the number of dialects registered with the given context. A
    registered dialect will be loaded if needed by the parser."""
    return MLIR_func["mlirContextGetNumRegisteredDialects", Int](context)


fn mlirContextAppendDialectRegistry(
    ctx: MlirContext, registry: MlirDialectRegistry
) -> None:
    """Append the contents of the given dialect registry to the registry associated
    with the context."""
    return MLIR_func["mlirContextAppendDialectRegistry", NoneType._mlir_type](
        ctx, registry
    )


#  Returns the number of dialects loaded by the context.


fn mlirContextGetNumLoadedDialects(context: MlirContext) -> Int:
    """Returns the number of dialects loaded by the context."""
    return MLIR_func["mlirContextGetNumLoadedDialects", Int](context)


fn mlirContextGetOrLoadDialect(
    context: MlirContext, name: MlirStringRef
) -> MlirDialect:
    """Gets the dialect instance owned by the given context using the dialect
    namespace to identify it, loads (i.e., constructs the instance of) the
    dialect if necessary. If the dialect is not registered with the context,
    returns null. Use mlirContextLoad<Name>Dialect to load an unregistered
    dialect."""
    return MLIR_func["mlirContextGetOrLoadDialect", MlirDialect](context, name)


fn mlirContextEnableMultithreading(context: MlirContext, enable: Bool) -> None:
    """Set threading mode (must be set to false to mlir-print-ir-after-all)."""
    return MLIR_func["mlirContextEnableMultithreading", NoneType._mlir_type](
        context, enable
    )


fn mlirContextLoadAllAvailableDialects(context: MlirContext) -> None:
    """Eagerly loads all available dialects registered with a context, making
    them available for use for IR construction."""
    return MLIR_func[
        "mlirContextLoadAllAvailableDialects", NoneType._mlir_type
    ](context)


fn mlirContextIsRegisteredOperation(
    context: MlirContext, name: MlirStringRef
) -> Bool:
    """Returns whether the given fully-qualified operation (i.e.
    'dialect.operation') is registered with the context. This will return true
    if the dialect is loaded and the operation is registered within the
    dialect."""
    return MLIR_func["mlirContextIsRegisteredOperation", Bool](context, name)


fn mlirContextSetThreadPool(
    context: MlirContext, thread_pool: MlirLlvmThreadPool
) -> None:
    """Sets the thread pool of the context explicitly, enabling multithreading in
    the process. This API should be used to avoid re-creating thread pools in
    long-running applications that perform multiple compilations, see
    the C++ documentation for MLIRContext for details."""
    return MLIR_func["mlirContextSetThreadPool", NoneType._mlir_type](
        context, thread_pool
    )


# ===----------------------------------------------------------------------===//
#  Dialect API.
# ===----------------------------------------------------------------------===//


fn mlirDialectGetContext(dialect: MlirDialect) -> MlirContext:
    """Returns the context that owns the dialect."""
    return MLIR_func["mlirDialectGetContext", MlirContext](dialect)


# FIXME(codegen): static function mlirDialectIsNull


fn mlirDialectEqual(dialect1: MlirDialect, dialect2: MlirDialect) -> Bool:
    """Checks if two dialects that belong to the same context are equal. Dialects
    from different contexts will not compare equal."""
    return MLIR_func["mlirDialectEqual", Bool](dialect1, dialect2)


fn mlirDialectGetNamespace(dialect: MlirDialect) -> MlirStringRef:
    """Returns the namespace of the given dialect."""
    return MLIR_func["mlirDialectGetNamespace", MlirStringRef](dialect)


# ===----------------------------------------------------------------------===//
#  DialectHandle API.
#  Registration entry-points for each dialect are declared using the common
#  MLIR_DECLARE_DIALECT_REGISTRATION_CAPI macro, which takes the dialect
#  API name (i.e. "Func", "Tensor", "Linalg") and namespace (i.e. "func",
#  "tensor", "linalg"). The following declarations are produced:
#
#    /// Gets the above hook methods in struct form for a dialect by namespace.
#    /// This is intended to facilitate dynamic lookup and registration of
#    /// dialects via a plugin facility based on shared library symbol lookup.
#    const MlirDialectHandle *mlirGetDialectHandle__{NAMESPACE}__();
#
#  This is done via a common macro to facilitate future expansion to
#  registration schemes.
# ===----------------------------------------------------------------------===//


@register_passable("trivial")
struct MlirDialectHandle:
    var ptr: OpaquePointer


fn mlirDialectHandleGetNamespace(a: MlirDialectHandle) -> MlirStringRef:
    """Returns the namespace associated with the provided dialect handle."""
    return MLIR_func["mlirDialectHandleGetNamespace", MlirStringRef](a)


fn mlirDialectHandleInsertDialect(
    a: MlirDialectHandle, b: MlirDialectRegistry
) -> None:
    """Inserts the dialect associated with the provided dialect handle into the
    provided dialect registry."""
    return MLIR_func["mlirDialectHandleInsertDialect", NoneType._mlir_type](
        a, b
    )


fn mlirDialectHandleRegisterDialect(
    a: MlirDialectHandle, b: MlirContext
) -> None:
    """Registers the dialect associated with the provided dialect handle."""
    return MLIR_func["mlirDialectHandleRegisterDialect", NoneType._mlir_type](
        a, b
    )


fn mlirDialectHandleLoadDialect(
    a: MlirDialectHandle, b: MlirContext
) -> MlirDialect:
    """Loads the dialect associated with the provided dialect handle."""
    return MLIR_func["mlirDialectHandleLoadDialect", MlirDialect](a, b)


# ===----------------------------------------------------------------------===//
#  DialectRegistry API.
# ===----------------------------------------------------------------------===//


fn mlirDialectRegistryCreate() -> MlirDialectRegistry:
    """Creates a dialect registry and transfers its ownership to the caller."""
    return MLIR_func["mlirDialectRegistryCreate", MlirDialectRegistry]()


# FIXME(codegen): static function mlirDialectRegistryIsNull


fn mlirDialectRegistryDestroy(registry: MlirDialectRegistry) -> None:
    """Takes a dialect registry owned by the caller and destroys it."""
    return MLIR_func["mlirDialectRegistryDestroy", NoneType._mlir_type](
        registry
    )


# ===----------------------------------------------------------------------===//
#  Location API.
# ===----------------------------------------------------------------------===//


fn mlirLocationGetAttribute(location: MlirLocation) -> MlirAttribute:
    """Returns the underlying location attribute of this location."""
    return MLIR_func["mlirLocationGetAttribute", MlirAttribute](location)


fn mlirLocationFromAttribute(attribute: MlirAttribute) -> MlirLocation:
    """Creates a location from a location attribute."""
    return MLIR_func["mlirLocationFromAttribute", MlirLocation](attribute)


fn mlirLocationFileLineColGet(
    context: MlirContext, filename: MlirStringRef, line: Int16, col: Int16
) -> MlirLocation:
    """Creates an File/Line/Column location owned by the given context."""
    return MLIR_func["mlirLocationFileLineColGet", MlirLocation](
        context, filename, line, col
    )


fn mlirLocationCallSiteGet(
    callee: MlirLocation, caller: MlirLocation
) -> MlirLocation:
    """Creates a call site location with a callee and a caller."""
    return MLIR_func["mlirLocationCallSiteGet", MlirLocation](callee, caller)


fn mlirLocationFusedGet(
    ctx: MlirContext,
    n_locations: Int,
    locations: UnsafePointer[MlirLocation],
    metadata: MlirAttribute,
) -> MlirLocation:
    """Creates a fused location with an array of locations and metadata."""
    return MLIR_func["mlirLocationFusedGet", MlirLocation](
        ctx, n_locations, locations, metadata
    )


fn mlirLocationNameGet(
    context: MlirContext, name: MlirStringRef, child_loc: MlirLocation
) -> MlirLocation:
    """Creates a name location owned by the given context. Providing null location
    for childLoc is allowed and if childLoc is null location, then the behavior
    is the same as having unknown child location."""
    return MLIR_func["mlirLocationNameGet", MlirLocation](
        context, name, child_loc
    )


fn mlirLocationUnknownGet(context: MlirContext) -> MlirLocation:
    """Creates a location with unknown position owned by the given context."""
    return MLIR_func["mlirLocationUnknownGet", MlirLocation](context)


fn mlirLocationGetContext(location: MlirLocation) -> MlirContext:
    """Gets the context that a location was created with."""
    return MLIR_func["mlirLocationGetContext", MlirContext](location)


# FIXME(codegen): static function mlirLocationIsNull


fn mlirLocationEqual(l1: MlirLocation, l2: MlirLocation) -> Bool:
    """Checks if two locations are equal."""
    return MLIR_func["mlirLocationEqual", Bool](l1, l2)


fn mlirLocationPrint[W: Writer](mut writer: W, location: MlirLocation):
    """Prints a location by sending chunks of the string representation and
    forwarding `userData to `callback`. Note that the callback may be called
    several times with consecutive chunks of the string."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirLocationPrint", NoneType._mlir_type](
        location, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


# ===----------------------------------------------------------------------===//
#  Module API.
# ===----------------------------------------------------------------------===//


fn mlirModuleCreateEmpty(location: MlirLocation) -> MlirModule:
    """Creates a new, empty module and transfers ownership to the caller."""
    return MLIR_func["mlirModuleCreateEmpty", MlirModule](location)


fn mlirModuleCreateParse(
    context: MlirContext, module: MlirStringRef
) -> MlirModule:
    """Parses a module from the string and transfers ownership to the caller."""
    return MLIR_func["mlirModuleCreateParse", MlirModule](context, module)


fn mlirModuleGetContext(module: MlirModule) -> MlirContext:
    """Gets the context that a module was created with."""
    return MLIR_func["mlirModuleGetContext", MlirContext](module)


fn mlirModuleGetBody(module: MlirModule) -> MlirBlock:
    """Gets the body of the module, i.e. the only block it contains."""
    return MLIR_func["mlirModuleGetBody", MlirBlock](module)


# FIXME(codegen): static function mlirModuleIsNull


fn mlirModuleDestroy(module: MlirModule) -> None:
    """Takes a module owned by the caller and deletes it."""
    return MLIR_func["mlirModuleDestroy", NoneType._mlir_type](module)


fn mlirModuleGetOperation(module: MlirModule) -> MlirOperation:
    """Views the module as a generic operation."""
    return MLIR_func["mlirModuleGetOperation", MlirOperation](module)


fn mlirModuleFromOperation(op: MlirOperation) -> MlirModule:
    """Views the generic operation as a module.
    The returned module is null when the input operation was not a ModuleOp."""
    return MLIR_func["mlirModuleFromOperation", MlirModule](op)


# ===----------------------------------------------------------------------===//
#  Operation state.
# ===----------------------------------------------------------------------===//

#  An auxiliary class for constructing operations.
#
#  This class contains all the information necessary to construct the
#  operation. It owns the MlirRegions it has pointers to and does not own
#  anything else. By default, the state can be constructed from a name and
#  location, the latter being also used to access the context, and has no other
#  components. These components can be added progressively until the operation
#  is constructed. Users are not expected to rely on the internals of this
#  class and should use mlirOperationState* functions instead.


@register_passable("trivial")
struct MlirOperationState:
    """An auxiliary class for constructing operations.

    This class contains all the information necessary to construct the
    operation. It owns the MlirRegions it has pointers to and does not own
    anything else. By default, the state can be constructed from a name and
    location, the latter being also used to access the context, and has no other
    components. These components can be added progressively until the operation
    is constructed. Users are not expected to rely on the internals of this
    class and should use mlirOperationState* functions instead."""

    var name: MlirStringRef
    var location: MlirLocation
    var nResults: Int
    var results: UnsafePointer[MlirType]
    var nOperands: Int
    var operands: UnsafePointer[MlirValue]
    var nRegions: Int
    var regions: UnsafePointer[MlirRegion]
    var nSuccessors: Int
    var successors: UnsafePointer[MlirBlock]
    var nAttributes: Int
    var attributes: UnsafePointer[MlirNamedAttribute]
    var enableResultTypeInference: Bool


fn mlirOperationStateGet(
    name: MlirStringRef, loc: MlirLocation
) -> MlirOperationState:
    """Constructs an operation state from a name and a location."""
    return MLIR_func["mlirOperationStateGet", MlirOperationState](name, loc)


fn mlirOperationStateAddResults(
    state: UnsafePointer[MlirOperationState],
    n: Int,
    results: UnsafePointer[MlirType],
) -> None:
    """Adds a list of components to the operation state."""
    return MLIR_func["mlirOperationStateAddResults", NoneType._mlir_type](
        state, n, results
    )


fn mlirOperationStateAddOperands(
    state: UnsafePointer[MlirOperationState],
    n: Int,
    operands: UnsafePointer[MlirValue],
) -> None:
    return MLIR_func["mlirOperationStateAddOperands", NoneType._mlir_type](
        state, n, operands
    )


fn mlirOperationStateAddOwnedRegions(
    state: UnsafePointer[MlirOperationState],
    n: Int,
    regions: UnsafePointer[MlirRegion],
) -> None:
    return MLIR_func["mlirOperationStateAddOwnedRegions", NoneType._mlir_type](
        state, n, regions
    )


fn mlirOperationStateAddSuccessors(
    state: UnsafePointer[MlirOperationState],
    n: Int,
    successors: UnsafePointer[MlirBlock],
) -> None:
    return MLIR_func["mlirOperationStateAddSuccessors", NoneType._mlir_type](
        state, n, successors
    )


fn mlirOperationStateAddAttributes(
    state: UnsafePointer[MlirOperationState],
    n: Int,
    attributes: UnsafePointer[MlirNamedAttribute],
) -> None:
    return MLIR_func["mlirOperationStateAddAttributes", NoneType._mlir_type](
        state, n, attributes
    )


fn mlirOperationStateEnableResultTypeInference(
    state: UnsafePointer[MlirOperationState],
) -> None:
    """Enables result type inference for the operation under construction. If
    enabled, then the caller must not have called
    mlirOperationStateAddResults(). Note that if enabled, the
    mlirOperationCreate() call is failable: it will return a null operation
    on inference failure and will emit diagnostics."""
    return MLIR_func[
        "mlirOperationStateEnableResultTypeInference", NoneType._mlir_type
    ](state)


# ===----------------------------------------------------------------------===//
#  AsmState API.
#  While many of these are simple settings that could be represented in a
#  struct, they are wrapped in a heap allocated object and accessed via
#  functions to maximize the possibility of compatibility over time.
# ===----------------------------------------------------------------------===//


fn mlirAsmStateCreateForOperation(
    op: MlirOperation, flags: MlirOpPrintingFlags
) -> MlirAsmState:
    """Creates new AsmState, as with AsmState the IR should not be mutated
    in-between using this state.
    Must be freed with a call to mlirAsmStateDestroy()."""
    return MLIR_func["mlirAsmStateCreateForOperation", MlirAsmState](op, flags)


fn mlirAsmStateCreateForValue(
    value: MlirValue, flags: MlirOpPrintingFlags
) -> MlirAsmState:
    """Creates new AsmState from value.
    Must be freed with a call to mlirAsmStateDestroy()."""
    return MLIR_func["mlirAsmStateCreateForValue", MlirAsmState](value, flags)


fn mlirAsmStateDestroy(state: MlirAsmState) -> None:
    """Destroys printing flags created with mlirAsmStateCreate."""
    return MLIR_func["mlirAsmStateDestroy", NoneType._mlir_type](state)


# ===----------------------------------------------------------------------===//
#  Op Printing flags API.
#  While many of these are simple settings that could be represented in a
#  struct, they are wrapped in a heap allocated object and accessed via
#  functions to maximize the possibility of compatibility over time.
# ===----------------------------------------------------------------------===//


fn mlirOpPrintingFlagsCreate() -> MlirOpPrintingFlags:
    """Creates new printing flags with defaults, intended for customization.
    Must be freed with a call to mlirOpPrintingFlagsDestroy()."""
    return MLIR_func["mlirOpPrintingFlagsCreate", MlirOpPrintingFlags]()


fn mlirOpPrintingFlagsDestroy(flags: MlirOpPrintingFlags) -> None:
    """Destroys printing flags created with mlirOpPrintingFlagsCreate."""
    return MLIR_func["mlirOpPrintingFlagsDestroy", NoneType._mlir_type](flags)


fn mlirOpPrintingFlagsElideLargeElementsAttrs(
    flags: MlirOpPrintingFlags, large_element_limit: Int
) -> None:
    """Enables the elision of large elements attributes by printing a lexically
    valid but otherwise meaningless form instead of the element data. The
    `largeElementLimit` is used to configure what is considered to be a "large"
    ElementsAttr by providing an upper limit to the number of elements."""
    return MLIR_func[
        "mlirOpPrintingFlagsElideLargeElementsAttrs", NoneType._mlir_type
    ](flags, large_element_limit)


fn mlirOpPrintingFlagsElideLargeResourceString(
    flags: MlirOpPrintingFlags, large_resource_limit: Int
) -> None:
    """Enables the elision of large resources strings by omitting them from the
    `dialect_resources` section. The `largeResourceLimit` is used to configure
    what is considered to be a "large" resource by providing an upper limit to
    the string size."""
    return MLIR_func[
        "mlirOpPrintingFlagsElideLargeResourceString", NoneType._mlir_type
    ](flags, large_resource_limit)


fn mlirOpPrintingFlagsEnableDebugInfo(
    flags: MlirOpPrintingFlags, enable: Bool, pretty_form: Bool
) -> None:
    """Enable or disable printing of debug information (based on `enable`). If
    'prettyForm' is set to true, debug information is printed in a more readable
    'pretty' form. Note: The IR generated with 'prettyForm' is not parsable."""
    return MLIR_func["mlirOpPrintingFlagsEnableDebugInfo", NoneType._mlir_type](
        flags, enable, pretty_form
    )


fn mlirOpPrintingFlagsPrintGenericOpForm(flags: MlirOpPrintingFlags) -> None:
    """Always print operations in the generic form."""
    return MLIR_func[
        "mlirOpPrintingFlagsPrintGenericOpForm", NoneType._mlir_type
    ](flags)


fn mlirOpPrintingFlagsUseLocalScope(flags: MlirOpPrintingFlags) -> None:
    """Use local scope when printing the operation. This allows for using the
    printer in a more localized and thread-safe setting, but may not
    necessarily be identical to what the IR will look like when dumping
    the full module."""
    return MLIR_func["mlirOpPrintingFlagsUseLocalScope", NoneType._mlir_type](
        flags
    )


fn mlirOpPrintingFlagsAssumeVerified(flags: MlirOpPrintingFlags) -> None:
    """Do not verify the operation when using custom operation printers."""
    return MLIR_func["mlirOpPrintingFlagsAssumeVerified", NoneType._mlir_type](
        flags
    )


fn mlirOpPrintingFlagsSkipRegions(flags: MlirOpPrintingFlags) -> None:
    """Skip printing regions."""
    return MLIR_func["mlirOpPrintingFlagsSkipRegions", NoneType._mlir_type](
        flags
    )


# ===----------------------------------------------------------------------===//
#  Bytecode printing flags API.
# ===----------------------------------------------------------------------===//


fn mlirBytecodeWriterConfigCreate() -> MlirBytecodeWriterConfig:
    """Creates new printing flags with defaults, intended for customization.
    Must be freed with a call to mlirBytecodeWriterConfigDestroy()."""
    return MLIR_func[
        "mlirBytecodeWriterConfigCreate", MlirBytecodeWriterConfig
    ]()


fn mlirBytecodeWriterConfigDestroy(config: MlirBytecodeWriterConfig) -> None:
    """Destroys printing flags created with mlirBytecodeWriterConfigCreate."""
    return MLIR_func["mlirBytecodeWriterConfigDestroy", NoneType._mlir_type](
        config
    )


fn mlirBytecodeWriterConfigDesiredEmitVersion(
    flags: MlirBytecodeWriterConfig, version: Int64
) -> None:
    """Sets the version to emit in the writer config."""
    return MLIR_func[
        "mlirBytecodeWriterConfigDesiredEmitVersion", NoneType._mlir_type
    ](flags, version)


# ===----------------------------------------------------------------------===//
#  Operation API.
# ===----------------------------------------------------------------------===//


fn mlirOperationCreate(
    state: UnsafePointer[MlirOperationState],
) -> MlirOperation:
    """Creates an operation and transfers ownership to the caller.
    Note that caller owned child objects are transferred in this call and must
    not be further used. Particularly, this applies to any regions added to
    the state (the implementation may invalidate any such pointers).

    This call can fail under the following conditions, in which case, it will
    return a null operation and emit diagnostics:
      - Result type inference is enabled and cannot be performed."""
    return MLIR_func["mlirOperationCreate", MlirOperation](state)


fn mlirOperationCreateParse(
    context: MlirContext, source_str: MlirStringRef, source_name: MlirStringRef
) -> MlirOperation:
    """Parses an operation, giving ownership to the caller. If parsing fails a null
    operation will be returned, and an error diagnostic emitted.

    `sourceStr` may be either the text assembly format, or binary bytecode
    format. `sourceName` is used as the file name of the source; any IR without
    locations will get a `FileLineColLoc` location with `sourceName` as the file
    name."""
    return MLIR_func["mlirOperationCreateParse", MlirOperation](
        context, source_str, source_name
    )


fn mlirOperationClone(op: MlirOperation) -> MlirOperation:
    """Creates a deep copy of an operation. The operation is not inserted and
    ownership is transferred to the caller."""
    return MLIR_func["mlirOperationClone", MlirOperation](op)


fn mlirOperationDestroy(op: MlirOperation) -> None:
    """Takes an operation owned by the caller and destroys it."""
    return MLIR_func["mlirOperationDestroy", NoneType._mlir_type](op)


fn mlirOperationRemoveFromParent(op: MlirOperation) -> None:
    """Removes the given operation from its parent block. The operation is not
    destroyed. The ownership of the operation is transferred to the caller."""
    return MLIR_func["mlirOperationRemoveFromParent", NoneType._mlir_type](op)


# FIXME(codegen): static function mlirOperationIsNull


fn mlirOperationEqual(op: MlirOperation, other: MlirOperation) -> Bool:
    """Checks whether two operation handles point to the same operation. This does
    not perform deep comparison."""
    return MLIR_func["mlirOperationEqual", Bool](op, other)


fn mlirOperationGetContext(op: MlirOperation) -> MlirContext:
    """Gets the context this operation is associated with."""
    return MLIR_func["mlirOperationGetContext", MlirContext](op)


fn mlirOperationGetLocation(op: MlirOperation) -> MlirLocation:
    """Gets the location of the operation."""
    return MLIR_func["mlirOperationGetLocation", MlirLocation](op)


fn mlirOperationGetTypeID(op: MlirOperation) -> MlirTypeID:
    """Gets the type id of the operation.
    Returns null if the operation does not have a registered operation
    description."""
    return MLIR_func["mlirOperationGetTypeID", MlirTypeID](op)


fn mlirOperationGetName(op: MlirOperation) -> MlirIdentifier:
    """Gets the name of the operation as an identifier."""
    return MLIR_func["mlirOperationGetName", MlirIdentifier](op)


fn mlirOperationGetBlock(op: MlirOperation) -> MlirBlock:
    """Gets the block that owns this operation, returning null if the operation is
    not owned."""
    return MLIR_func["mlirOperationGetBlock", MlirBlock](op)


fn mlirOperationGetParentOperation(op: MlirOperation) -> MlirOperation:
    """Gets the operation that owns this operation, returning null if the operation
    is not owned."""
    return MLIR_func["mlirOperationGetParentOperation", MlirOperation](op)


fn mlirOperationGetNumRegions(op: MlirOperation) -> Int:
    """Returns the number of regions attached to the given operation."""
    return MLIR_func["mlirOperationGetNumRegions", Int](op)


fn mlirOperationGetRegion(op: MlirOperation, pos: Int) -> MlirRegion:
    """Returns `pos`-th region attached to the operation."""
    return MLIR_func["mlirOperationGetRegion", MlirRegion](op, pos)


fn mlirOperationGetNextInBlock(op: MlirOperation) -> MlirOperation:
    """Returns an operation immediately following the given operation in its
    enclosing block."""
    return MLIR_func["mlirOperationGetNextInBlock", MlirOperation](op)


fn mlirOperationGetNumOperands(op: MlirOperation) -> Int:
    """Returns the number of operands of the operation."""
    return MLIR_func["mlirOperationGetNumOperands", Int](op)


fn mlirOperationGetOperand(op: MlirOperation, pos: Int) -> MlirValue:
    """Returns `pos`-th operand of the operation."""
    return MLIR_func["mlirOperationGetOperand", MlirValue](op, pos)


fn mlirOperationSetOperand(
    op: MlirOperation, pos: Int, new_value: MlirValue
) -> None:
    """Sets the `pos`-th operand of the operation."""
    return MLIR_func["mlirOperationSetOperand", NoneType._mlir_type](
        op, pos, new_value
    )


fn mlirOperationSetOperands(
    op: MlirOperation, n_operands: Int, operands: UnsafePointer[MlirValue]
) -> None:
    """Replaces the operands of the operation."""
    return MLIR_func["mlirOperationSetOperands", NoneType._mlir_type](
        op, n_operands, operands
    )


fn mlirOperationGetNumResults(op: MlirOperation) -> Int:
    """Returns the number of results of the operation."""
    return MLIR_func["mlirOperationGetNumResults", Int](op)


fn mlirOperationGetResult(op: MlirOperation, pos: Int) -> MlirValue:
    """Returns `pos`-th result of the operation."""
    return MLIR_func["mlirOperationGetResult", MlirValue](op, pos)


fn mlirOperationGetNumSuccessors(op: MlirOperation) -> Int:
    """Returns the number of successor blocks of the operation."""
    return MLIR_func["mlirOperationGetNumSuccessors", Int](op)


fn mlirOperationGetSuccessor(op: MlirOperation, pos: Int) -> MlirBlock:
    """Returns `pos`-th successor of the operation."""
    return MLIR_func["mlirOperationGetSuccessor", MlirBlock](op, pos)


fn mlirOperationSetSuccessor(
    op: MlirOperation, pos: Int, block: MlirBlock
) -> None:
    """Set `pos`-th successor of the operation."""
    return MLIR_func["mlirOperationSetSuccessor", NoneType._mlir_type](
        op, pos, block
    )


fn mlirOperationHasInherentAttributeByName(
    op: MlirOperation, name: MlirStringRef
) -> Bool:
    """Returns true if this operation defines an inherent attribute with this name.
    Note: the attribute can be optional, so
    `mlirOperationGetInherentAttributeByName` can still return a null attribute.
    """
    return MLIR_func["mlirOperationHasInherentAttributeByName", Bool](op, name)


fn mlirOperationGetInherentAttributeByName(
    op: MlirOperation, name: MlirStringRef
) -> MlirAttribute:
    """Returns an inherent attribute attached to the operation given its name.
    """
    return MLIR_func["mlirOperationGetInherentAttributeByName", MlirAttribute](
        op, name
    )


fn mlirOperationSetInherentAttributeByName(
    op: MlirOperation, name: MlirStringRef, attr: MlirAttribute
) -> None:
    """Sets an inherent attribute by name, replacing the existing if it exists.
    This has no effect if "name" does not match an inherent attribute."""
    return MLIR_func[
        "mlirOperationSetInherentAttributeByName", NoneType._mlir_type
    ](op, name, attr)


fn mlirOperationGetNumDiscardableAttributes(op: MlirOperation) -> Int:
    """Returns the number of discardable attributes attached to the operation.
    """
    return MLIR_func["mlirOperationGetNumDiscardableAttributes", Int](op)


fn mlirOperationGetDiscardableAttribute(
    op: MlirOperation, pos: Int
) -> MlirNamedAttribute:
    """Return `pos`-th discardable attribute of the operation."""
    return MLIR_func[
        "mlirOperationGetDiscardableAttribute", MlirNamedAttribute
    ](op, pos)


fn mlirOperationGetDiscardableAttributeByName(
    op: MlirOperation, name: MlirStringRef
) -> MlirAttribute:
    """Returns a discardable attribute attached to the operation given its name.
    """
    return MLIR_func[
        "mlirOperationGetDiscardableAttributeByName", MlirAttribute
    ](op, name)


fn mlirOperationSetDiscardableAttributeByName(
    op: MlirOperation, name: MlirStringRef, attr: MlirAttribute
) -> None:
    """Sets a discardable attribute by name, replacing the existing if it exists or
    adding a new one otherwise. The new `attr` Attribute is not allowed to be
    null, use `mlirOperationRemoveDiscardableAttributeByName` to remove an
    Attribute instead."""
    return MLIR_func[
        "mlirOperationSetDiscardableAttributeByName", NoneType._mlir_type
    ](op, name, attr)


fn mlirOperationRemoveDiscardableAttributeByName(
    op: MlirOperation, name: MlirStringRef
) -> Bool:
    """Removes a discardable attribute by name. Returns false if the attribute was
    not found and true if removed."""
    return MLIR_func["mlirOperationRemoveDiscardableAttributeByName", Bool](
        op, name
    )


fn mlirOperationGetNumAttributes(op: MlirOperation) -> Int:
    """Returns the number of attributes attached to the operation.
    Deprecated, please use `mlirOperationGetNumInherentAttributes` or
    `mlirOperationGetNumDiscardableAttributes`."""
    return MLIR_func["mlirOperationGetNumAttributes", Int](op)


fn mlirOperationGetAttribute(op: MlirOperation, pos: Int) -> MlirNamedAttribute:
    """Return `pos`-th attribute of the operation.
    Deprecated, please use `mlirOperationGetInherentAttribute` or
    `mlirOperationGetDiscardableAttribute`."""
    return MLIR_func["mlirOperationGetAttribute", MlirNamedAttribute](op, pos)


fn mlirOperationGetAttributeByName(
    op: MlirOperation, name: MlirStringRef
) -> MlirAttribute:
    """Returns an attribute attached to the operation given its name.
    Deprecated, please use `mlirOperationGetInherentAttributeByName` or
    `mlirOperationGetDiscardableAttributeByName`."""
    return MLIR_func["mlirOperationGetAttributeByName", MlirAttribute](op, name)


fn mlirOperationSetAttributeByName(
    op: MlirOperation, name: MlirStringRef, attr: MlirAttribute
) -> None:
    """Sets an attribute by name, replacing the existing if it exists or
    adding a new one otherwise.
    Deprecated, please use `mlirOperationSetInherentAttributeByName` or
    `mlirOperationSetDiscardableAttributeByName`."""
    return MLIR_func["mlirOperationSetAttributeByName", NoneType._mlir_type](
        op, name, attr
    )


fn mlirOperationRemoveAttributeByName(
    op: MlirOperation, name: MlirStringRef
) -> Bool:
    """Removes an attribute by name. Returns false if the attribute was not found
    and true if removed.
    Deprecated, please use `mlirOperationRemoveInherentAttributeByName` or
    `mlirOperationRemoveDiscardableAttributeByName`."""
    return MLIR_func["mlirOperationRemoveAttributeByName", Bool](op, name)


fn mlirOperationPrint[W: Writer](mut writer: W, op: MlirOperation):
    """Prints an operation by sending chunks of the string representation and
    forwarding `userData to `callback`. Note that the callback may be called
    several times with consecutive chunks of the string."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirOperationPrint", NoneType._mlir_type](
        op, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


fn mlirOperationPrintWithFlags[
    W: Writer
](mut writer: W, op: MlirOperation, flags: MlirOpPrintingFlags):
    """Same as mlirOperationPrint but accepts flags controlling the printing
    behavior."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirOperationPrintWithFlags", NoneType._mlir_type](
        op, flags, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


fn mlirOperationPrintWithState[
    W: Writer
](mut writer: W, op: MlirOperation, state: MlirAsmState):
    """Same as mlirOperationPrint but accepts AsmState controlling the printing
    behavior as well as caching computed names."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirOperationPrintWithState", NoneType._mlir_type](
        op, state, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


fn mlirOperationWriteBytecode[W: Writer](mut writer: W, op: MlirOperation):
    """Same as mlirOperationPrint but writing the bytecode format."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirOperationWriteBytecode", NoneType._mlir_type](
        op, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


fn mlirOperationWriteBytecodeWithConfig[
    W: Writer
](
    mut writer: W, op: MlirOperation, config: MlirBytecodeWriterConfig
) -> MlirLogicalResult:
    """Same as mlirOperationWriteBytecode but with writer config and returns
    failure only if desired bytecode could not be honored."""
    var buffer = _WriteBufferStack(writer)
    var result = MLIR_func[
        "mlirOperationWriteBytecodeWithConfig", MlirLogicalResult
    ](
        op,
        config,
        write_buffered_callback[W],
        UnsafePointer(to=buffer),
    )
    buffer.flush()
    return result


fn mlirOperationDump(op: MlirOperation) -> None:
    """Prints an operation to stderr."""
    return MLIR_func["mlirOperationDump", NoneType._mlir_type](op)


fn mlirOperationVerify(op: MlirOperation) -> Bool:
    """Verify the operation and return true if it passes, false if it fails."""
    return MLIR_func["mlirOperationVerify", Bool](op)


fn mlirOperationMoveAfter(op: MlirOperation, other: MlirOperation) -> None:
    """Moves the given operation immediately after the other operation in its
    parent block. The given operation may be owned by the caller or by its
    current block. The other operation must belong to a block. In any case, the
    ownership is transferred to the block of the other operation."""
    return MLIR_func["mlirOperationMoveAfter", NoneType._mlir_type](op, other)


fn mlirOperationMoveBefore(op: MlirOperation, other: MlirOperation) -> None:
    """Moves the given operation immediately before the other operation in its
    parent block. The given operation may be owner by the caller or by its
    current block. The other operation must belong to a block. In any case, the
    ownership is transferred to the block of the other operation."""
    return MLIR_func["mlirOperationMoveBefore", NoneType._mlir_type](op, other)


@fieldwise_init
@register_passable("trivial")
struct MlirWalkResult(Copyable, Movable):
    """Operation walk result."""

    var value: Int8


alias MlirWalkResultAdvance = MlirWalkResult(0)
alias MlirWalkResultInterrupt = MlirWalkResult(1)
alias MlirWalkResultSkip = MlirWalkResult(2)


@fieldwise_init
@register_passable("trivial")
struct MlirWalkOrder(Copyable, Movable):
    """Traversal order for operation walk."""

    var value: Int8


alias MlirWalkPreOrder = MlirWalkOrder(0)
alias MlirWalkPostOrder = MlirWalkOrder(1)

# Operation walker type. The handler is passed an (opaque) reference to an
# operation and a pointer to a `userData`.
alias MlirOperationWalkCallback = fn (
    MlirOperation, OpaquePointer
) -> MlirWalkResult


fn mlirOperationWalk(
    op: MlirOperation,
    callback: MlirOperationWalkCallback,
    user_data: OpaquePointer,
    walk_order: MlirWalkOrder,
) -> None:
    """Walks operation `op` in `walkOrder` and calls `callback` on that operation.
    `*userData` is passed to the callback as well and can be used to tunnel some
    context or other data into the callback."""
    return MLIR_func["mlirOperationWalk", NoneType._mlir_type](
        op, callback, user_data, walk_order
    )


# ===----------------------------------------------------------------------===//
#  Region API.
# ===----------------------------------------------------------------------===//


fn mlirRegionCreate() -> MlirRegion:
    """Creates a new empty region and transfers ownership to the caller."""
    return MLIR_func["mlirRegionCreate", MlirRegion]()


fn mlirRegionDestroy(region: MlirRegion) -> None:
    """Takes a region owned by the caller and destroys it."""
    return MLIR_func["mlirRegionDestroy", NoneType._mlir_type](region)


# FIXME(codegen): static function mlirRegionIsNull


fn mlirRegionEqual(region: MlirRegion, other: MlirRegion) -> Bool:
    """Checks whether two region handles point to the same region. This does not
    perform deep comparison."""
    return MLIR_func["mlirRegionEqual", Bool](region, other)


fn mlirRegionGetFirstBlock(region: MlirRegion) -> MlirBlock:
    """Gets the first block in the region."""
    return MLIR_func["mlirRegionGetFirstBlock", MlirBlock](region)


fn mlirRegionAppendOwnedBlock(region: MlirRegion, block: MlirBlock) -> None:
    """Takes a block owned by the caller and appends it to the given region."""
    return MLIR_func["mlirRegionAppendOwnedBlock", NoneType._mlir_type](
        region, block
    )


fn mlirRegionInsertOwnedBlock(
    region: MlirRegion, pos: Int, block: MlirBlock
) -> None:
    """Takes a block owned by the caller and inserts it at `pos` to the given
    region. This is an expensive operation that linearly scans the region,
    prefer insertAfter/Before instead."""
    return MLIR_func["mlirRegionInsertOwnedBlock", NoneType._mlir_type](
        region, pos, block
    )


fn mlirRegionInsertOwnedBlockAfter(
    region: MlirRegion, reference: MlirBlock, block: MlirBlock
) -> None:
    """Takes a block owned by the caller and inserts it after the (non-owned)
    reference block in the given region. The reference block must belong to the
    region. If the reference block is null, prepends the block to the region."""
    return MLIR_func["mlirRegionInsertOwnedBlockAfter", NoneType._mlir_type](
        region, reference, block
    )


fn mlirRegionInsertOwnedBlockBefore(
    region: MlirRegion, reference: MlirBlock, block: MlirBlock
) -> None:
    """Takes a block owned by the caller and inserts it before the (non-owned)
    reference block in the given region. The reference block must belong to the
    region. If the reference block is null, appends the block to the region."""
    return MLIR_func["mlirRegionInsertOwnedBlockBefore", NoneType._mlir_type](
        region, reference, block
    )


fn mlirOperationGetFirstRegion(op: MlirOperation) -> MlirRegion:
    """Returns first region attached to the operation."""
    return MLIR_func["mlirOperationGetFirstRegion", MlirRegion](op)


fn mlirRegionGetNextInOperation(region: MlirRegion) -> MlirRegion:
    """Returns the region immediately following the given region in its parent
    operation."""
    return MLIR_func["mlirRegionGetNextInOperation", MlirRegion](region)


fn mlirRegionTakeBody(target: MlirRegion, source: MlirRegion) -> None:
    """Moves the entire content of the source region to the target region."""
    return MLIR_func["mlirRegionTakeBody", NoneType._mlir_type](target, source)


# ===----------------------------------------------------------------------===//
#  Block API.
# ===----------------------------------------------------------------------===//


fn mlirBlockCreate(
    n_args: Int,
    args: UnsafePointer[MlirType],
    locs: UnsafePointer[MlirLocation],
) -> MlirBlock:
    """Creates a new empty block with the given argument types and transfers
    ownership to the caller."""
    return MLIR_func["mlirBlockCreate", MlirBlock](n_args, args, locs)


fn mlirBlockDestroy(block: MlirBlock) -> None:
    """Takes a block owned by the caller and destroys it."""
    return MLIR_func["mlirBlockDestroy", NoneType._mlir_type](block)


fn mlirBlockDetach(block: MlirBlock) -> None:
    """Detach a block from the owning region and assume ownership."""
    return MLIR_func["mlirBlockDetach", NoneType._mlir_type](block)


# FIXME(codegen): static function mlirBlockIsNull


fn mlirBlockEqual(block: MlirBlock, other: MlirBlock) -> Bool:
    """Checks whether two blocks handles point to the same block. This does not
    perform deep comparison."""
    return MLIR_func["mlirBlockEqual", Bool](block, other)


fn mlirBlockGetParentOperation(a: MlirBlock) -> MlirOperation:
    """Returns the closest surrounding operation that contains this block."""
    return MLIR_func["mlirBlockGetParentOperation", MlirOperation](a)


fn mlirBlockGetParentRegion(block: MlirBlock) -> MlirRegion:
    """Returns the region that contains this block."""
    return MLIR_func["mlirBlockGetParentRegion", MlirRegion](block)


fn mlirBlockGetNextInRegion(block: MlirBlock) -> MlirBlock:
    """Returns the block immediately following the given block in its parent
    region."""
    return MLIR_func["mlirBlockGetNextInRegion", MlirBlock](block)


fn mlirBlockGetFirstOperation(block: MlirBlock) -> MlirOperation:
    """Returns the first operation in the block."""
    return MLIR_func["mlirBlockGetFirstOperation", MlirOperation](block)


fn mlirBlockGetTerminator(block: MlirBlock) -> MlirOperation:
    """Returns the terminator operation in the block or null if no terminator.
    """
    return MLIR_func["mlirBlockGetTerminator", MlirOperation](block)


fn mlirBlockAppendOwnedOperation(
    block: MlirBlock, operation: MlirOperation
) -> None:
    """Takes an operation owned by the caller and appends it to the block."""
    return MLIR_func["mlirBlockAppendOwnedOperation", NoneType._mlir_type](
        block, operation
    )


fn mlirBlockInsertOwnedOperation(
    block: MlirBlock, pos: Int, operation: MlirOperation
) -> None:
    """Takes an operation owned by the caller and inserts it as `pos` to the block.
    This is an expensive operation that scans the block linearly, prefer
    insertBefore/After instead."""
    return MLIR_func["mlirBlockInsertOwnedOperation", NoneType._mlir_type](
        block, pos, operation
    )


fn mlirBlockInsertOwnedOperationAfter(
    block: MlirBlock, reference: MlirOperation, operation: MlirOperation
) -> None:
    """Takes an operation owned by the caller and inserts it after the (non-owned)
    reference operation in the given block. If the reference is null, prepends
    the operation. Otherwise, the reference must belong to the block."""
    return MLIR_func["mlirBlockInsertOwnedOperationAfter", NoneType._mlir_type](
        block, reference, operation
    )


fn mlirBlockInsertOwnedOperationBefore(
    block: MlirBlock, reference: MlirOperation, operation: MlirOperation
) -> None:
    """Takes an operation owned by the caller and inserts it before the (non-owned)
    reference operation in the given block. If the reference is null, appends
    the operation. Otherwise, the reference must belong to the block."""
    return MLIR_func[
        "mlirBlockInsertOwnedOperationBefore", NoneType._mlir_type
    ](block, reference, operation)


fn mlirBlockGetNumArguments(block: MlirBlock) -> Int:
    """Returns the number of arguments of the block."""
    return MLIR_func["mlirBlockGetNumArguments", Int](block)


fn mlirBlockAddArgument(
    block: MlirBlock, type: MlirType, loc: MlirLocation
) -> MlirValue:
    """Appends an argument of the specified type to the block. Returns the newly
    added argument."""
    return MLIR_func["mlirBlockAddArgument", MlirValue](block, type, loc)


fn mlirBlockEraseArgument(block: MlirBlock, index: Int16) -> None:
    """Erase the argument at 'index' and remove it from the argument list."""
    return MLIR_func["mlirBlockEraseArgument", NoneType._mlir_type](
        block, index
    )


fn mlirBlockInsertArgument(
    block: MlirBlock, pos: Int, type: MlirType, loc: MlirLocation
) -> MlirValue:
    """Inserts an argument of the specified type at a specified index to the block.
    Returns the newly added argument."""
    return MLIR_func["mlirBlockInsertArgument", MlirValue](
        block, pos, type, loc
    )


fn mlirBlockGetArgument(block: MlirBlock, pos: Int) -> MlirValue:
    """Returns `pos`-th argument of the block."""
    return MLIR_func["mlirBlockGetArgument", MlirValue](block, pos)


fn mlirBlockPrint[W: Writer](mut writer: W, block: MlirBlock):
    """Prints a block by sending chunks of the string representation and
    forwarding `userData to `callback`. Note that the callback may be called
    several times with consecutive chunks of the string."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirBlockPrint", NoneType._mlir_type](
        block, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


# ===----------------------------------------------------------------------===//
#  Value API.
# ===----------------------------------------------------------------------===//

# FIXME(codegen): static function mlirValueIsNull


fn mlirValueEqual(value1: MlirValue, value2: MlirValue) -> Bool:
    """Returns 1 if two values are equal, 0 otherwise."""
    return MLIR_func["mlirValueEqual", Bool](value1, value2)


fn mlirValueIsABlockArgument(value: MlirValue) -> Bool:
    """Returns 1 if the value is a block argument, 0 otherwise."""
    return MLIR_func["mlirValueIsABlockArgument", Bool](value)


fn mlirValueIsAOpResult(value: MlirValue) -> Bool:
    """Returns 1 if the value is an operation result, 0 otherwise."""
    return MLIR_func["mlirValueIsAOpResult", Bool](value)


fn mlirBlockArgumentGetOwner(value: MlirValue) -> MlirBlock:
    """Returns the block in which this value is defined as an argument. Asserts if
    the value is not a block argument."""
    return MLIR_func["mlirBlockArgumentGetOwner", MlirBlock](value)


fn mlirBlockArgumentGetArgNumber(value: MlirValue) -> Int:
    """Returns the position of the value in the argument list of its block."""
    return MLIR_func["mlirBlockArgumentGetArgNumber", Int](value)


fn mlirBlockArgumentSetType(value: MlirValue, type: MlirType) -> None:
    """Sets the type of the block argument to the given type."""
    return MLIR_func["mlirBlockArgumentSetType", NoneType._mlir_type](
        value, type
    )


fn mlirOpResultGetOwner(value: MlirValue) -> MlirOperation:
    """Returns an operation that produced this value as its result. Asserts if the
    value is not an op result."""
    return MLIR_func["mlirOpResultGetOwner", MlirOperation](value)


fn mlirOpResultGetResultNumber(value: MlirValue) -> Int:
    """Returns the position of the value in the list of results of the operation
    that produced it."""
    return MLIR_func["mlirOpResultGetResultNumber", Int](value)


fn mlirValueGetType(value: MlirValue) -> MlirType:
    """Returns the type of the value."""
    return MLIR_func["mlirValueGetType", MlirType](value)


fn mlirValueSetType(value: MlirValue, type: MlirType) -> None:
    """Set the type of the value."""
    return MLIR_func["mlirValueSetType", NoneType._mlir_type](value, type)


fn mlirValueDump(value: MlirValue) -> None:
    """Prints the value to the standard error stream."""
    return MLIR_func["mlirValueDump", NoneType._mlir_type](value)


fn mlirValuePrint[W: Writer](mut writer: W, value: MlirValue):
    """Prints a value by sending chunks of the string representation and
    forwarding `userData to `callback`. Note that the callback may be called
    several times with consecutive chunks of the string."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirValuePrint", NoneType._mlir_type](
        value, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


fn mlirValuePrintAsOperand[
    W: Writer
](mut writer: W, value: MlirValue, state: MlirAsmState):
    """Prints a value as an operand (i.e., the ValueID)."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirValuePrintAsOperand", NoneType._mlir_type](
        value,
        state,
        write_buffered_callback[W],
        UnsafePointer(to=buffer),
    )
    buffer.flush()


fn mlirValueGetFirstUse(value: MlirValue) -> MlirOpOperand:
    """Returns an op operand representing the first use of the value, or a null op
    operand if there are no uses."""
    return MLIR_func["mlirValueGetFirstUse", MlirOpOperand](value)


fn mlirValueReplaceAllUsesOfWith(of: MlirValue, `with`: MlirValue) -> None:
    """Replace all uses of 'of' value with the 'with' value, updating anything in
    the IR that uses 'of' to use the other value instead.  When this returns
    there are zero uses of 'of'."""
    return MLIR_func["mlirValueReplaceAllUsesOfWith", NoneType._mlir_type](
        of, `with`
    )


# ===----------------------------------------------------------------------===//
#  OpOperand API.
# ===----------------------------------------------------------------------===//


fn mlirOpOperandIsNull(op_operand: MlirOpOperand) -> Bool:
    """Returns whether the op operand is null."""
    return MLIR_func["mlirOpOperandIsNull", Bool](op_operand)


fn mlirOpOperandGetValue(op_operand: MlirOpOperand) -> MlirValue:
    """Returns the value of an op operand."""
    return MLIR_func["mlirOpOperandGetValue", MlirValue](op_operand)


fn mlirOpOperandGetOwner(op_operand: MlirOpOperand) -> MlirOperation:
    """Returns the owner operation of an op operand."""
    return MLIR_func["mlirOpOperandGetOwner", MlirOperation](op_operand)


fn mlirOpOperandGetOperandNumber(op_operand: MlirOpOperand) -> Int16:
    """Returns the operand number of an op operand."""
    return MLIR_func["mlirOpOperandGetOperandNumber", Int16](op_operand)


fn mlirOpOperandGetNextUse(op_operand: MlirOpOperand) -> MlirOpOperand:
    """Returns an op operand representing the next use of the value, or a null op
    operand if there is no next use."""
    return MLIR_func["mlirOpOperandGetNextUse", MlirOpOperand](op_operand)


# ===----------------------------------------------------------------------===//
#  Type API.
# ===----------------------------------------------------------------------===//


fn mlirTypeParseGet(context: MlirContext, type: MlirStringRef) -> MlirType:
    """Parses a type. The type is owned by the context."""
    return MLIR_func["mlirTypeParseGet", MlirType](context, type)


fn mlirTypeGetContext(type: MlirType) -> MlirContext:
    """Gets the context that a type was created with."""
    return MLIR_func["mlirTypeGetContext", MlirContext](type)


fn mlirTypeGetTypeID(type: MlirType) -> MlirTypeID:
    """Gets the type ID of the type."""
    return MLIR_func["mlirTypeGetTypeID", MlirTypeID](type)


fn mlirTypeGetDialect(type: MlirType) -> MlirDialect:
    """Gets the dialect a type belongs to."""
    return MLIR_func["mlirTypeGetDialect", MlirDialect](type)


# FIXME(codegen): static function mlirTypeIsNull


fn mlirTypeEqual(t1: MlirType, t2: MlirType) -> Bool:
    """Checks if two types are equal."""
    return MLIR_func["mlirTypeEqual", Bool](t1, t2)


fn mlirTypePrint[W: Writer](mut writer: W, type: MlirType):
    """Prints a location by sending chunks of the string representation and
    forwarding `userData to `callback`. Note that the callback may be called
    several times with consecutive chunks of the string."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirTypePrint", NoneType._mlir_type](
        type, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


fn mlirTypeDump(type: MlirType) -> None:
    """Prints the type to the standard error stream."""
    return MLIR_func["mlirTypeDump", NoneType._mlir_type](type)


# ===----------------------------------------------------------------------===//
#  Attribute API.
# ===----------------------------------------------------------------------===//


fn mlirAttributeParseGet(
    context: MlirContext, attr: MlirStringRef
) -> MlirAttribute:
    """Parses an attribute. The attribute is owned by the context."""
    return MLIR_func["mlirAttributeParseGet", MlirAttribute](context, attr)


fn mlirAttributeGetContext(attribute: MlirAttribute) -> MlirContext:
    """Gets the context that an attribute was created with."""
    return MLIR_func["mlirAttributeGetContext", MlirContext](attribute)


fn mlirAttributeGetType(attribute: MlirAttribute) -> MlirType:
    """Gets the type of this attribute."""
    return MLIR_func["mlirAttributeGetType", MlirType](attribute)


fn mlirAttributeGetTypeID(attribute: MlirAttribute) -> MlirTypeID:
    """Gets the type id of the attribute."""
    return MLIR_func["mlirAttributeGetTypeID", MlirTypeID](attribute)


fn mlirAttributeGetDialect(attribute: MlirAttribute) -> MlirDialect:
    """Gets the dialect of the attribute."""
    return MLIR_func["mlirAttributeGetDialect", MlirDialect](attribute)


# FIXME(codegen): static function mlirAttributeIsNull


fn mlirAttributeEqual(a1: MlirAttribute, a2: MlirAttribute) -> Bool:
    """Checks if two attributes are equal."""
    return MLIR_func["mlirAttributeEqual", Bool](a1, a2)


fn mlirAttributePrint[W: Writer](mut writer: W, attr: MlirAttribute):
    """Prints an attribute by sending chunks of the string representation and
    forwarding `userData to `callback`. Note that the callback may be called
    several times with consecutive chunks of the string."""
    var buffer = _WriteBufferStack(writer)
    MLIR_func["mlirAttributePrint", NoneType._mlir_type](
        attr, write_buffered_callback[W], UnsafePointer(to=buffer)
    )
    buffer.flush()


fn mlirAttributeDump(attr: MlirAttribute) -> None:
    """Prints the attribute to the standard error stream."""
    return MLIR_func["mlirAttributeDump", NoneType._mlir_type](attr)


fn mlirNamedAttributeGet(
    name: MlirIdentifier, attr: MlirAttribute
) -> MlirNamedAttribute:
    """Associates an attribute with the name. Takes ownership of neither."""
    return MLIR_func["mlirNamedAttributeGet", MlirNamedAttribute](name, attr)


# ===----------------------------------------------------------------------===//
#  Identifier API.
# ===----------------------------------------------------------------------===//


fn mlirIdentifierGet(
    context: MlirContext, str: MlirStringRef
) -> MlirIdentifier:
    """Gets an identifier with the given string value."""
    return MLIR_func["mlirIdentifierGet", MlirIdentifier](context, str)


fn mlirIdentifierGetContext(a: MlirIdentifier) -> MlirContext:
    """Returns the context associated with this identifier."""
    return MLIR_func["mlirIdentifierGetContext", MlirContext](a)


fn mlirIdentifierEqual(ident: MlirIdentifier, other: MlirIdentifier) -> Bool:
    """Checks whether two identifiers are the same."""
    return MLIR_func["mlirIdentifierEqual", Bool](ident, other)


fn mlirIdentifierStr(ident: MlirIdentifier) -> MlirStringRef:
    """Gets the string value of the identifier."""
    return MLIR_func["mlirIdentifierStr", MlirStringRef](ident)


# ===----------------------------------------------------------------------===//
#  Symbol and SymbolTable API.
# ===----------------------------------------------------------------------===//


fn mlirSymbolTableGetSymbolAttributeName() -> MlirStringRef:
    """Returns the name of the attribute used to store symbol names compatible with
    symbol tables."""
    return MLIR_func["mlirSymbolTableGetSymbolAttributeName", MlirStringRef]()


fn mlirSymbolTableGetVisibilityAttributeName() -> MlirStringRef:
    """Returns the name of the attribute used to store symbol visibility."""
    return MLIR_func[
        "mlirSymbolTableGetVisibilityAttributeName", MlirStringRef
    ]()


fn mlirSymbolTableCreate(operation: MlirOperation) -> MlirSymbolTable:
    """Creates a symbol table for the given operation. If the operation does not
    have the SymbolTable trait, returns a null symbol table."""
    return MLIR_func["mlirSymbolTableCreate", MlirSymbolTable](operation)


# FIXME(codegen): static function mlirSymbolTableIsNull


fn mlirSymbolTableDestroy(symbol_table: MlirSymbolTable) -> None:
    """Destroys the symbol table created with mlirSymbolTableCreate. This does not
    affect the operations in the table."""
    return MLIR_func["mlirSymbolTableDestroy", NoneType._mlir_type](
        symbol_table
    )


fn mlirSymbolTableLookup(
    symbol_table: MlirSymbolTable, name: MlirStringRef
) -> MlirOperation:
    """Looks up a symbol with the given name in the given symbol table and returns
    the operation that corresponds to the symbol. If the symbol cannot be found,
    returns a null operation."""
    return MLIR_func["mlirSymbolTableLookup", MlirOperation](symbol_table, name)


fn mlirSymbolTableInsert(
    symbol_table: MlirSymbolTable, operation: MlirOperation
) -> MlirAttribute:
    """Inserts the given operation into the given symbol table. The operation must
    have the symbol trait. If the symbol table already has a symbol with the
    same name, renames the symbol being inserted to ensure name uniqueness. Note
    that this does not move the operation itself into the block of the symbol
    table operation, this should be done separately. Returns the name of the
    symbol after insertion."""
    return MLIR_func["mlirSymbolTableInsert", MlirAttribute](
        symbol_table, operation
    )


fn mlirSymbolTableErase(
    symbol_table: MlirSymbolTable, operation: MlirOperation
) -> None:
    """Removes the given operation from the symbol table and erases it."""
    return MLIR_func["mlirSymbolTableErase", NoneType._mlir_type](
        symbol_table, operation
    )


fn mlirSymbolTableReplaceAllSymbolUses(
    old_symbol: MlirStringRef, new_symbol: MlirStringRef, `from`: MlirOperation
) -> MlirLogicalResult:
    """Attempt to replace all uses that are nested within the given operation
    of the given symbol 'oldSymbol' with the provided 'newSymbol'. This does
    not traverse into nested symbol tables. Will fail atomically if there are
    any unknown operations that may be potential symbol tables."""
    return MLIR_func["mlirSymbolTableReplaceAllSymbolUses", MlirLogicalResult](
        old_symbol, new_symbol, `from`
    )


fn mlirSymbolTableWalkSymbolTables(
    `from`: MlirOperation,
    all_sym_uses_visible: Bool,
    callback: fn (MlirOperation, Bool, OpaquePointer) -> None,
    user_data: OpaquePointer,
) -> None:
    """Walks all symbol table operations nested within, and including, `op`. For
    each symbol table operation, the provided callback is invoked with the op
    and a boolean signifying if the symbols within that symbol table can be
    treated as if all uses within the IR are visible to the caller.
    `allSymUsesVisible` identifies whether all of the symbol uses of symbols
    within `op` are visible."""
    return MLIR_func["mlirSymbolTableWalkSymbolTables", NoneType._mlir_type](
        `from`, all_sym_uses_visible, callback, user_data
    )
