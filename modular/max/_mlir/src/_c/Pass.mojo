# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #
#
# GENERATED FILE, DO NOT EDIT!
#
# Last generated by joe at 2024-09-19 16:25:27.900279 with command
# ```
#   ./utils/mojo/bindings-scripts/mlir/generate_mlir_mojo_bindings.sh
# ```
#
# ===----------------------------------------------------------------------=== #


from utils.write import _WriteBufferStack

from .ffi import MLIR_func
from .IR import *
from .Support import *

# ===-- mlir-c/Pass.h - C API to Pass Management ------------------*- C -*-===//
#
#  Part of the LLVM Project, under the Apache License v2.0 with LLVM
#  Exceptions.
#  See https://llvm.org/LICENSE.txt for license information.
#  SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
#
# ===----------------------------------------------------------------------===//
#
#  This header declares the C interface to MLIR pass manager.
#
# ===----------------------------------------------------------------------===//


# ===----------------------------------------------------------------------===//
#  Opaque type declarations.
#
#  Types are exposed to C bindings as structs containing opaque pointers. They
#  are not supposed to be inspected from C. This allows the underlying
#  representation to change without affecting the API users. The use of structs
#  instead of typedefs enables some type safety as structs are not implicitly
#  convertible to each other.
#
#  Instances of these types may or may not own the underlying object. The
#  ownership semantics is defined by how an instance of the type was obtained.
# ===----------------------------------------------------------------------===//


@register_passable("trivial")
struct MlirPass:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirExternalPass:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirPassManager:
    var ptr: OpaquePointer


@register_passable("trivial")
struct MlirOpPassManager:
    var ptr: OpaquePointer


# ===----------------------------------------------------------------------===//
#  PassManager/OpPassManager APIs.
# ===----------------------------------------------------------------------===//


fn mlirPassManagerCreate(ctx: MlirContext) -> MlirPassManager:
    """Create a new top-level PassManager with the default anchor."""
    return MLIR_func["mlirPassManagerCreate", MlirPassManager](ctx)


fn mlirPassManagerCreateOnOperation(
    ctx: MlirContext, anchor_op: MlirStringRef
) -> MlirPassManager:
    """Create a new top-level PassManager anchored on `anchorOp`."""
    return MLIR_func["mlirPassManagerCreateOnOperation", MlirPassManager](
        ctx, anchor_op
    )


fn mlirPassManagerDestroy(pass_manager: MlirPassManager) -> None:
    """Destroy the provided PassManager."""
    return MLIR_func["mlirPassManagerDestroy", NoneType._mlir_type](
        pass_manager
    )


# FIXME(codegen): static function mlirPassManagerIsNull


fn mlirPassManagerGetAsOpPassManager(
    pass_manager: MlirPassManager,
) -> MlirOpPassManager:
    """Cast a top-level PassManager to a generic OpPassManager."""
    return MLIR_func["mlirPassManagerGetAsOpPassManager", MlirOpPassManager](
        pass_manager
    )


fn mlirPassManagerRunOnOp(
    pass_manager: MlirPassManager, op: MlirOperation
) -> MlirLogicalResult:
    """Run the provided `passManager` on the given `op`."""
    return MLIR_func["mlirPassManagerRunOnOp", MlirLogicalResult](
        pass_manager, op
    )


fn mlirPassManagerEnableIRPrinting(pass_manager: MlirPassManager) -> None:
    """Enable mlir-print-ir-after-all."""
    return MLIR_func["mlirPassManagerEnableIRPrinting", NoneType._mlir_type](
        pass_manager
    )


fn mlirPassManagerEnableVerifier(
    pass_manager: MlirPassManager, enable: Bool
) -> None:
    """Enable / disable verify-each."""
    return MLIR_func["mlirPassManagerEnableVerifier", NoneType._mlir_type](
        pass_manager, enable
    )


fn mlirPassManagerGetNestedUnder(
    pass_manager: MlirPassManager, operation_name: MlirStringRef
) -> MlirOpPassManager:
    """Nest an OpPassManager under the top-level PassManager, the nested
    passmanager will only run on operations matching the provided name.
    The returned OpPassManager will be destroyed when the parent is destroyed.
    To further nest more OpPassManager under the newly returned one, see
    `mlirOpPassManagerNest` below."""
    return MLIR_func["mlirPassManagerGetNestedUnder", MlirOpPassManager](
        pass_manager, operation_name
    )


fn mlirOpPassManagerGetNestedUnder(
    pass_manager: MlirOpPassManager, operation_name: MlirStringRef
) -> MlirOpPassManager:
    """Nest an OpPassManager under the provided OpPassManager, the nested
    passmanager will only run on operations matching the provided name.
    The returned OpPassManager will be destroyed when the parent is destroyed.
    """
    return MLIR_func["mlirOpPassManagerGetNestedUnder", MlirOpPassManager](
        pass_manager, operation_name
    )


fn mlirPassManagerAddOwnedPass(
    pass_manager: MlirPassManager, `pass`: MlirPass
) -> None:
    """Add a pass and transfer ownership to the provided top-level mlirPassManager.
    If the pass is not a generic operation pass or a ModulePass, a new
    OpPassManager is implicitly nested under the provided PassManager."""
    return MLIR_func["mlirPassManagerAddOwnedPass", NoneType._mlir_type](
        pass_manager, `pass`
    )


fn mlirOpPassManagerAddOwnedPass(
    pass_manager: MlirOpPassManager, `pass`: MlirPass
) -> None:
    """Add a pass and transfer ownership to the provided mlirOpPassManager. If the
    pass is not a generic operation pass or matching the type of the provided
    PassManager, a new OpPassManager is implicitly nested under the provided
    PassManager."""
    return MLIR_func["mlirOpPassManagerAddOwnedPass", NoneType._mlir_type](
        pass_manager, `pass`
    )


fn mlirOpPassManagerAddPipeline[
    W: Writer
](
    mut writer: W,
    pass_manager: MlirOpPassManager,
    pipeline_elements: MlirStringRef,
) -> MlirLogicalResult:
    """Parse a sequence of textual MLIR pass pipeline elements and add them to the
    provided OpPassManager. If parsing fails an error message is reported using
    the provided callback."""
    var buffer = _WriteBufferStack(writer)
    var result = MLIR_func["mlirOpPassManagerAddPipeline", MlirLogicalResult](
        pass_manager,
        pipeline_elements,
        write_buffered_callback[W],
        UnsafePointer(to=buffer),
    )
    buffer.flush()
    return result


fn mlirPrintPassPipeline[
    W: Writer
](mut writer: W, pass_manager: MlirOpPassManager):
    """Print a textual MLIR pass pipeline by sending chunks of the string
    representation and forwarding `userData to `callback`. Note that the
    callback may be called several times with consecutive chunks of the string.
    """
    var buffer = _WriteBufferStack(writer)
    var result = MLIR_func["mlirPrintPassPipeline", NoneType._mlir_type](
        pass_manager,
        write_buffered_callback[W],
        UnsafePointer(to=buffer),
    )
    buffer.flush()
    return result


fn mlirParsePassPipeline[
    W: Writer
](
    mut writer: W,
    pass_manager: MlirOpPassManager,
    pipeline: MlirStringRef,
) -> MlirLogicalResult:
    """Parse a textual MLIR pass pipeline and assign it to the provided
    OpPassManager. If parsing fails an error message is reported using the
    provided callback."""
    var buffer = _WriteBufferStack(writer)
    var result = MLIR_func["mlirParsePassPipeline", MlirLogicalResult](
        pass_manager,
        pipeline,
        write_buffered_callback[W],
        UnsafePointer(to=buffer),
    )
    buffer.flush()
    return result


# ===----------------------------------------------------------------------===//
#  External Pass API.
#
#  This API allows to define passes outside of MLIR, not necessarily in
#  C++, and register them with the MLIR pass management infrastructure.
#
# ===----------------------------------------------------------------------===//


@register_passable("trivial")
struct MlirExternalPassCallbacks:
    """Structure of external `MlirPass` callbacks.
    All callbacks are required to be set unless otherwise specified."""

    # This callback is called from the pass is created.
    # This is analogous to a C++ pass constructor.
    var construct: fn (OpaquePointer) -> None
    # This callback is called when the pass is destroyed
    # This is analogous to a C++ pass destructor.
    var destruct: fn (OpaquePointer) -> None
    # This callback is optional.
    # The callback is called before the pass is run, allowing a chance to
    # initialize any complex state necessary for running the pass.
    # See Pass::initialize(MLIRContext *).
    var initialize: fn (MlirContext, OpaquePointer) -> MlirLogicalResult
    # This callback is called when the pass is cloned.
    # See Pass::clonePass().
    var clone: fn (OpaquePointer) -> OpaquePointer
    # This callback is called when the pass is run.
    # See Pass::runOnOperation().
    var run: fn (MlirOperation, MlirExternalPass, OpaquePointer) -> None


fn mlirCreateExternalPass(
    pass_id: MlirTypeID,
    name: MlirStringRef,
    argument: MlirStringRef,
    description: MlirStringRef,
    op_name: MlirStringRef,
    n_dependent_dialects: Int,
    dependent_dialects: UnsafePointer[MlirDialectHandle],
    callbacks: MlirExternalPassCallbacks,
    user_data: OpaquePointer,
) -> MlirPass:
    """Creates an external `MlirPass` that calls the supplied `callbacks` using the
    supplied `userData`. If `opName` is empty, the pass is a generic operation
    pass. Otherwise it is an operation pass specific to the specified pass name.
    """
    return MLIR_func["mlirCreateExternalPass", MlirPass](
        pass_id,
        name,
        argument,
        description,
        op_name,
        n_dependent_dialects,
        dependent_dialects,
        callbacks,
        user_data,
    )


fn mlirExternalPassSignalFailure(`pass`: MlirExternalPass) -> None:
    """This signals that the pass has failed. This is only valid to call during
    the `run` callback of `MlirExternalPassCallbacks`.
    See Pass::signalPassFailure()."""
    return MLIR_func["mlirExternalPassSignalFailure", NoneType._mlir_type](
        `pass`
    )
