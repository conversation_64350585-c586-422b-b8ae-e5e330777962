##===----------------------------------------------------------------------===##
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
##===----------------------------------------------------------------------===##

name: Check PR target branch

on:
  pull_request:
    types: [opened, edited, synchronize, reopened]

permissions:
  contents: read
  pull-requests: read

jobs:
  check-pr-target:
    name: Check PR target branch

    runs-on: ubuntu-latest
    timeout-minutes: 10

    defaults:
      run:
        shell: bash

    steps:
    - name: Fail if not targeting main branch
      if: ${{ github.base_ref != 'main' }}
      run: |
        echo "PRs must be targeted to merge to the main branch!"
        echo "PR is currently targeting: \"${{ github.base_ref }}\""
        exit 1
