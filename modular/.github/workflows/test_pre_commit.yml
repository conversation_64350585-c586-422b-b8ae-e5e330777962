##===----------------------------------------------------------------------===##
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
##===----------------------------------------------------------------------===##

name: Run pre-commit
on:
  pull_request:
    types: [opened, synchronize, reopened]
  workflow_dispatch:

permissions:
  contents: read
  pull-requests: read

jobs:
  lint:
    runs-on: "ubuntu-latest"
    timeout-minutes: 30

    defaults:
      run:
        shell: bash
    env:
      DEBIAN_FRONTEND: noninteractive

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Install Pixi
        run: |
          curl -fsSL https://pixi.sh/install.sh | PIXI_VERSION=0.47.0 sh
          # Add pixi to PATH
          echo "$HOME/.pixi/bin" >> $GITHUB_PATH

      - name: Install pre-commit
        run: |
          pip install pre-commit
          pre-commit install

      - name: Run pre-commit
        run: pixi run --manifest-path mojo pre-commit run --all-files
