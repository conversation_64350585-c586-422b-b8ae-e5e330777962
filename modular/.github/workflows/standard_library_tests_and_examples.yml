##===----------------------------------------------------------------------===##
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
##===----------------------------------------------------------------------===##

name: Test stdlib and examples
on:
  pull_request:
    types: [opened, synchronize, reopened]
  workflow_dispatch:

permissions:
  contents: read
  pull-requests: read

concurrency: # Only allow a single build/test run at a time for a PR.
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  test-examples:
    name: with ${{ matrix.os }} and assertions=${{ matrix.mojo-enable-assertions }}
    strategy:
      fail-fast: false
      matrix:
        os: ["ubuntu-latest", "macos-14"]
        mojo-enable-assertions: [0, 1]

    runs-on: ${{ matrix.os }}
    timeout-minutes: 30

    defaults:
      run:
        shell: bash
    env:
      DEBIAN_FRONTEND: noninteractive

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Install Pixi
        run: |
          curl -fsSL https://pixi.sh/install.sh | PIXI_VERSION=0.47.0 sh
          # Add pixi to PATH
          echo "$HOME/.pixi/bin" >> $GITHUB_PATH

      - name: Install build tools (Linux)
        if: ${{ matrix.os == 'ubuntu-latest' }}
        run: |
          ./mojo/stdlib/scripts/install-build-tools-linux.sh

      - name: Install build tools (macOS)
        if: ${{ matrix.os == 'macos-14' }}
        run: |
          ./mojo/stdlib/scripts/install-build-tools-macos.sh

      - name: Run standard library tests and examples
        env:
          MOJO_ENABLE_ASSERTIONS_IN_TESTS: ${{ matrix.mojo-enable-assertions }}
        working-directory: mojo
        run: |
          pixi run --frozen tests
          pixi run --frozen examples
          pixi run --frozen benchmarks
