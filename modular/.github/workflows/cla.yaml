##===----------------------------------------------------------------------===##
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
##===----------------------------------------------------------------------===##

name: "CLA Assistant"
on:
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened,closed,synchronize]

# explicitly configure permissions, in case your GITHUB_TOKEN workflow permissions are set to read-only in repository settings
permissions:
  actions: write
  contents: read
  pull-requests: write
  statuses: write

jobs:
  CLAAssistant:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@df432ceedc7162793a195dd1713ff69aefc7379e
        id: app-token
        with:
          app-id: ${{ vars.CLA_BOT_CLIENT_ID }}
          private-key: ${{ secrets.CLA_BOT_PRIVATE_KEY }}
          owner: modular
          repositories: cla
          permission-contents: write

      - name: "CLA Assistant"
        if: (github.event.comment.body == 'recheck' || github.event.comment.body == 'I have read the CLA Document and I hereby sign the CLA') || github.event_name == 'pull_request_target'
        uses: contributor-assistant/github-action@ca4a40a7d1004f18d9960b404b97e5f30a505a08
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # the below token should have repo scope and must be manually added by you in the repository's secret
          # This token is required only if you have configured to store the signatures in a remote repository/organization
          PERSONAL_ACCESS_TOKEN: ${{ steps.app-token.outputs.token }}
        with:
          path-to-document: 'https://github.com/modular/cla/blob/main/CLA.md' # e.g. a CLA or a DCO document
          branch: 'main'
          remote-organization-name: 'modular'
          remote-repository-name: 'cla'
          path-to-signatures: 'signatures/version1/cla.json'
