##===----------------------------------------------------------------------===##
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
##===----------------------------------------------------------------------===##

name: MAX Builds model bug report
description: Report a bug with a model
title: "[BUG]: "
labels:
  - "max-builds,modular-repo"
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out a bug report!

        Please provide a descriptive title above and fill in the following fields.

  - type: input
    id: model-variant
    attributes:
      label: On which model did the bug occur?
      description: Provide the model (and variant).
    validations:
      required: true

  - type: textarea
    id: Description
    attributes:
      label: Bug description
      description: Describe the bug you encountered and what you expected to happen.
    validations:
      required: true

  - type: textarea
    id: Steps
    attributes:
      label: Steps to reproduce
      description: Provide the specific steps to reproduce the issue.
      value: |
        - Include relevant code snippet or link to code that did not work as expected.
        - If applicable, add screenshots to help explain the problem.
        - Include anything else that might help us debug the issue.
    validations:
      required: true
