##===----------------------------------------------------------------------===##
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
##===----------------------------------------------------------------------===##

name: Mojo feature request
description: Suggest an enhancement for Mojo
title: "[Feature Request]"
labels:
  - "enhancement,mojo,modular-repo"
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to suggest a Mojo enhancement!

        Please enter a concise title above and fill out the following fields.

  - type: checkboxes
    id: Roadmap
    attributes:
      label: Review Mojo's priorities
      description: Please take a look at our roadmap before you file a new feature request.
      options:
        - label: I have read the [roadmap and priorities](https://docs.modular.com/mojo/roadmap.html#overall-priorities) and I believe this request falls within the priorities.
          required: true

  - type: markdown
    attributes:
      value: |
        If the request is out of the published roadmap and priorities, please start a [discussion](https://forum.modular.com/) in the Modular Forum to get feedback from the team.

  - type: textarea
    id: Request
    attributes:
      label: What is your request?
      description: Describe how you'd like us to improve Mojo.
    validations:
      required: true

  - type: textarea
    id: Motivation
    attributes:
      label: What is your motivation for this change?
      description: Describe the problem that your feature seeks to address (what is the value to the product/user?).
    validations:
      required: true

  - type: textarea
    id: Description
    attributes:
      label: Any other details?
      description: Perhaps some minimum functional attributes the implementation should include, or other context about your feature.
    validations:
      required: false
