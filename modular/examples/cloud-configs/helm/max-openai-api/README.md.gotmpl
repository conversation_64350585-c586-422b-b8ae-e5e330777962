<!-- markdownlint-disable -->
<!--
NOTE: This file is generated by helm-docs: https://github.com/norwoodj/helm-docs#installation
-->

# MAX OpenAI API Helm chart

{{ template "chart.deprecationWarning" . }}

{{ template "chart.description" . }}

{{ template "chart.homepageLine" . }}

{{ template "chart.sourcesSection" . }}

## Usage

### Installing the chart

To install this chart using Helm 3, run the following command:

```console
helm install max-openai-api oci://registry-1.docker.io/modular/max-openai-api-chart \
  --version <insert-version> \
  --set huggingfaceRepoId=<insert-huggingface-model-id>
  --set maxServe.maxLength=512 \
  --set maxServe.maxBatchSize=16 \
  --set envSecret.HF_TOKEN=<insert-huggingface-token> \
  --set env.HF_HUB_ENABLE_HF_TRANSFER=1 \
  --wait
```

The command deploys MAX OpenAI API on the Kubernetes cluster in the default configuration. The Values reference section below lists the parameters that can be configured during installation.

### Upgrading the chart

To upgrade the chart with the release name `max-openai-api`:

```console
helm upgrade max-openai-api oci://registry-1.docker.io/modular/max-openai-api-chart
```

### Uninstalling the chart

To uninstall/delete the `max-openai-api` deployment:

```console
helm delete max-openai-api
```

### End-to-end example that provisions an K8s cluster and installs MAX OpenAI API

To provision a k8s cluster via `eksctl` and then install MAX OpenAI API, run the following commands:

```console
# provision a k8s cluster (takes 10-15 minutes)
eksctl create cluster \
  --name max-openai-api-demo \
  --region us-east-1 \
  --node-type g5.4xlarge \
  --nodes 1

# create a k8s namespace
kubectl create namespace max-openai-api-demo

# deploy MAX OpenAI API via helm chart (takes 10 minutes)
helm install max-openai-api oci://registry-1.docker.io/modular/max-openai-api-chart \
  --version <insert-version> \
  --namespace max-openai-api-demo \
  --set huggingfaceRepoId=modularai/Llama-3.1-8B-Instruct-GGUF
  --set maxServe.maxLength=512 \
  --set maxServe.maxBatchSize=16 \
  --set envSecret.HF_TOKEN=<insert-huggingface-token> \
  --set env.HF_HUB_ENABLE_HF_TRANSFER=1 \
  --timeout 10m0s \
  --wait

# forward the remote k8s port to the local network to access the service locally
# the command is blocking and takes the terminal
# user another terminal for subsequent curl and ctrl-c to stop the port forwarding
POD_NAME=$(kubectl get pods --namespace max-openai-api-demo -l "app.kubernetes.io/name=max-openai-api-chart,app.kubernetes.io/instance=max-openai-api" -o jsonpath="{.items[0].metadata.name}")
CONTAINER_PORT=$(kubectl get pod --namespace max-openai-api-demo $POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
kubectl port-forward $POD_NAME 8000:$CONTAINER_PORT --namespace max-openai-api-demo &

# test the service
curl -N http://localhost:8000/v1/chat/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "modularai/Llama-3.1-8B-Instruct-GGUF",
        "stream": true,
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Who won the world series in 2020?"}
        ]
    }'

# uninstall MAX OpenAI API
helm uninstall max-openai-api --namespace max-openai-api-demo

# Delete the namespace
kubectl delete namespace max-openai-api-demo

# delete the k8s cluster
eksctl delete cluster \
  --name max-openai-api-demo \
  --region us-east-1
```


{{ template "chart.requirementsSection" . }}

{{ template "chart.valuesSection" . }}
