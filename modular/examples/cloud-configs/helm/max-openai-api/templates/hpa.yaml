##===----------------------------------------------------------------------===##
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
##===----------------------------------------------------------------------===##
{{- if .Values.inferenceServer.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "max.fullname" . }}-hpa
  labels:
    app.kubernetes.io/name: {{ template "max.name" . }}
    helm.sh/chart: {{ template "max.chart" . }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "max.fullname" . }}
  minReplicas: {{ .Values.inferenceServer.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.inferenceServer.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.inferenceServer.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.inferenceServer.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.inferenceServer.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.inferenceServer.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
