##===----------------------------------------------------------------------===##
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
##===----------------------------------------------------------------------===##
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "max.fullname" . }}-env
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ template "max.name" . }}
    helm.sh/chart: {{ template "max.chart" . }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    app.kubernetes.io/instance: {{ .Release.Name }}
type: Opaque
stringData:
    {{- if .Values.envSecret }}
    {{- range $key, $value := .Values.envSecret }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
    {{- end }}
