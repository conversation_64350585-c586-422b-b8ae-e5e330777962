{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "metadata": {"_generator": {"name": "Modular Inc.", "copyright": "Copyright (c) 2025, Modular Inc. All rights reserved.", "license": "Licensed under the Apache License v2.0 with LLVM Exceptions: https://llvm.org/LICENSE.txt", "disclaimer": "Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License."}}, "parameters": {"adminUsername": {"type": "string", "metadata": {"description": "Admin username for the virtual machine."}}, "adminPassword": {"type": "securestring", "metadata": {"description": "Admin password for the virtual machine."}}, "vmSize": {"type": "string", "defaultValue": "Standard_NV36ads_A10_v5", "metadata": {"description": "Size of the virtual machine."}}, "osDiskSizeGB": {"type": "int", "defaultValue": 128, "metadata": {"description": "OS disk size in GB."}}, "vnetAddressPrefix": {"type": "string", "defaultValue": "10.0.0.0/16", "metadata": {"description": "Address space for the virtual network."}}, "subnetAddressPrefix": {"type": "string", "defaultValue": "10.0.0.0/24", "metadata": {"description": "Subnet address space."}}, "startupScript": {"type": "string", "metadata": {"description": "Base64-encoded startup script."}}, "location": {"type": "string", "defaultValue": "westus3", "metadata": {"description": "Location for all resources."}}}, "resources": [{"type": "Microsoft.Network/virtualNetworks", "apiVersion": "2021-03-01", "name": "maxServeVNet", "location": "[parameters('location')]", "properties": {"addressSpace": {"addressPrefixes": ["[parameters('vnetAddressPrefix')]"]}, "subnets": [{"name": "maxServeSubnet", "properties": {"addressPrefix": "[parameters('subnetAddressPrefix')]"}}]}}, {"type": "Microsoft.Network/publicIPAddresses", "apiVersion": "2021-03-01", "name": "maxServePublicIP", "location": "[parameters('location')]", "properties": {"publicIPAllocationMethod": "Dynamic"}}, {"type": "Microsoft.Network/networkSecurityGroups", "apiVersion": "2021-02-01", "name": "maxServeNSG", "location": "[parameters('location')]", "properties": {"securityRules": [{"name": "allowHTTP", "properties": {"priority": 100, "protocol": "Tcp", "access": "Allow", "direction": "Inbound", "sourceAddressPrefix": "*", "sourcePortRange": "*", "destinationAddressPrefix": "*", "destinationPortRange": "80", "description": "Allow HTTP traffic on port 80"}}, {"name": "allowSSH", "properties": {"priority": 200, "protocol": "Tcp", "access": "Allow", "direction": "Inbound", "sourceAddressPrefix": "*", "sourcePortRange": "*", "destinationAddressPrefix": "*", "destinationPortRange": "22", "description": "Allow SSH traffic on port 22"}}, {"name": "allowOutbound", "properties": {"priority": 300, "protocol": "Tcp", "access": "Allow", "direction": "Outbound", "sourceAddressPrefix": "*", "sourcePortRange": "*", "destinationAddressPrefix": "*", "destinationPortRange": "*", "description": "Allow all outbound traffic"}}]}}, {"type": "Microsoft.Network/networkInterfaces", "apiVersion": "2021-03-01", "name": "maxServeNIC", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Network/publicIPAddresses', 'maxServePublicIP')]", "[resourceId('Microsoft.Network/virtualNetworks', 'maxServeVNet')]", "[resourceId('Microsoft.Network/networkSecurityGroups', 'maxServeNSG')]"], "properties": {"ipConfigurations": [{"name": "ipconfig1", "properties": {"subnet": {"id": "[concat(resourceId('Microsoft.Network/virtualNetworks', 'maxServeVNet'), '/subnets/maxServeSubnet')]"}, "privateIPAllocationMethod": "Dynamic", "publicIPAddress": {"id": "[resourceId('Microsoft.Network/publicIPAddresses', 'maxServePublicIP')]"}}}], "networkSecurityGroup": {"id": "[resourceId('Microsoft.Network/networkSecurityGroups', 'maxServeNSG')]"}}}, {"type": "Microsoft.Compute/virtualMachines", "apiVersion": "2021-03-01", "name": "maxServeVM", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Network/networkInterfaces', 'maxServeNIC')]"], "plan": {"name": "nvaie_gpu_1_gen2", "publisher": "nvidia", "product": "nvidia-ai-enterprise"}, "properties": {"hardwareProfile": {"vmSize": "[parameters('vmSize')]"}, "osProfile": {"computerName": "maxServeVM", "adminUsername": "[parameters('adminUsername')]", "adminPassword": "[parameters('adminPassword')]"}, "storageProfile": {"imageReference": {"publisher": "nvidia", "offer": "nvidia-ai-enterprise", "sku": "nvaie_gpu_1_gen2", "version": "24.07.03"}, "osDisk": {"createOption": "FromImage", "managedDisk": {"storageAccountType": "Standard_LRS"}, "diskSizeGB": "[parameters('osDiskSizeGB')]"}}, "networkProfile": {"networkInterfaces": [{"id": "[resourceId('Microsoft.Network/networkInterfaces', 'maxServeNIC')]"}]}}}, {"type": "Microsoft.Compute/virtualMachines/extensions", "apiVersion": "2021-03-01", "name": "maxServeVM/customScriptExtension", "location": "[resourceGroup().location]", "dependsOn": ["[resourceId('Microsoft.Compute/virtualMachines', 'maxServeVM')]"], "properties": {"publisher": "Microsoft.Azure.Extensions", "type": "CustomScript", "typeHandlerVersion": "2.1", "autoUpgradeMinorVersion": true, "settings": {"fileUris": [], "script": "[parameters('startupScript')]"}}}], "outputs": {"vmName": {"type": "string", "value": "[reference('maxServeVM').osProfile.computerName]"}}}