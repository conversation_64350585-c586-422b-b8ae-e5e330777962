[project]
authors = ["Modular <<EMAIL>>"]
channels = ["conda-forge", "https://conda.modular.com/max-nightly/"]
description = "Examples of extending a graph with custom Mojo operations"
name = "Custom Operations"
platforms = ["osx-arm64", "linux-aarch64", "linux-64"]
version = "0.1.0"

[tasks]
addition = "python addition.py"
mandelbrot = "python mandelbrot.py"
vector_addition = "python vector_addition.py"
top_k = "python top_k.py"
fused_attention = "python fused_attention.py"
matrix_multiplication = "python matrix_multiplication.py"
histogram = "python histogram.py"
benchmark = "mojo benchmarks.mojo"
image_pipeline = "python image_pipeline.py"
test = { depends-on = [
  "addition",
  "mandelbrot",
  "vector_addition",
  "top_k",
  "fused_attention",
  "matrix_multiplication",
  "benchmark",
  "image_pipeline",
] }

[dependencies]
python = ">=3.9,<3.14"
max = ">=24.6.0.dev2024090821"
imageio = ">=2.37.0,<3"
