version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.modular.com/max-nightly/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-3_kmp_llvm.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-crt-tools-12.9.86-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cudart-12.9.79-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-cudart_linux-64-12.9.79-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cuobjdump-12.9.82-hbd13f7d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cupti-12.9.79-h9ab20c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvcc-tools-12.9.86-he02047a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvdisasm-12.9.88-hbd13f7d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvrtc-12.9.86-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvtx-12.9.79-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvvm-tools-12.9.86-he02047a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-12.9-h4f385c5_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cudnn-9.10.1.4-h7646684_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.2.1-py313h11186cd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_hfdb39a5_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_h372d94f_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcublas-12.9.1.4-h9ab20c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcudnn-9.10.1.4-h4840ae0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcudnn-dev-9.10.1.4-hcd2ec93_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcudss-0.5.0.16-h14340ca_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcufft-11.4.1.4-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcufile-1.14.1.1-ha8da6e3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurand-10.3.10.19-h9ab20c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcusolver-11.7.5.82-h9ab20c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcusparse-12.5.10.65-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_hc41d3b0_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmagma-2.9.0-h19665d7_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnl-3.11.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnvjitlink-12.9.86-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtorch-2.7.0-cuda126_mkl_h99b69db_300.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-20.1.7-h024ca30_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py313h8060acc_1.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2024.2.2-ha957f24_16.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-h24ddda3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nccl-********-h9b8ff78_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.5-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.2.6-py313h17eae1a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/optree-0.16.0-py313h33d0bda_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.2.1-py313h8db990d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-2.13.6-pyhc790b64_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-2.13.6-pyh217bc35_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-2.7.0-cuda126_mkl_py313_he20fe19_300.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-gpu-2.7.0-cuda126_mkl_ha999a5f_300.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py313h8e95178_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rdma-core-57.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-75.8.2-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sleef-3.8-h1b44611_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.13.0-hceb3a55_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/triton-3.3.0-cuda126py313hdd23915_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.0-h32cad80_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      linux-aarch64:
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-3_kmp_llvm.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arm-variant-1.2.0-sbsa.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/attr-2.5.1-h4e544f5_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-crt-tools-12.9.86-h579c4fd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-cudart-12.9.79-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-cudart_linux-aarch64-12.9.79-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-cuobjdump-12.9.82-h05609ea_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-cupti-12.9.79-hd55a8e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvcc-tools-12.9.86-h614329b_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvdisasm-12.9.88-h5101a13_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvrtc-12.9.86-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvtx-12.9.79-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvvm-tools-12.9.86-h614329b_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-12.9-h4f385c5_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cudnn-9.10.1.4-h71bdb64_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gmp-6.3.0-h0a1ffab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gmpy2-2.2.1-py313h0c041f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/keyutils-1.6.1-h4e544f5_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/krb5-1.21.3-h50a48e9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lcms2-2.17-hc88f144_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.43-h5e2c951_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lerc-4.0.0-hfdc4d58_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libabseil-20250127.1-cxx17_h18dbdb1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libblas-3.9.0-32_h1a9f1db_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcap-2.75-h51d75a7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcblas-3.9.0-32_hab92f65_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcublas-12.9.1.4-hd55a8e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcudnn-9.10.1.4-hb67e6b9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcudnn-dev-9.10.1.4-he9157cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcudss-0.5.0.16-hdd5694c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcufft-11.4.1.4-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcufile-1.14.1.1-h72d2f36_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcurand-10.3.10.19-hd55a8e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcusolver-11.7.5.82-hd55a8e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcusparse-12.5.10.65-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdeflate-1.24-he377734_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libedit-3.1.20250104-pl5321h976ea20_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype-2.13.3-h8af1aa0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype6-2.13.3-he93130f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcrypt-lib-1.11.1-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran5-15.1.0-hbc25352_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgpg-error-1.55-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libjpeg-turbo-3.1.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblapack-3.9.0-32_h411afd4_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmagma-2.9.0-h95896e5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libnl-3.11.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libnvjitlink-12.9.86-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenblas-0.3.30-pthreads_h9d3fd7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpng-1.6.49-hec79eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libprotobuf-5.29.3-h4edc36e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsodium-1.0.20-h68df207_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.1-h4970e9a_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsystemd0-257.7-h2bb824b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtiff-4.7.0-h7c15681_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtorch-2.7.0-cuda126_generic_h5977fb4_200.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libudev1-257.7-h7b9e449_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuv-1.51.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libwebp-base-1.5.0-h0886dbf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxcb-1.17.0-h262b8f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/llvm-openmp-20.1.7-h013ceaa_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lz4-c-1.10.0-h5ad3122_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/markupsafe-3.0.2-py313h7815b11_1.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-aarch64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-aarch64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpc-1.3.1-h783934e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpfr-4.2.1-h2305555_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/nccl-********-h3544485_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.5-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nomkl-1.0-h5ca1d4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/numpy-2.2.6-py313hcf1be6b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openjpeg-2.5.3-h3f56577_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.0-hd08dc88_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/optree-0.16.0-py313h44a8f36_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pillow-11.2.1-py313h96bbe82_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pthread-stubs-0.4-h86ecc28_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-2.13.6-pyhc790b64_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-2.13.6-pyh217bc35_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pytorch-2.7.0-cuda126_generic_py313_h982c678_200.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pytorch-gpu-2.7.0-cuda126_generic_hbf71451_200.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyzmq-27.0.0-py313h6e72e74_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/rdma-core-57.0-h1d056c8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-75.8.2-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sleef-3.8-h8fb0607_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tornado-6.5.1-py313h6a51379_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/triton-3.3.0-cuda126py313h3a9e705_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.0-h32cad80_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxau-1.0.12-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxdmcp-1.1.5-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zeromq-4.3.5-h5efb499_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstd-1.5.7-hbcf94c1_2.conda
  whisper:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.modular.com/max-nightly/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-3_kmp_llvm.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiohappyeyeballs-2.6.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.12.13-py313h8060acc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.3.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/audioop-lts-0.2.1-py313h5d4cfb6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/audioread-3.0.1-py313h78bf25f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.9.0-hbfa7f16_15.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.9.2-h5e3027f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.12.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.3.1-hafb2847_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.5.4-h76f0014_12.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.10.2-h015de20_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.20.1-hdfce8c9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.13.1-h1e5e6c0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.8.3-h5e174a9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.2.4-hafb2847_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.2.7-hafb2847_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.32.10-hff780f1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.510-h937e755_11.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-core-cpp-1.14.0-h5cfcd09_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-identity-cpp-1.10.0-h113e628_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-blobs-cpp-12.13.0-h3cf044e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-common-cpp-12.8.0-h736e048_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-files-datalake-cpp-12.12.0-ha633028_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-bin-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py313h46c70d0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.6.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py313hfab6e84_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/contourpy-1.3.2-py313h33d0bda_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-crt-tools-12.9.86-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cudart-12.9.79-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-cudart_linux-64-12.9.79-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cuobjdump-12.9.82-hbd13f7d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cupti-12.9.79-h9ab20c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvcc-tools-12.9.86-he02047a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvdisasm-12.9.88-hbd13f7d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvrtc-12.9.86-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvtx-12.9.79-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvvm-tools-12.9.86-he02047a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-12.9-h4f385c5_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cudnn-9.10.1.4-h7646684_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/datasets-3.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/dill-0.3.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.1.1-gpl_hceff1ee_706.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fonttools-4.58.4-py313h8060acc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.6.0-py313h61b7b33_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-h5888daf_1005.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.1-hbabe93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.2.1-py313h11186cd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/hf-xet-1.1.5-py39h260a9e5_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.33.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/kiwisolver-1.4.7-py313h33d0bda_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/lazy-loader-0.4-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lazy_loader-0.4-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/level-zero-1.23.0-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-20.0.0-h019e7cd_8_cuda.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-20.0.0-hb826db4_8_cuda.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-20.0.0-hb826db4_8_cuda.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-20.0.0-h69308b4_8_cuda.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.3-h52826cd_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_hfdb39a5_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_h372d94f_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcublas-12.9.1.4-h9ab20c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcudnn-9.10.1.4-h4840ae0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcudnn-dev-9.10.1.4-hcd2ec93_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcudss-0.5.0.16-h14340ca_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcufft-11.4.1.4-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcufile-1.14.1.1-ha8da6e3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurand-10.3.10.19-h9ab20c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.14.1-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcusolver-11.7.5.82-h9ab20c4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcusparse-12.5.10.65-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.36.0-hc4361e1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.36.0-h0121fbd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.71.0-h8e591d7_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_hc41d3b0_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmagma-2.9.0-h19665d7_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnl-3.11.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnvjitlink-12.9.86-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-1.21.0-hd1b1c89_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-headers-1.21.0-ha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-npu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2025.0.0-h684f15b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-20.0.0-h3f30f2e_8_cuda.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2024.07.02-hba17884_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/librosa-0.11.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-he92a37e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.21.0-h0e7cc3e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtorch-2.7.0-cuda126_mkl_h99b69db_300.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.10.0-h202a827_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libva-2.22.0-h4f16b4b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-20.1.7-h024ca30_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/llvmlite-0.44.0-py313h1b76d92_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py313h8060acc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-base-3.10.3-py313h129903b_0.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2024.2.2-ha957f24_16.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-h24ddda3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/msgpack-python-1.1.1-py313h33d0bda_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.5.1-py313h8060acc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/multiprocess-0.70.16-py313h536fd9c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nccl-********-h9b8ff78_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.5-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nlohmann_json-3.12.0-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numba-0.61.2-py313h50b8c88_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.2.6-py313h17eae1a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.6.0-hc22cd8d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/optree-0.16.0-py313h33d0bda_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.1.2-h17f744e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.0-py313ha87cce1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h9ac818e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.2.1-py313h8db990d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pooch-1.8.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/prometheus-cpp-1.3.0-ha5d0236_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/propcache-0.3.1-py313h8060acc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.15-h3f63f65_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-20.0.0-py313h78bf25f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-core-20.0.0-py313hc6b0d6e_0_cuda.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-2.13.6-pyhc790b64_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-2.13.6-pyh217bc35_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysoundfile-0.13.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.5.0-py313h536fd9c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-2.7.0-cuda126_mkl_py313_he20fe19_300.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-gpu-2.7.0-cuda126_mkl_ha999a5f_300.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py313h8060acc_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py313h8e95178_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qhull-2020.2-h434a139_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rdma-core-57.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2024.07.02-h9925aae_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/regex-2024.11.6-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.5.21-h7ab7c64_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/safetensors-0.5.3-py313h920b4c0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scikit-learn-1.7.0-py313h8ef605b_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.15.2-py313h86fcf2b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-75.8.2-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sleef-3.8-h1b44611_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/soxr-0.1.3-h0b41bf4_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/soxr-python-0.5.0.post1-py313h46c70d0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/standard-aifc-3.13.0-py313h78bf25f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/standard-chunk-3.13.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/standard-sunau-3.13.0-py313h78bf25f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.13.0-hceb3a55_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tokenizers-0.21.2-py313h1191936_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/transformers-4.53.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/triton-3.3.0-cuda126py313hdd23915_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.0-h32cad80_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wayland-protocols-1.45-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.3-hb47aa4a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.20.1-py313h8060acc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py313h536fd9c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      linux-aarch64:
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-3_kmp_llvm.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiohappyeyeballs-2.6.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aiohttp-3.12.13-py313h857f82b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.3.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/alsa-lib-1.2.14-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aom-3.9.1-hcccb83c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arm-variant-1.2.0-sbsa.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/attr-2.5.1-h4e544f5_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/audioread-3.0.1-py313hd81a959_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-auth-0.9.0-h41219c9_15.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-cal-0.9.2-h2067cef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-common-0.12.3-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-compression-0.3.1-h0e592bd_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-event-stream-0.5.4-h5e90aed_12.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-http-0.10.2-h815e00f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-io-0.20.1-h6822acb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-mqtt-0.13.1-h2005bbd_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-s3-0.8.3-hd2cf7fb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-sdkutils-0.2.4-h0e592bd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-checksums-0.2.7-h0e592bd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-crt-cpp-0.32.10-h2f6a9f5_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-sdk-cpp-1.11.510-h250aa87_11.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-core-cpp-1.14.0-h1887c18_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-identity-cpp-1.10.0-h47b0b28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-storage-blobs-cpp-12.13.0-h185ecfd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-storage-common-cpp-12.8.0-h1b94036_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-storage-files-datalake-cpp-12.12.0-h37d6d07_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/brotli-1.1.0-h86ecc28_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/brotli-bin-1.1.0-h86ecc28_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/brotli-python-1.1.0-py313hb6a6212_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/c-ares-1.34.5-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cairo-1.18.4-h83712da_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.6.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cffi-1.17.1-py313h2135053_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/contourpy-1.3.2-py313h44a8f36_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-crt-tools-12.9.86-h579c4fd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-cudart-12.9.79-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-cudart_linux-aarch64-12.9.79-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-cuobjdump-12.9.82-h05609ea_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-cupti-12.9.79-hd55a8e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvcc-tools-12.9.86-h614329b_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvdisasm-12.9.88-h5101a13_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvrtc-12.9.86-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvtx-12.9.79-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvvm-tools-12.9.86-h614329b_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-12.9-h4f385c5_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cudnn-9.10.1.4-h71bdb64_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/datasets-3.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dav1d-1.2.1-h31becfc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dbus-1.16.2-heda779d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/dill-0.3.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ffmpeg-7.1.1-gpl_h3bf3c16_706.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fontconfig-2.15.0-h8dda3cd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fonttools-4.58.4-py313h857f82b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/freetype-2.13.3-h8af1aa0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fribidi-1.0.10-hb9de7d4_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/frozenlist-1.6.0-py313hf157169_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gdk-pixbuf-2.42.12-ha61d561_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-0.24.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-tools-0.24.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gflags-2.2.2-h5ad3122_1005.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/glog-0.7.1-h468a4a4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gmp-6.3.0-h0a1ffab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gmpy2-2.2.1-py313h0c041f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/graphite2-1.3.14-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/harfbuzz-11.2.1-h405b6a2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/hf-xet-1.1.5-py39h076f640_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.33.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/keyutils-1.6.1-h4e544f5_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/kiwisolver-1.4.7-py313h1d91839_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/krb5-1.21.3-h50a48e9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lame-3.100-h4e544f5_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/lazy-loader-0.4-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lazy_loader-0.4-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lcms2-2.17-hc88f144_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.43-h5e2c951_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lerc-4.0.0-hfdc4d58_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libabseil-20250127.1-cxx17_h18dbdb1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libarrow-20.0.0-h82abc5a_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libarrow-acero-20.0.0-h3b568fd_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libarrow-dataset-20.0.0-h3b568fd_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libarrow-substrait-20.0.0-hcd5cee9_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-0.24.1-h5e0f5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-devel-0.24.1-h5e0f5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libass-0.17.3-h3c9f632_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libblas-3.9.0-32_h1a9f1db_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libbrotlicommon-1.1.0-h86ecc28_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libbrotlidec-1.1.0-h86ecc28_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libbrotlienc-1.1.0-h86ecc28_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcap-2.75-h51d75a7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcblas-3.9.0-32_hab92f65_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcrc32c-1.1.2-h01db608_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcublas-12.9.1.4-hd55a8e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcudnn-9.10.1.4-hb67e6b9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcudnn-dev-9.10.1.4-he9157cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcudss-0.5.0.16-hdd5694c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcufft-11.4.1.4-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcufile-1.14.1.1-h72d2f36_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcurand-10.3.10.19-hd55a8e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcurl-8.14.1-h6702fde_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcusolver-11.7.5.82-hd55a8e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcusparse-12.5.10.65-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdeflate-1.24-he377734_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdrm-2.4.125-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libedit-3.1.20250104-pl5321h976ea20_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libegl-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libev-4.33-h31becfc_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libevent-2.1.12-h4ba1bb4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libflac-1.4.3-h2f0025b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype-2.13.3-h8af1aa0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype6-2.13.3-he93130f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcrypt-lib-1.11.1-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-0.24.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-devel-0.24.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran5-15.1.0-hbc25352_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgl-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglib-2.84.2-hc022ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglvnd-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglx-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgoogle-cloud-2.36.0-h1c497bb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgoogle-cloud-storage-2.36.0-hb9b2b65_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgpg-error-1.55-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgrpc-1.71.0-h107bb78_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libhwloc-2.11.2-default_h2c612a5_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libiconv-1.18-hc99b53d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libjpeg-turbo-3.1.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblapack-3.9.0-32_h411afd4_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmagma-2.9.0-h95896e5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libnghttp2-1.64.0-hc8609a4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libnl-3.11.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libnvjitlink-12.9.86-h3ae8b8a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libogg-1.3.5-h86ecc28_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenblas-0.3.30-pthreads_h9d3fd7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopentelemetry-cpp-1.21.0-h77ebfab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopentelemetry-cpp-headers-1.21.0-h8af1aa0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-2025.0.0-hd63d6c0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-arm-cpu-plugin-2025.0.0-hd63d6c0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-batch-plugin-2025.0.0-hf15766e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-plugin-2025.0.0-hf15766e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-hetero-plugin-2025.0.0-ha8e9e04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-ir-frontend-2025.0.0-ha8e9e04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-onnx-frontend-2025.0.0-hd8f0270_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-paddle-frontend-2025.0.0-hd8f0270_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-pytorch-frontend-2025.0.0-h5ad3122_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-frontend-2025.0.0-h33e842c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5ad3122_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopus-1.5.2-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libparquet-20.0.0-hfc78867_8_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpciaccess-0.18-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpng-1.6.49-hec79eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libprotobuf-5.29.3-h4edc36e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libre2-11-2024.07.02-h201e9ed_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/librosa-0.10.2.post1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/librsvg-2.58.4-h3ac5bce_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsndfile-1.2.2-h79657aa_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsodium-1.0.20-h68df207_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.1-h4970e9a_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libssh2-1.11.1-h18c354c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsystemd0-257.7-h2bb824b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libthrift-0.21.0-h154c74f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtiff-4.7.0-h7c15681_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtorch-2.7.0-cuda126_generic_h5977fb4_200.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libudev1-257.7-h7b9e449_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libunwind-1.6.2-h01db608_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liburing-2.10-h17cf362_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libusb-1.0.29-h06eaf92_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libutf8proc-2.10.0-hb15dbc2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuv-1.51.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvorbis-1.3.7-h01db608_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvpx-1.14.1-h0a1ffab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libwebp-base-1.5.0-h0886dbf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxcb-1.17.0-h262b8f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxkbcommon-1.10.0-hbab7b08_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxml2-2.13.8-he060846_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/llvm-openmp-20.1.7-h013ceaa_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/llvmlite-0.44.0-py313h8a79bcf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lz4-c-1.10.0-h5ad3122_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/markupsafe-3.0.2-py313h7815b11_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/matplotlib-base-3.10.3-py313h16bfeab_0.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-aarch64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-aarch64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpc-1.3.1-h783934e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpfr-4.2.1-h2305555_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpg123-1.32.9-h65af167_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/msgpack-python-1.1.1-py313h44a8f36_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/multidict-6.5.1-py313h857f82b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/multiprocess-0.70.16-py313h31d5739_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/nccl-********-h3544485_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.5-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/nlohmann_json-3.12.0-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nomkl-1.0-h5ca1d4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/numba-0.61.2-py313h83e9d6b_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/numpy-2.2.6-py313hcf1be6b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openh264-2.6.0-h0564a2a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openjpeg-2.5.3-h3f56577_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.0-hd08dc88_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/optree-0.16.0-py313h44a8f36_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/orc-2.1.2-h38030b9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pandas-2.3.0-py313h7b4b6ea_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pango-1.56.3-h1e6a6fd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pcre2-10.45-hf4ec17f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pillow-11.2.1-py313h96bbe82_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pixman-0.46.2-h86a87f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pooch-1.8.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/prometheus-cpp-1.3.0-h7938499_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/propcache-0.3.1-py313h857f82b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pthread-stubs-0.4-h86ecc28_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pugixml-1.15-h6ef32b0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pulseaudio-client-17.0-h2f84921_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyarrow-20.0.0-py313h1258fbd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyarrow-core-20.0.0-py313h656e22b_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-2.13.6-pyhc790b64_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-2.13.6-pyh217bc35_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysoundfile-0.13.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-xxhash-3.5.0-py313h6a51379_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pytorch-2.7.0-cuda126_generic_py313_h982c678_200.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pytorch-gpu-2.7.0-cuda126_generic_hbf71451_200.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyyaml-6.0.2-py313h857f82b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyzmq-27.0.0-py313h6e72e74_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/qhull-2020.2-h70be974_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/rdma-core-57.0-h1d056c8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/re2-2024.07.02-haa97905_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/regex-2024.11.6-py313h31d5739_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/s2n-1.5.21-hcc03fc0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/safetensors-0.5.3-py313h8aa417a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/scikit-learn-1.7.0-py313h2a234e8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/scipy-1.15.2-py313hfdb6400_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2-2.32.54-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl3-3.2.16-h7e2c5d6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-75.8.2-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sleef-3.8-h8fb0607_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/snappy-1.2.1-hd4fb6f5_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/soxr-0.1.3-hb4cce97_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/soxr-python-0.5.0.post1-py313hb6a6212_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/svt-av1-3.0.2-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tbb-2022.1.0-hf6e3e71_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tokenizers-0.21.2-py313h5e73428_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tornado-6.5.1-py313h6a51379_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/transformers-4.53.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/triton-3.3.0-cuda126py313h3a9e705_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.0-h32cad80_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/wayland-1.23.1-h698ed42_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x264-1!164.3095-h4e544f5_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x265-3.5-hdd96247_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xkeyboard-config-2.45-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libice-1.1.2-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libsm-1.2.6-h0808dbd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libx11-1.8.12-hca56bd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxau-1.0.12-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxcursor-1.2.3-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxdmcp-1.1.5-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxext-1.3.6-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxfixes-6.0.1-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxrender-0.9.12-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xxhash-0.8.3-hd794028_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/yaml-0.2.5-hf897c2e_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/yarl-1.20.1-py313h857f82b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zeromq-4.3.5-h5efb499_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zlib-1.3.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstandard-0.23.0-py313h31d5739_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstd-1.5.7-hbcf94c1_2.conda
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-3_kmp_llvm.conda
  build_number: 3
  sha256: cec7343e76c9da6a42c7e7cba53391daa6b46155054ef61a5ef522ea27c5a058
  md5: ee5c2118262e30b972bc0b4db8ef0ba5
  depends:
  - llvm-openmp >=9.0.1
  license: BSD-3-Clause
  license_family: BSD
  size: 7649
  timestamp: 1741390353130
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-3_kmp_llvm.conda
  build_number: 3
  sha256: 5ec4d1d1dca8cc57e48132ec795e1df96082a7a69037f07add30d73d388df6ff
  md5: 21ea61af8405db60077be8d0006d09a1
  depends:
  - llvm-openmp >=9.0.1
  license: BSD-3-Clause
  license_family: BSD
  size: 7644
  timestamp: 1741391334729
- conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
  sha256: a3967b937b9abf0f2a99f3173fa4630293979bd1644709d89580e7c62a544661
  md5: aaa2a381ccc56eac91d63b6c1240312f
  depends:
  - cpython
  - python-gil
  license: MIT
  license_family: MIT
  size: 8191
  timestamp: 1744137672556
- conda: https://conda.anaconda.org/conda-forge/noarch/aiohappyeyeballs-2.6.1-pyhd8ed1ab_0.conda
  sha256: 7842ddc678e77868ba7b92a726b437575b23aaec293bca0d40826f1026d90e27
  md5: 18fd895e0e775622906cdabfc3cf0fb4
  depends:
  - python >=3.9
  license: PSF-2.0
  license_family: PSF
  size: 19750
  timestamp: 1741775303303
- conda: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.12.13-py313h8060acc_0.conda
  sha256: a1713b06928c59864206428c931e8b1042992128ae3491bbff00a8a707d3b5f0
  md5: 5509be7947f1cf7c0335a45278fd09d5
  depends:
  - __glibc >=2.17,<3.0.a0
  - aiohappyeyeballs >=2.5.0
  - aiosignal >=1.1.2
  - attrs >=17.3.0
  - frozenlist >=1.1.1
  - libgcc >=13
  - multidict >=4.5,<7.0
  - propcache >=0.2.0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - yarl >=1.17.0,<2.0
  license: MIT AND Apache-2.0
  license_family: Apache
  size: 1010019
  timestamp: 1749925691050
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aiohttp-3.12.13-py313h857f82b_0.conda
  sha256: 6c9dd30cb7d36ec1a069db464af7a2edaf45013560f4c4736353cc268b8f30da
  md5: 2b0cdbaa86e297ab27b548ed26df6612
  depends:
  - aiohappyeyeballs >=2.5.0
  - aiosignal >=1.1.2
  - attrs >=17.3.0
  - frozenlist >=1.1.1
  - libgcc >=13
  - multidict >=4.5,<7.0
  - propcache >=0.2.0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - yarl >=1.17.0,<2.0
  license: MIT AND Apache-2.0
  license_family: Apache
  size: 1001083
  timestamp: 1749923600979
- conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.3.2-pyhd8ed1ab_0.conda
  sha256: 7de8ced1918bbdadecf8e1c1c68237fe5709c097bd9e0d254f4cad118f4345d0
  md5: 1a3981115a398535dbe3f6d5faae3d36
  depends:
  - frozenlist >=1.1.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 13229
  timestamp: 1734342253061
- conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
  sha256: b9214bc17e89bf2b691fad50d952b7f029f6148f4ac4fe7c60c08f093efdf745
  md5: 76df83c2a9035c54df5d04ff81bcc02d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  size: 566531
  timestamp: 1744668655747
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/alsa-lib-1.2.14-h86ecc28_0.conda
  sha256: 0aa836f6dd9132f243436898ed8024f408910f65220bafbfc95f71ab829bb395
  md5: a696b24c1b473ecc4774bcb5a6ac6337
  depends:
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  size: 595290
  timestamp: 1744668754404
- conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
  sha256: b08ef033817b5f9f76ce62dfcac7694e7b6b4006420372de22494503decac855
  md5: 346722a0be40f6edc53f12640d301338
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 2706396
  timestamp: 1718551242397
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aom-3.9.1-hcccb83c_0.conda
  sha256: ac438ce5d3d3673a9188b535fc7cda413b479f0d52536aeeac1bd82faa656ea0
  md5: cc744ac4efe5bcaa8cca51ff5b850df0
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 3250813
  timestamp: 1718551360260
- conda: https://conda.anaconda.org/conda-forge/noarch/arm-variant-1.2.0-sbsa.conda
  sha256: 0658cac65071ace5beded633851681e6f0b381040c8ce313bbe2a0ab410c5072
  md5: b7d6244b9c7a660f10336645e73c2cd2
  license: BSD-3-Clause
  license_family: BSD
  size: 7126
  timestamp: 1742928603302
- conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
  sha256: 82c13b1772c21fc4a17441734de471d3aabf82b61db9b11f4a1bd04a9c4ac324
  md5: d9c69a24ad678ffce24c6543a0176b00
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  size: 71042
  timestamp: 1660065501192
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/attr-2.5.1-h4e544f5_1.tar.bz2
  sha256: 2c793b48e835a8fac93f1664c706442972a0206963bf8ca202e83f7f4d29a7d7
  md5: 1ef6c06fec1b6f5ee99ffe2152e53568
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  size: 74992
  timestamp: 1660065534958
- conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
  sha256: 99c53ffbcb5dc58084faf18587b215f9ac8ced36bbfb55fa807c00967e419019
  md5: a10d11958cadc13fdb43df75f8b1903f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 57181
  timestamp: 1741918625732
- conda: https://conda.anaconda.org/conda-forge/linux-64/audioop-lts-0.2.1-py313h5d4cfb6_2.conda
  sha256: 97da86da98d4b2b7ecb9687d028901a23a2aeb31d2c9d40605ea3c9d82d8102a
  md5: f7c6ecb6735da09a41e6aee2d9f634fa
  depends:
  - python
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - python_abi 3.13.* *_cp313
  license: EPL-2.0
  size: 38565
  timestamp: 1748891816169
- conda: https://conda.anaconda.org/conda-forge/linux-64/audioread-3.0.1-py313h78bf25f_2.conda
  sha256: 606fe88f4d2e1cf7dd27a3fffbdb6f522d71f4fcb1c9c931e10adefb66f7f0fa
  md5: 63d4b59a49b3941c3352df080d2c9245
  depends:
  - ffmpeg
  - python >=3.13.0rc1,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: MIT
  license_family: MIT
  size: 44327
  timestamp: 1725357595449
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/audioread-3.0.1-py313hd81a959_2.conda
  sha256: 3291434a8dafdcd265d57085522e8aeaf445cef54ef788a23b9d70084f98c0f2
  md5: 1af6a50a3d34dadfff5a27e45dcd8ce8
  depends:
  - ffmpeg
  - python >=3.13.0rc1,<3.14.0a0
  - python >=3.13.0rc1,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: MIT
  license_family: MIT
  size: 45415
  timestamp: 1725357675151
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.9.0-hbfa7f16_15.conda
  sha256: 85086df9b358450196a13fc55bab1c552227df78cafddbe2d15caaea458b41a6
  md5: 16baa9bb7f70a1e457a82023898314a7
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-sdkutils >=0.2.4,<0.2.5.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 122993
  timestamp: 1750291448852
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-auth-0.9.0-h41219c9_15.conda
  sha256: 6f85cf67566af94ac2c188e5719a4eb008d5236498a5bcc86179a4dbc08eb477
  md5: 5bc00379a7e446113a9697ac9d442a28
  depends:
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-sdkutils >=0.2.4,<0.2.5.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-io >=0.20.1,<0.20.2.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 129524
  timestamp: 1750291488278
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.9.2-h5e3027f_0.conda
  sha256: d61cce967e6d97d03aa2828458f7344cdc93422fd2c1126976ab8f475a313363
  md5: 0ead3ab65460d51efb27e5186f50f8e4
  depends:
  - __glibc >=2.17,<3.0.a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 51039
  timestamp: 1749095567725
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-cal-0.9.2-h2067cef_0.conda
  sha256: 02f767c25b665365819501e4d31e384eedc4c43f986e836b9b5576a1f7475f1c
  md5: fb55949bdf09a5400e46b3ddbd33c8f2
  depends:
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 54087
  timestamp: 1749095660060
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.12.3-hb9d3cd8_0.conda
  sha256: 251883d45fbc3bc88a8290da073f54eb9d17e8b9edfa464d80cff1b948c571ec
  md5: 8448031a22c697fac3ed98d69e8a9160
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 236494
  timestamp: 1747101172537
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-common-0.12.3-h86ecc28_0.conda
  sha256: 88f38901a49375c1341a1b65abb558cc3f188ee5cd5c70d1614b18b437cf97bc
  md5: 4bd9536e66933e9c5dbeca5207c1367a
  depends:
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 258550
  timestamp: 1747101196695
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.3.1-hafb2847_5.conda
  sha256: 68e7ec0ab4f5973343de089ac71c7b9b9387c35640c61e0236ad45fc3dbfaaaa
  md5: e96cc668c0f9478f5771b37d57f90386
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 21817
  timestamp: 1747144982788
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-compression-0.3.1-h0e592bd_5.conda
  sha256: 6cc055c869931590ae5f1e0da3bc3695222b06d55025596a6d3b7406beafdbeb
  md5: 72e5b0ae7b9491c1b0c944493e07eb13
  depends:
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 23305
  timestamp: 1747145033231
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.5.4-h76f0014_12.conda
  sha256: 7b89ed99ac73c863bea4479f1f1af6ce250f9f1722d2804e07cf05d3630c7e08
  md5: f978f2a3032952350d0036c4c4a63bd6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 57252
  timestamp: 1750287878861
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-event-stream-0.5.4-h5e90aed_12.conda
  sha256: 72b22a068a78f72e1d01e33d068a0b4050f51f0b24f2df845df848b2a0f6bdd4
  md5: 576d69f77f55beb1d15d86807a77d1c7
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 60112
  timestamp: 1750287914924
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.10.2-h015de20_2.conda
  sha256: ca0268cead19e985f9b153613f0f6cdb46e0ca32e1647466c506f256269bcdd9
  md5: ad05d594704926ba7c0c894a02ea98f1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-compression >=0.3.1,<0.3.2.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 223038
  timestamp: 1750289165728
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-http-0.10.2-h815e00f_2.conda
  sha256: 9471abc885d475d41bd40d296cc047c5df2f61c71a0e47d253022061b7a3583b
  md5: fa95a344303d730a5e5be9404e7b9b2f
  depends:
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-compression >=0.3.1,<0.3.2.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 210024
  timestamp: 1750289206854
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.20.1-hdfce8c9_0.conda
  sha256: c6bd4f067a7829795e1c44e4536b71d46f55f69569216aed34a7b375815fa046
  md5: dd2d3530296d75023a19bc9dfb0a1d59
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - s2n >=1.5.21,<1.5.22.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 179223
  timestamp: 1749844480175
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-io-0.20.1-h6822acb_0.conda
  sha256: a6dc1d1274b835a926b64152989949401f31e8909295131e287ac3d9afce1ea1
  md5: e68ddc5cc9cffb254ce20b5fb0c2a41b
  depends:
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - s2n >=1.5.21,<1.5.22.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 183182
  timestamp: 1749844527845
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.13.1-h1e5e6c0_3.conda
  sha256: f9e63492d5dd17f361878ce7efa1878de27225216b4e07990a6cb18c378014dc
  md5: d55921ca3469224f689f974278107308
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 215867
  timestamp: 1750291920145
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-mqtt-0.13.1-h2005bbd_3.conda
  sha256: d058ba0b30deb1de8e06db82fe7026fdff85e23f5fdd84bc137439beac9fee3f
  md5: cd083ac2972333959c6c9d2ea5b7e5fb
  depends:
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 189458
  timestamp: 1750291964588
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.8.3-h5e174a9_0.conda
  sha256: f4e7b200da5df7135cd087618fa30b2cd60cec0eebbd5570fb4c1e9a789dd9aa
  md5: dea2540e57e8c1b949ca58ff4c7c0cbf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - openssl >=3.5.0,<4.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  license: Apache-2.0
  size: 133960
  timestamp: 1750831815089
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-s3-0.8.3-hd2cf7fb_0.conda
  sha256: d6cbdd7c851edbd2bed0882c9ef4eae3ce54bbdf19c4c2de81184ce67845f1c2
  md5: 327bcf39263487193fbd7088ffb45a15
  depends:
  - libgcc >=13
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  - openssl >=3.5.0,<4.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  size: 138226
  timestamp: 1750831830218
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.2.4-hafb2847_0.conda
  sha256: 18c588c386e21e2a926c6f3c1ba7aaf69059ce1459a134f7c8c1ebfc68cf67ec
  md5: 65853df44b7e4029d978c50be888ed89
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 59037
  timestamp: 1747308292628
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-c-sdkutils-0.2.4-h0e592bd_0.conda
  sha256: 2464da7197282b2f620ddf35980a18a7201753538d1f45b3112271dade542aef
  md5: 10c5980362b57d81f8daceb04e2b88ff
  depends:
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 62482
  timestamp: 1747308316515
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.2.7-hafb2847_1.conda
  sha256: 03a5e4b3dcda35696133632273043d0b81e55129ff0f9e6d75483aa8eb96371b
  md5: 6d28d50637fac4f081a0903b4b33d56d
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 76627
  timestamp: 1747141741534
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-checksums-0.2.7-h0e592bd_1.conda
  sha256: 0e7bd4ab70f349e4377b2eaf706a94fbed917133f0cd7004739933fee0d59db0
  md5: 714c4bd6d9a589e26d060332239008aa
  depends:
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 77027
  timestamp: 1747141777732
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.32.10-hff780f1_1.conda
  sha256: 9602a5199dccf257709afdef326abfde6e84c63862b7cee59979803c4d636840
  md5: 843f52366658086c4f0b0654afbf3730
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - aws-c-mqtt >=0.13.1,<0.13.2.0a0
  - aws-c-event-stream >=0.5.4,<0.5.5.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  - aws-c-s3 >=0.8.3,<0.8.4.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-sdkutils >=0.2.4,<0.2.5.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-io >=0.20.1,<0.20.2.0a0
  license: Apache-2.0
  size: 399987
  timestamp: 1750855462459
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-crt-cpp-0.32.10-h2f6a9f5_1.conda
  sha256: 436bd491617cf793db364daa010bf1177aa010a730e1ef56fbf4c02e53d9c157
  md5: 3aea0aea0aaf42098e6bf416f6ed01e3
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-mqtt >=0.13.1,<0.13.2.0a0
  - aws-c-io >=0.20.1,<0.20.2.0a0
  - aws-c-http >=0.10.2,<0.10.3.0a0
  - aws-c-event-stream >=0.5.4,<0.5.5.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-sdkutils >=0.2.4,<0.2.5.0a0
  - aws-c-s3 >=0.8.3,<0.8.4.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  license: Apache-2.0
  size: 320029
  timestamp: 1750855470521
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.510-h937e755_11.conda
  sha256: 6febc586060ed0458a1cfbb8d6ceae94f5dd29e18315c4f0bc239df2b07715df
  md5: 11264cb7d5ad4c27d3eaffc909839698
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  - aws-c-event-stream >=0.5.4,<0.5.5.0a0
  - libcurl >=8.14.1,<9.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - libzlib >=1.3.1,<2.0a0
  - aws-crt-cpp >=0.32.10,<0.32.11.0a0
  license: Apache-2.0
  size: 3401544
  timestamp: 1750844008776
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aws-sdk-cpp-1.11.510-h250aa87_11.conda
  sha256: b5bdab8438396fca6a0b43dc7a79d36a8a044a9d482d6c86003869736e18e44b
  md5: e2b5a0d2d7064ccb4659f87f320eb181
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - libzlib >=1.3.1,<2.0a0
  - aws-c-event-stream >=0.5.4,<0.5.5.0a0
  - aws-crt-cpp >=0.32.10,<0.32.11.0a0
  - libcurl >=8.14.1,<9.0a0
  license: Apache-2.0
  size: 3279105
  timestamp: 1750844044534
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-core-cpp-1.14.0-h5cfcd09_0.conda
  sha256: fe07debdb089a3db17f40a7f20d283d75284bb4fc269ef727b8ba6fc93f7cb5a
  md5: 0a8838771cc2e985cd295e01ae83baf1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.10.1,<9.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 345117
  timestamp: 1728053909574
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-core-cpp-1.14.0-h1887c18_0.conda
  sha256: 8967b3ccee4d74e61f6ec82dd8efb9deb854ee7ba012dfe767b7a92e0ac77724
  md5: e0c3a906a41be769f0ae20ca3e31cfc0
  depends:
  - libcurl >=8.10.1,<9.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 338650
  timestamp: 1728055589907
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-identity-cpp-1.10.0-h113e628_0.conda
  sha256: 286b31616c191486626cb49e9ceb5920d29394b9e913c23adb7eb637629ba4de
  md5: 73f73f60854f325a55f1d31459f2ab73
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 232351
  timestamp: 1728486729511
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-identity-cpp-1.10.0-h47b0b28_0.conda
  sha256: 1c72423b9beba167d2f01b80dc204da77240a8266f1edb3d89510c852b300d69
  md5: 94e73a7877743a85c57091d8afab2348
  depends:
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 217132
  timestamp: 1728488096615
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-blobs-cpp-12.13.0-h3cf044e_1.conda
  sha256: 2606260e5379eed255bcdc6adc39b93fb31477337bcd911c121fc43cd29bf394
  md5: 7eb66060455c7a47d9dcdbfa9f46579b
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-storage-common-cpp >=12.8.0,<12.8.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 549342
  timestamp: 1728578123088
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-storage-blobs-cpp-12.13.0-h185ecfd_1.conda
  sha256: 280ec70009a92626054f58e45b168fce393e71a9710587488bd8401628cda481
  md5: 221e1e5ecb2643e113f32b3229d5ba33
  depends:
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-storage-common-cpp >=12.8.0,<12.8.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 502934
  timestamp: 1728580241002
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-common-cpp-12.8.0-h736e048_1.conda
  sha256: 273475f002b091b66ce7366da04bf164c3732c03f8692ab2ee2d23335b6a82ba
  md5: 13de36be8de3ae3f05ba127631599213
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.12.7,<2.14.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 149312
  timestamp: 1728563338704
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-storage-common-cpp-12.8.0-h1b94036_1.conda
  sha256: 146e76aac169e3dbdce5d3b142b7930ac643795c765e7655d1989905ec7d3231
  md5: 793b1080ab2d958980f137a8643cd6e8
  depends:
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.12.7,<2.14.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 140832
  timestamp: 1728565334900
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-files-datalake-cpp-12.12.0-ha633028_1.conda
  sha256: 5371e4f3f920933bb89b926a85a67f24388227419abd6e99f6086481e5e8d5f2
  md5: 7c1980f89dd41b097549782121a73490
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-storage-blobs-cpp >=12.13.0,<12.13.1.0a0
  - azure-storage-common-cpp >=12.8.0,<12.8.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 287366
  timestamp: 1728729530295
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/azure-storage-files-datalake-cpp-12.12.0-h37d6d07_1.conda
  sha256: 4079c617a75682e49bae63670d58fd6078ccfbbe55ca1f994acab3a74ab6bbcc
  md5: b724f3b4b7f4e9b36c58cbe3ed8610a2
  depends:
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-storage-blobs-cpp >=12.13.0,<12.13.1.0a0
  - azure-storage-common-cpp >=12.8.0,<12.8.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 260547
  timestamp: 1728730924071
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-1.1.0-hb9d3cd8_3.conda
  sha256: c969baaa5d7a21afb5ed4b8dd830f82b78e425caaa13d717766ed07a61630bec
  md5: 5d08a0ac29e6a5a984817584775d4131
  depends:
  - __glibc >=2.17,<3.0.a0
  - brotli-bin 1.1.0 hb9d3cd8_3
  - libbrotlidec 1.1.0 hb9d3cd8_3
  - libbrotlienc 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19810
  timestamp: 1749230148642
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/brotli-1.1.0-h86ecc28_3.conda
  sha256: 71291a171728400f7af6be1a4ffaf805cff3684ae621ae5f792171235c7171f1
  md5: 725908554f2bf8f68502bbade3ea3489
  depends:
  - brotli-bin 1.1.0 h86ecc28_3
  - libbrotlidec 1.1.0 h86ecc28_3
  - libbrotlienc 1.1.0 h86ecc28_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19937
  timestamp: 1749230328962
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-bin-1.1.0-hb9d3cd8_3.conda
  sha256: ab74fa8c3d1ca0a055226be89e99d6798c65053e2d2d3c6cb380c574972cd4a7
  md5: 58178ef8ba927229fba6d84abf62c108
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlidec 1.1.0 hb9d3cd8_3
  - libbrotlienc 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19390
  timestamp: 1749230137037
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/brotli-bin-1.1.0-h86ecc28_3.conda
  sha256: 0ccc233f83fdaef013b1dfa7b0501b7301abe1d5e38a0cac6eb3742d5ae46567
  md5: e06eec5d869ddde3abbb8c9784425106
  depends:
  - libbrotlidec 1.1.0 h86ecc28_3
  - libbrotlienc 1.1.0 h86ecc28_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19394
  timestamp: 1749230315332
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py313h46c70d0_3.conda
  sha256: e510ad1db7ea882505712e815ff02514490560fd74b5ec3a45a6c7cf438f754d
  md5: 2babfedd9588ad40c7113ddfe6a5ca82
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  license: MIT
  license_family: MIT
  size: 350295
  timestamp: 1749230225293
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/brotli-python-1.1.0-py313hb6a6212_3.conda
  sha256: cc0c79975844e7ca91ed69250d3b71fb8c5b259324c8d2c710292db1c1a6b06d
  md5: 191505c9c8e4cc9f959f640b75d6520b
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - libbrotlicommon 1.1.0 h86ecc28_3
  license: MIT
  license_family: MIT
  size: 357876
  timestamp: 1749230511796
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
  sha256: 2258b0b33e1cb3a9852d47557984abb6e7ea58e3d7f92706ec1f8e879290c4cb
  md5: 56398c28220513b9ea13d7b450acfb20
  depends:
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 189884
  timestamp: 1720974504976
- conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
  sha256: f8003bef369f57396593ccd03d08a8e21966157269426f71e943f96e4b579aeb
  md5: f7f0d6cc2dc986d42ac2689ec88192be
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 206884
  timestamp: 1744127994291
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/c-ares-1.34.5-h86ecc28_0.conda
  sha256: ccae98c665d86723993d4cb0b456bd23804af5b0645052c09a31c9634eebc8df
  md5: 5deaa903d46d62a1f8077ad359c3062e
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 215950
  timestamp: 1744127972012
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
  sha256: 7cfec9804c84844ea544d98bda1d9121672b66ff7149141b8415ca42dfcd44f6
  md5: 72525f07d72806e3b639ad4504c30ce5
  depends:
  - __unix
  license: ISC
  size: 151069
  timestamp: 1749990087500
- conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
  sha256: 3bd6a391ad60e471de76c0e9db34986c4b5058587fbf2efa5a7f54645e28c2c7
  md5: 09262e66b19567aff4f592fb53b28760
  depends:
  - __glibc >=2.17,<3.0.a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  size: 978114
  timestamp: 1741554591855
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cairo-1.18.4-h83712da_0.conda
  sha256: 37cfff940d2d02259afdab75eb2dbac42cf830adadee78d3733d160a1de2cc66
  md5: cd55953a67ec727db5dc32b167201aa6
  depends:
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  size: 966667
  timestamp: 1741554768968
- conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.6.15-pyhd8ed1ab_0.conda
  sha256: d71c85835813072cd6d7ce4b24be34215cd90c104785b15a5d58f4cd0cb50778
  md5: 781d068df0cc2407d4db0ecfbb29225b
  depends:
  - python >=3.9
  license: ISC
  size: 155377
  timestamp: 1749972291158
- conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py313hfab6e84_0.conda
  sha256: 73cd6199b143a8a6cbf733ce124ed57defc1b9a7eab9b10fd437448caf8eaa45
  md5: ce6386a5892ef686d6d680c345c40ad1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - pycparser
  - python >=3.13.0rc1,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: MIT
  license_family: MIT
  size: 295514
  timestamp: 1725560706794
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cffi-1.17.1-py313h2135053_0.conda
  sha256: 59842445337855185bee9d11a6624cf2fad651230496793f7b21d2243f7b4039
  md5: c5506b336622c8972eb38613f7937f26
  depends:
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - pycparser
  - python >=3.13.0rc1,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: MIT
  license_family: MIT
  size: 314846
  timestamp: 1725561791533
- conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
  sha256: 535ae5dcda8022e31c6dc063eb344c80804c537a5a04afba43a845fa6fa130f5
  md5: 40fe4284b8b5835a9073a645139f35af
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 50481
  timestamp: 1746214981991
- conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
  sha256: 8aee789c82d8fdd997840c952a586db63c6890b00e88c4fb6e80a38edd5f51c0
  md5: 94b550b8d3a614dbd326af798c7dfb40
  depends:
  - __unix
  - python >=3.10
  license: BSD-3-Clause
  license_family: BSD
  size: 87749
  timestamp: 1747811451319
- conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
  sha256: ab29d57dc70786c1269633ba3dff20288b81664d3ff8d21af995742e2bb03287
  md5: 962b9857ee8e7018c22f2776ffa0b2d7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 27011
  timestamp: 1733218222191
- conda: https://conda.anaconda.org/conda-forge/linux-64/contourpy-1.3.2-py313h33d0bda_0.conda
  sha256: 8e6e7c9644fa4841909f46b8136b6fad540c9c7b2688bfc15e8f9ce5eef0aabe
  md5: 5dc81fffe102f63045225007a33d6199
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.23
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 278576
  timestamp: 1744743243839
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/contourpy-1.3.2-py313h44a8f36_0.conda
  sha256: e945013b606bc745a2b887646de5007c83578d7f9e40eb16d15188c0d5616f94
  md5: 0106a8421d7f9c4ec2b981a1bd2558f5
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.23
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 287766
  timestamp: 1744743327442
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
  noarch: generic
  sha256: 058c8156ff880b1180a36b94307baad91f9130d0e3019ad8c7ade035852016fb
  md5: 0401f31e3c9e48cebf215472aa3e7104
  depends:
  - python >=3.13,<3.14.0a0
  - python_abi * *_cp313
  license: Python-2.0
  size: 47560
  timestamp: 1750062514868
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-crt-tools-12.9.86-ha770c72_1.conda
  sha256: 4475409f91176c0a77ead29e961617366ef1fbe932c7315abdd5699ad134f0be
  md5: ba98092d1090d5f5ddd2d7f827e7d3a5
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 28928
  timestamp: 1749226545023
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-crt-tools-12.9.86-h579c4fd_1.conda
  sha256: f332391eb9dbdc6928a9df063a737da7b5c6404a7da060a42081082674ae37dd
  md5: 7d995febb715af1f8227e57f7497600d
  depends:
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 28970
  timestamp: 1749226533921
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cudart-12.9.79-h5888daf_0.conda
  sha256: 57d1294ecfaf9dc8cdb5fc4be3e63ebc7614538bddb5de53cfd9b1b7de43aed5
  md5: cb15315d19b58bd9cd424084e58ad081
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-cudart_linux-64 12.9.79 h3f2d84a_0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 23242
  timestamp: 1749218416505
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-cudart-12.9.79-h3ae8b8a_0.conda
  sha256: 3d6699fc27ffabf28a9d359b48e7b88437e4d945844718a58608627998db5d1b
  md5: df78e19e5fe656631d1470aa0fcf6ced
  depends:
  - arm-variant * sbsa
  - cuda-cudart_linux-aarch64 12.9.79 h3ae8b8a_0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 23466
  timestamp: 1749218349235
- conda: https://conda.anaconda.org/conda-forge/noarch/cuda-cudart_linux-64-12.9.79-h3f2d84a_0.conda
  sha256: 6cde0ace2b995b49d0db2eefb7bc30bf00ffc06bb98ef7113632dec8f8907475
  md5: 64508631775fbbf9eca83c84b1df0cae
  depends:
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 197249
  timestamp: 1749218394213
- conda: https://conda.anaconda.org/conda-forge/noarch/cuda-cudart_linux-aarch64-12.9.79-h3ae8b8a_0.conda
  sha256: 4900ff2f000a4f8a70a7bc8576469640aa6590618fa9e73c84e066e025dcb760
  md5: cc2459ad427431e089d78d760cf24437
  depends:
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 212993
  timestamp: 1749218341193
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cuobjdump-12.9.82-hbd13f7d_0.conda
  sha256: a4f37cd8823d209639bdda1eea3ee0eb01040e44e2480c2f393e684c472c2f0c
  md5: 667a138d80047e7869f5330087772fd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-nvdisasm
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 243219
  timestamp: 1749223489014
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-cuobjdump-12.9.82-h05609ea_0.conda
  sha256: 4d69828b89d1e2d041c79b69127224db7e1ac3dbca13b52c423ce0898868e281
  md5: 1e28fd566d3906807e2f7ff2dce0bc5a
  depends:
  - cuda-nvdisasm
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 253159
  timestamp: 1749223535484
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-cupti-12.9.79-h9ab20c4_0.conda
  sha256: 55922005d1b31ba090455ab39d2e5a9b771fe503713d4b7699752a76aedccb2b
  md5: 229b3cc1f6b6b633923e1c9856ee0d80
  depends:
  - __glibc >=2.28,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 1842820
  timestamp: 1749218443367
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-cupti-12.9.79-hd55a8e4_0.conda
  sha256: fa11c62d28225ac1dc06f8449c9552c9e7c7f39c1e901de7dd161f20738e417d
  md5: 6de85fbeb617a6bd6ff96dd3a4e1fc70
  depends:
  - __glibc >=2.28,<3.0.a0
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 1602876
  timestamp: 1749218514620
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvcc-tools-12.9.86-he02047a_1.conda
  sha256: 7e5ab4ae67254c6d814007708a8183355684c81a917b383a7f042c25149737c3
  md5: a076f1ec812ce8fceacd538d6e672f37
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-crt-tools 12.9.86 ha770c72_1
  - cuda-nvvm-tools 12.9.86 he02047a_1
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=12
  - libstdcxx >=12
  constrains:
  - gcc_impl_linux-64 >=6,<15.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 27490340
  timestamp: 1749226666055
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvcc-tools-12.9.86-h614329b_1.conda
  sha256: 1c7b756dd5e7503ee878caaa0c4bbb23074b0f5f5ed9ce095b330d2481f0ed75
  md5: 477fcb03135205c1debd7e0094b9d0d1
  depends:
  - arm-variant * sbsa
  - cuda-crt-tools 12.9.86 h579c4fd_1
  - cuda-nvvm-tools 12.9.86 h614329b_1
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=12
  - libstdcxx >=12
  constrains:
  - gcc_impl_linux-aarch64 >=6,<15.0a0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 23865753
  timestamp: 1749226650472
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvdisasm-12.9.88-hbd13f7d_0.conda
  sha256: 6ef7c122897a9e27bc3aaed1745ea03bfecb5f553d420b0e4bf2ef6f568aab81
  md5: 7e9e4991e5890f32e8ef3c9a971171df
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 5517799
  timestamp: 1749221325784
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvdisasm-12.9.88-h5101a13_0.conda
  sha256: d1326f55234ddad63e816832038eff0e4f322d9e7bb7c2e442b54dc81deef7c1
  md5: a8db525ef1b0608287857621eb0ad69e
  depends:
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 5472392
  timestamp: 1749221407102
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvrtc-12.9.86-h5888daf_0.conda
  sha256: 4d339c411c23d40ff3a8671284e476a31b31273b1a4d29c680c01940a559bd95
  md5: 9c52e4389e54d4f5800b23512e479479
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 67183992
  timestamp: 1749221543691
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvrtc-12.9.86-h3ae8b8a_0.conda
  sha256: f1cce9fddb1824a0facba4a1025b1f17e280c3c0fdf1b26a21969ee31ae5f1a4
  md5: d6b08a8b067434bc96c015a5aee6b28a
  depends:
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 33318456
  timestamp: 1749221507330
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvtx-12.9.79-h5888daf_0.conda
  sha256: 8a09c380831215cd3c996bac59c5e3bd774648a2a19e4edfc99b283b65605844
  md5: 50e6a4a31fb588f158ab850b1d545747
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 29292
  timestamp: 1749221478549
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvtx-12.9.79-h3ae8b8a_0.conda
  sha256: 07c5e87848b0a2d36dbf2cfebfca4272aa62be75a32eafb8d4200cd897e3b683
  md5: 67dbf9bfb7c60f6c24f6eb30020e4c90
  depends:
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 30125
  timestamp: 1749221599582
- conda: https://conda.anaconda.org/conda-forge/linux-64/cuda-nvvm-tools-12.9.86-he02047a_1.conda
  sha256: 0958aee5a72f4be02c8f988539261cf549c9fcd6b61c6ce895bc6a13fe61f5d6
  md5: f716064b73c93d9aab74b5cc7f57985d
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=12
  - libstdcxx >=12
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 24248725
  timestamp: 1749226615764
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cuda-nvvm-tools-12.9.86-h614329b_1.conda
  sha256: d82666d96fc40a545965d56138767bcbd3d903676a3d6f685032ff5a4df53d56
  md5: 8c95694038f658140a2782655579b083
  depends:
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=12
  - libstdcxx >=12
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 24417088
  timestamp: 1749226609357
- conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-12.9-h4f385c5_3.conda
  sha256: 5f5f428031933f117ff9f7fcc650e6ea1b3fef5936cf84aa24af79167513b656
  md5: b6d5d7f1c171cbd228ea06b556cfa859
  constrains:
  - cudatoolkit 12.9|12.9.*
  - __cuda >=12
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 21578
  timestamp: 1746134436166
- conda: https://conda.anaconda.org/conda-forge/linux-64/cudnn-9.10.1.4-h7646684_0.conda
  sha256: 746cfa7c0e9b9eba3429465cf9a70786a63da2f4b2c322c33d74b5ff2db6d8ae
  md5: 5aa5b04b995ebe10fe44de6fe93b1850
  depends:
  - __glibc >=2.28,<3.0.a0
  - cuda-version >=12,<13.0a0
  - libcudnn-dev 9.10.1.4 hcd2ec93_0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - cudnn-jit <0a
  license: LicenseRef-cuDNN-Software-License-Agreement
  size: 19516
  timestamp: 1747774432049
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cudnn-9.10.1.4-h71bdb64_0.conda
  sha256: 3b37d4cae790b63c12bb66270c9df270f868f620831c5838e257a549cd275c8a
  md5: f3a1df20e35985c2b937189438eab87d
  depends:
  - __glibc >=2.28,<3.0.a0
  - arm-variant * sbsa
  - cuda-version >=12,<13.0a0
  - libcudnn-dev 9.10.1.4 he9157cb_0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - cudnn-jit <0a
  license: LicenseRef-cuDNN-Software-License-Agreement
  size: 19704
  timestamp: 1747774532803
- conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9827efa891e507a91a8a2acf64e210d2aff394e1cde432ad08e1f8c66b12293c
  md5: 44600c4667a319d67dbe0681fc0bc833
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 13399
  timestamp: 1733332563512
- conda: https://conda.anaconda.org/conda-forge/noarch/datasets-3.6.0-pyhd8ed1ab_0.conda
  sha256: 0218079382123518bfa9b461833996d5c3afb541c6e3ab9e41a24ec4bc5b15f6
  md5: 39b0719f7ade25bda275efe4b6973486
  depends:
  - aiohttp
  - dill >=0.3.0,<0.3.9
  - filelock
  - fsspec >=2023.1.0,<=2025.3.0
  - huggingface_hub >=0.24.0
  - multiprocess <0.70.17
  - numpy >=1.17
  - packaging
  - pandas
  - pyarrow >=15.0.0
  - python >=3.9
  - python-xxhash
  - pyyaml >=5.1
  - requests >=2.32.2
  - tqdm >=4.66.3
  license: Apache-2.0
  license_family: Apache
  size: 338869
  timestamp: 1746740579822
- conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
  sha256: 22053a5842ca8ee1cf8e1a817138cdb5e647eb2c46979f84153f6ad7bde73020
  md5: 418c6ca5929a611cbd69204907a83995
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 760229
  timestamp: 1685695754230
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dav1d-1.2.1-h31becfc_0.conda
  sha256: 33fe66d025cf5bac7745196d1a3dd7a437abcf2dbce66043e9745218169f7e17
  md5: 6e5a87182d66b2d1328a96b61ca43a62
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 347363
  timestamp: 1685696690003
- conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
  sha256: 3b988146a50e165f0fa4e839545c679af88e4782ec284cc7b6d07dd226d6a068
  md5: 679616eb5ad4e521c83da4650860aba7
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - libglib >=2.84.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 437860
  timestamp: 1747855126005
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dbus-1.16.2-heda779d_0.conda
  sha256: 5c9166bbbe1ea7d0685a1549aad4ea887b1eb3a07e752389f86b185ef8eac99a
  md5: 9203b74bb1f3fa0d6f308094b3b44c1e
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libglib >=2.84.2,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 469781
  timestamp: 1747855172617
- conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
  sha256: c17c6b9937c08ad63cb20a26f403a3234088e57d4455600974a0ce865cb14017
  md5: 9ce473d1d1be1cc3810856a48b3fab32
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  size: 14129
  timestamp: 1740385067843
- conda: https://conda.anaconda.org/conda-forge/noarch/dill-0.3.8-pyhd8ed1ab_0.conda
  sha256: 482b5b566ca559119b504c53df12b08f3962a5ef8e48061d62fd58a47f8f2ec4
  md5: 78745f157d56877a2c6e7b386f66f3e2
  depends:
  - python >=3.7
  license: BSD-3-Clause
  license_family: BSD
  size: 88169
  timestamp: 1706434833883
- conda: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.1.1-gpl_hceff1ee_706.conda
  sha256: 160f5c5a76eb50e8dc24d9cf7a10dabe472d8a814054bfa3d17c1d5ea9fe1441
  md5: 9ffc098a61a02142be92e942940813d9
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.14,<1.3.0a0
  - aom >=3.9.1,<3.10.0a0
  - bzip2 >=1.0.8,<2.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - gmp >=6.3.0,<7.0a0
  - harfbuzz >=11.0.1
  - lame >=3.100,<3.101.0a0
  - libass >=0.17.3,<0.17.4.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libopenvino >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-batch-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-hetero-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-intel-cpu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-intel-gpu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-intel-npu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-ir-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-onnx-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-paddle-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-pytorch-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-lite-frontend >=2025.0.0,<2025.0.1.0a0
  - libopus >=1.5.2,<2.0a0
  - librsvg >=2.58.4,<3.0a0
  - libstdcxx >=13
  - libva >=2.22.0,<3.0a0
  - libvorbis >=1.3.7,<1.4.0a0
  - libvpx >=1.14.1,<1.15.0a0
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openh264 >=2.6.0,<2.6.1.0a0
  - openssl >=3.5.0,<4.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - sdl2 >=2.32.54,<3.0a0
  - svt-av1 >=3.0.2,<3.0.3.0a0
  - x264 >=1!164.3095,<1!165
  - x265 >=3.5,<3.6.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  constrains:
  - __cuda  >=12.4
  license: GPL-2.0-or-later
  license_family: GPL
  size: 10413314
  timestamp: 1748704904696
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ffmpeg-7.1.1-gpl_h3bf3c16_706.conda
  sha256: 7813f928b50b4f1d6e91e9dad890390c89270a949ee3e1e4ba098cfdd29f203c
  md5: 5aff84a14f1cce8121c39007856ceef2
  depends:
  - alsa-lib >=1.2.14,<1.3.0a0
  - aom >=3.9.1,<3.10.0a0
  - bzip2 >=1.0.8,<2.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - gmp >=6.3.0,<7.0a0
  - harfbuzz >=11.0.1
  - lame >=3.100,<3.101.0a0
  - libass >=0.17.3,<0.17.4.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libopenvino >=2025.0.0,<2025.0.1.0a0
  - libopenvino-arm-cpu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-batch-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-hetero-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-ir-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-onnx-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-paddle-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-pytorch-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-lite-frontend >=2025.0.0,<2025.0.1.0a0
  - libopus >=1.5.2,<2.0a0
  - librsvg >=2.58.4,<3.0a0
  - libstdcxx >=13
  - libvorbis >=1.3.7,<1.4.0a0
  - libvpx >=1.14.1,<1.15.0a0
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openh264 >=2.6.0,<2.6.1.0a0
  - openssl >=3.5.0,<4.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - sdl2 >=2.32.54,<3.0a0
  - svt-av1 >=3.0.2,<3.0.3.0a0
  - x264 >=1!164.3095,<1!165
  - x265 >=3.5,<3.6.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  constrains:
  - __cuda  >=12.4
  license: GPL-2.0-or-later
  license_family: GPL
  size: 10010996
  timestamp: 1748705316426
- conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
  sha256: de7b6d4c4f865609ae88db6fa03c8b7544c2452a1aa5451eb7700aad16824570
  md5: 4547b39256e296bb758166893e909a7c
  depends:
  - python >=3.9
  license: Unlicense
  size: 17887
  timestamp: 1741969612334
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
  sha256: 58d7f40d2940dd0a8aa28651239adbf5613254df0f75789919c4e6762054403b
  md5: 0c96522c6bdaed4b1566d11387caaf45
  license: BSD-3-Clause
  license_family: BSD
  size: 397370
  timestamp: 1566932522327
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
  sha256: c52a29fdac682c20d252facc50f01e7c2e7ceac52aa9817aaf0bb83f7559ec5c
  md5: 34893075a5c9e55cdafac56607368fc6
  license: OFL-1.1
  license_family: Other
  size: 96530
  timestamp: 1620479909603
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
  sha256: 00925c8c055a2275614b4d983e1df637245e19058d79fc7dd1a93b8d9fb4b139
  md5: 4d59c254e01d9cde7957100457e2d5fb
  license: OFL-1.1
  license_family: Other
  size: 700814
  timestamp: 1620479612257
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
  sha256: 2821ec1dc454bd8b9a31d0ed22a7ce22422c0aef163c59f49dfdf915d0f0ca14
  md5: 49023d73832ef61042f6a237cb2687e7
  license: LicenseRef-Ubuntu-Font-Licence-Version-1.0
  license_family: Other
  size: 1620504
  timestamp: 1727511233259
- conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
  sha256: 7093aa19d6df5ccb6ca50329ef8510c6acb6b0d8001191909397368b65b02113
  md5: 8f5b0b297b59e1ac160ad4beec99dbee
  depends:
  - __glibc >=2.17,<3.0.a0
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 265599
  timestamp: 1730283881107
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fontconfig-2.15.0-h8dda3cd_1.conda
  sha256: fe023bb8917c8a3138af86ef537b70c8c5d60c44f93946a87d1e8bb1a6634b55
  md5: 112b71b6af28b47c624bcbeefeea685b
  depends:
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 277832
  timestamp: 1730284967179
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
  sha256: a997f2f1921bb9c9d76e6fa2f6b408b7fa549edd349a77639c9fe7a23ea93e61
  md5: fee5683a3f04bd15cbd8318b096a27ab
  depends:
  - fonts-conda-forge
  license: BSD-3-Clause
  license_family: BSD
  size: 3667
  timestamp: 1566974674465
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
  sha256: 53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38
  md5: f766549260d6815b0c52253f1fb1bb29
  depends:
  - font-ttf-dejavu-sans-mono
  - font-ttf-inconsolata
  - font-ttf-source-code-pro
  - font-ttf-ubuntu
  license: BSD-3-Clause
  license_family: BSD
  size: 4102
  timestamp: 1566932280397
- conda: https://conda.anaconda.org/conda-forge/linux-64/fonttools-4.58.4-py313h8060acc_0.conda
  sha256: 82948f4d402a6b4a6ccf48c43a650ac43ddccee7f78b75cc4849890542c0ad98
  md5: 1a5eb37c590d8adeb64145990f70c50b
  depends:
  - __glibc >=2.17,<3.0.a0
  - brotli
  - libgcc >=13
  - munkres
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: MIT
  license_family: MIT
  size: 2856292
  timestamp: 1749848501202
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fonttools-4.58.4-py313h857f82b_0.conda
  sha256: 6b23bd254821069ac7bbe8883480fe2cd989d89ab807985f5a499f6687d81b8b
  md5: a0799fb099cf4bb955c758d8f96e5c60
  depends:
  - brotli
  - libgcc >=13
  - munkres
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: MIT
  license_family: MIT
  size: 2818545
  timestamp: 1749848525079
- conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
  sha256: 7ef7d477c43c12a5b4cddcf048a83277414512d1116aba62ebadfa7056a7d84f
  md5: 9ccd736d31e0c6e41f54e704e5312811
  depends:
  - libfreetype 2.13.3 ha770c72_1
  - libfreetype6 2.13.3 h48d6fc4_1
  license: GPL-2.0-only OR FTL
  size: 172450
  timestamp: 1745369996765
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/freetype-2.13.3-h8af1aa0_1.conda
  sha256: 3b3ff45ac1fc880fbc8268477d29901a8fead32fb2241f98e4f2a1acffe6eea2
  md5: 71c4cbe1b384a8e7b56993394a435343
  depends:
  - libfreetype 2.13.3 h8af1aa0_1
  - libfreetype6 2.13.3 he93130f_1
  license: GPL-2.0-only OR FTL
  size: 172259
  timestamp: 1745370055170
- conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
  sha256: 5d7b6c0ee7743ba41399e9e05a58ccc1cfc903942e49ff6f677f6e423ea7a627
  md5: ac7bc6a654f8f41b352b38f4051135f8
  depends:
  - libgcc-ng >=7.5.0
  license: LGPL-2.1
  size: 114383
  timestamp: 1604416621168
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fribidi-1.0.10-hb9de7d4_0.tar.bz2
  sha256: bcb5a40f1aaf4ea8cda2fc6b2b12aa336403772121350281ce31fd2d9d3e214e
  md5: f6c91a43eace6fb926a8730b3b9a8a50
  depends:
  - libgcc-ng >=7.5.0
  license: LGPL-2.1
  size: 115689
  timestamp: 1604417149643
- conda: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.6.0-py313h61b7b33_0.conda
  sha256: 25cc87c67246e67aca945467f6cc53699f91d4947a88fc0eba1f23dd75f6d3b6
  md5: 5e028b3e9037cdba89b3914eba571bef
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 112428
  timestamp: 1746635568569
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/frozenlist-1.6.0-py313hf157169_0.conda
  sha256: fda2232354810af5e2fc77e0f7d31efe9b1c7bce305adc5a1bc86138ef7cecb2
  md5: 269d92453f2e69c27afb682e9ae51d9d
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 112897
  timestamp: 1746635619172
- conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.3.0-pyhd8ed1ab_0.conda
  sha256: 9cbba3b36d1e91e4806ba15141936872d44d20a4d1e3bb74f4aea0ebeb01b205
  md5: 5ecafd654e33d1f2ecac5ec97057593b
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 141329
  timestamp: 1741404114588
- conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
  sha256: d5283b95a8d49dcd88d29b360d8b38694aaa905d968d156d72ab71d32b38facb
  md5: 201db6c2d9a3c5e46573ac4cb2e92f4f
  depends:
  - libgcc-ng >=12
  - libglib >=2.80.2,<3.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libtiff >=4.6.0,<4.8.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 528149
  timestamp: 1715782983957
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gdk-pixbuf-2.42.12-ha61d561_0.conda
  sha256: 608f64aa9cf3085e91da8d417aa7680715130b4da73d8aabc50b19e29de697d2
  md5: 332ed304e6d1c1333ccbdc0fdd722fe9
  depends:
  - libgcc-ng >=12
  - libglib >=2.80.2,<3.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libtiff >=4.6.0,<4.8.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 536613
  timestamp: 1715784386033
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
  sha256: 88db27c666e1f8515174bf622a3e2ad983c94d69e3a23925089e476b9b06ad00
  md5: c63e7590d4d6f4c85721040ed8b12888
  depends:
  - __glibc >=2.17,<3.0.a0
  - gettext-tools 0.24.1 h5888daf_0
  - libasprintf 0.24.1 h8e693c7_0
  - libasprintf-devel 0.24.1 h8e693c7_0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  - libgettextpo-devel 0.24.1 h5888daf_0
  - libstdcxx >=13
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  size: 511988
  timestamp: 1746228987123
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-0.24.1-h5ad3122_0.conda
  sha256: 027499618b9e9c8306490c4af11a329059642ba16086c8a4c3c2596652770a6f
  md5: 7c2185682ee955c32d65f35822ccc06f
  depends:
  - gettext-tools 0.24.1 h5ad3122_0
  - libasprintf 0.24.1 h5e0f5ae_0
  - libasprintf-devel 0.24.1 h5e0f5ae_0
  - libgcc >=13
  - libgettextpo 0.24.1 h5ad3122_0
  - libgettextpo-devel 0.24.1 h5ad3122_0
  - libstdcxx >=13
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  size: 509273
  timestamp: 1746228828727
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
  sha256: 3ba33868630b903e3cda7a9176363cdf02710fb8f961efed5f8200c4d53fb4e3
  md5: d54305672f0361c2f3886750e7165b5f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 3129801
  timestamp: 1746228937647
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-tools-0.24.1-h5ad3122_0.conda
  sha256: 4176d66aa44d070db71f7e80145d2b423faeef702473469bb82cd894dd589c66
  md5: 66459c6f8b54cea68654198f03d33e7d
  depends:
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 3530002
  timestamp: 1746228795208
- conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-h5888daf_1005.conda
  sha256: 6c33bf0c4d8f418546ba9c250db4e4221040936aef8956353bc764d4877bc39a
  md5: d411fc29e338efb48c5fd4576d71d881
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 119654
  timestamp: 1726600001928
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gflags-2.2.2-h5ad3122_1005.conda
  sha256: 28fe6b40b20454106d5e4ef6947cf298c13cc72a46347bbc49b563cd3a463bfa
  md5: 4ff634d515abbf664774b5e1168a9744
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 106638
  timestamp: 1726599967617
- conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.1-hbabe93e_0.conda
  sha256: dc824dc1d0aa358e28da2ecbbb9f03d932d976c8dca11214aa1dcdfcbd054ba2
  md5: ff862eebdfeb2fd048ae9dc92510baca
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 143452
  timestamp: 1718284177264
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/glog-0.7.1-h468a4a4_0.conda
  sha256: 920795d4f775a9f47e91c2223e64847f0b212b3fedc56c137c5889e32efe8ba0
  md5: 08940a32c6ced3703d1412dd37df4f62
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 145811
  timestamp: 1718284208668
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
  sha256: 309cf4f04fec0c31b6771a5809a1909b4b3154a2208f52351e1ada006f4c750c
  md5: c94a5994ef49749880a8139cf9afcbe1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  size: 460055
  timestamp: 1718980856608
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gmp-6.3.0-h0a1ffab_2.conda
  sha256: a5e341cbf797c65d2477b27d99091393edbaa5178c7d69b7463bb105b0488e69
  md5: 7cbfb3a8bb1b78a7f5518654ac6725ad
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  size: 417323
  timestamp: 1718980707330
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.2.1-py313h11186cd_0.conda
  sha256: 1f66faf02d062348148afb7eb86fa5baf011afd5e826884e20c378e79a0d6174
  md5: 54d020e0eaacf1e99bfb2410b9aa2e5e
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpc >=1.3.1,<2.0a0
  - mpfr >=4.2.1,<5.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: LGPL-3.0-or-later
  license_family: LGPL
  size: 213289
  timestamp: 1745509587714
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gmpy2-2.2.1-py313h0c041f1_0.conda
  sha256: 8020336cb5024afb332255171ea0530dfaa57f3d1969c686f7dd69ba9adbba0c
  md5: 2af39b59f7645db95bccf99faae09951
  depends:
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpc >=1.3.1,<2.0a0
  - mpfr >=4.2.1,<5.0a0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: LGPL-3.0-or-later
  license_family: LGPL
  size: 204551
  timestamp: 1745509582834
- conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
  sha256: cac69f3ff7756912bbed4c28363de94f545856b35033c0b86193366b95f5317d
  md5: 951ff8d9e5536896408e89d63230b8d5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 98419
  timestamp: 1750079957535
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/graphite2-1.3.14-h5ad3122_0.conda
  sha256: 957d9bcf7f8b2d8925a9af238189b372ba42c0fdbda4248cd8bd76684781af3d
  md5: 087ecf989fc23fc50944a06fddf5f3bc
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 101397
  timestamp: 1750080039341
- conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
  sha256: 0aa1cdc67a9fe75ea95b5644b734a756200d6ec9d0dff66530aec3d1c1e9df75
  md5: b4754fb1bdcb70c8fd54f918301582c6
  depends:
  - hpack >=4.1,<5
  - hyperframe >=6.1,<7
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 53888
  timestamp: 1738578623567
- conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
  sha256: 5bd0f3674808862838d6e2efc0b3075e561c34309c5c2f4c976f7f1f57c91112
  md5: 0e6e192d4b3d95708ad192d957cf3163
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - freetype
  - graphite2
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libglib >=2.84.1,<3.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 1730226
  timestamp: 1747091044218
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/harfbuzz-11.2.1-h405b6a2_0.conda
  sha256: 2f7754c197fc1b7e57cf5b4063298834818889561c0c462b7fe363742defdbd5
  md5: b55680fc90e9747dc858e7ceb0abc2b2
  depends:
  - cairo >=1.18.4,<2.0a0
  - freetype
  - graphite2
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libglib >=2.84.1,<3.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 1746366
  timestamp: 1747094097917
- conda: https://conda.anaconda.org/conda-forge/linux-64/hf-xet-1.1.5-py39h260a9e5_3.conda
  noarch: python
  sha256: b28905ff975bd935cd113ee97b7eb5b5e3b0969a21302135c6ae096aa06a61f6
  md5: 7b6007f4ad18a970ca3a977148cf47de
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  - _python_abi3_support 1.*
  - cpython >=3.9
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 2537615
  timestamp: 1750541218448
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/hf-xet-1.1.5-py39h076f640_3.conda
  noarch: python
  sha256: afacd39a1b47538fb0caa40515fae551805b54103c177480ddb3e400a042f75c
  md5: 9698d5d696833cdeba8c76c1b1b0813d
  depends:
  - python
  - libgcc >=13
  - _python_abi3_support 1.*
  - cpython >=3.9
  - openssl >=3.5.0,<4.0a0
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 2478451
  timestamp: 1750541462321
- conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
  sha256: 6ad78a180576c706aabeb5b4c8ceb97c0cb25f1e112d76495bff23e3779948ba
  md5: 0a802cb9888dd14eeefc611f05c40b6e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 30731
  timestamp: 1737618390337
- conda: https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.33.1-pyhd8ed1ab_0.conda
  sha256: bdbfb0a2aa957fc2a79dc342022529def69162825d6420f03b2dcfaab92765a2
  md5: 4a634f9e9ad0e28ecd4da031a4616d03
  depends:
  - filelock
  - fsspec >=2023.5.0
  - hf-xet >=1.1.2,<2.0.0
  - packaging >=20.9
  - python >=3.9
  - pyyaml >=5.1
  - requests
  - tqdm >=4.42.1
  - typing-extensions >=3.7.4.3
  - typing_extensions >=3.7.4.3
  license: Apache-2.0
  size: 317782
  timestamp: 1750865913736
- conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
  sha256: 77af6f5fe8b62ca07d09ac60127a30d9069fdc3c68d6b256754d0ffb1f7779f8
  md5: 8e6923fc12f1fe8f8c4e5c9f343256ac
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 17397
  timestamp: 1737618427549
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12129203
  timestamp: 1720853576813
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
  sha256: 813298f2e54ef087dbfc9cc2e56e08ded41de65cff34c639cc8ba4e27e4540c9
  md5: 268203e8b983fddb6412b36f2024e75c
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12282786
  timestamp: 1720853454991
- conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
  sha256: d7a472c9fd479e2e8dcb83fb8d433fce971ea369d704ece380e876f9c3494e87
  md5: 39a4f67be3286c86d696df570b1201b7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 49765
  timestamp: 1733211921194
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
  sha256: c18ab120a0613ada4391b15981d86ff777b5690ca461ea7e9e49531e8f374745
  md5: 63ccfdc3a3ce25b027b8767eb722fca8
  depends:
  - python >=3.9
  - zipp >=3.20
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 34641
  timestamp: 1747934053147
- conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
  sha256: f1ac18b11637ddadc05642e8185a851c7fab5998c6f5470d716812fae943b2af
  md5: 446bd6c8cb26050d528881df495ce646
  depends:
  - markupsafe >=2.0
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 112714
  timestamp: 1741263433881
- conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
  sha256: e5a4eca9a5d8adfaa3d51e24eefd1a6d560cb3b33a7e1eee13e410bec457b7ed
  md5: fb1c14694de51a476ce8636d92b6f42c
  depends:
  - python >=3.9
  - setuptools
  license: BSD-3-Clause
  license_family: BSD
  size: 224437
  timestamp: 1748019237972
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
  sha256: 19d8bd5bb2fde910ec59e081eeb59529491995ce0d653a5209366611023a0b3a
  md5: 4ebae00eae9705b0c3d6d1018a81d047
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-dateutil >=2.8.2
  - pyzmq >=23.0
  - tornado >=6.2
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 106342
  timestamp: 1733441040958
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
  sha256: 56a7a7e907f15cca8c4f9b0c99488276d4cb10821d2d15df9245662184872e81
  md5: b7d89d860ebcda28a5303526cdee68ab
  depends:
  - __unix
  - platformdirs >=2.5
  - python >=3.8
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 59562
  timestamp: 1748333186063
- conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 117831
  timestamp: 1646151697040
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/keyutils-1.6.1-h4e544f5_0.tar.bz2
  sha256: 6d4233d97a9b38acbb26e1268bcf8c10a8e79c2aed7e5a385ec3769967e3e65b
  md5: 1f24853e59c68892452ef94ddd8afd4b
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 112327
  timestamp: 1646166857935
- conda: https://conda.anaconda.org/conda-forge/linux-64/kiwisolver-1.4.7-py313h33d0bda_0.conda
  sha256: 3e742fc388a4e8124f4b626e85e448786f368e5fce460a00733b849c7314bb20
  md5: 9862d13a5e466273d5a4738cffcb8d6c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13.0rc1,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 70982
  timestamp: 1725459393722
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/kiwisolver-1.4.7-py313h1d91839_0.conda
  sha256: 18d273839129edb7a52a3f18669afa79f60cf3bff1a613655f6376a4c4e75c36
  md5: 454c4884323753845b978b7129ec8ffc
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13.0rc1,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 70371
  timestamp: 1725461439548
- conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
  sha256: 99df692f7a8a5c27cd14b5fb1374ee55e756631b9c3d659ed3ee60830249b238
  md5: 3f43953b7d3fb3aaa1d0d0723d91e368
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1370023
  timestamp: 1719463201255
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/krb5-1.21.3-h50a48e9_0.conda
  sha256: 0ec272afcf7ea7fbf007e07a3b4678384b7da4047348107b2ae02630a570a815
  md5: 29c10432a2ca1472b53f299ffb2ffa37
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1474620
  timestamp: 1719463205834
- conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
  sha256: aad2a703b9d7b038c0f745b853c6bb5f122988fe1a7a096e0e606d9cbec4eaab
  md5: a8832b479f93521a9e7b5b743803be51
  depends:
  - libgcc-ng >=12
  license: LGPL-2.0-only
  license_family: LGPL
  size: 508258
  timestamp: 1664996250081
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lame-3.100-h4e544f5_1003.tar.bz2
  sha256: 2502904a42df6d94bd743f7b73915415391dd6d31d5f50cb57c0a54a108e7b0a
  md5: ab05bcf82d8509b4243f07e93bada144
  depends:
  - libgcc-ng >=12
  license: LGPL-2.0-only
  license_family: LGPL
  size: 604863
  timestamp: 1664997611416
- conda: https://conda.anaconda.org/conda-forge/noarch/lazy-loader-0.4-pyhd8ed1ab_2.conda
  sha256: d7ea986507090fff801604867ef8e79c8fda8ec21314ba27c032ab18df9c3411
  md5: d10d9393680734a8febc4b362a4c94f2
  depends:
  - importlib-metadata
  - packaging
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 16298
  timestamp: 1733636905835
- conda: https://conda.anaconda.org/conda-forge/noarch/lazy_loader-0.4-pyhd8ed1ab_2.conda
  sha256: e26803188a54cd90df9ce1983af70b287c4918c0fd178a9aabd9f1580f657a2b
  md5: bb0230917e2473c77d615104dbe8a49d
  depends:
  - lazy-loader 0.4 pyhd8ed1ab_2
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 6661
  timestamp: 1733636912265
- conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
  sha256: d6a61830a354da022eae93fa896d0991385a875c6bba53c82263a289deda9db8
  md5: 000e85703f0fd9594c81710dd5066471
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  size: 248046
  timestamp: 1739160907615
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lcms2-2.17-hc88f144_0.conda
  sha256: 47cf6a4780dc41caa9bc95f020eed485b07010c9ccc85e9ef44b538fedb5341d
  md5: b87b1abd2542cf65a00ad2e2461a3083
  depends:
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  size: 287007
  timestamp: 1739161069194
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
  sha256: dcd2b1a065bbf5c54004ddf6551c775a8eb6993c8298ca8a6b92041ed413f785
  md5: 6dc9e1305e7d3129af4ad0dabda30e56
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  size: 670635
  timestamp: 1749858327854
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.43-h5e2c951_5.conda
  sha256: 0cb7f3a3281e86c850ea09e0dbd0c3652eaee367aa089170d7f6bec07ff9fc07
  md5: e62696c21a84af63cfc49f4b5428a36a
  constrains:
  - binutils_impl_linux-aarch64 2.43
  license: GPL-3.0-only
  license_family: GPL
  size: 699857
  timestamp: 1749858448061
- conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
  sha256: 412381a43d5ff9bbed82cd52a0bbca5b90623f62e41007c9c42d3870c60945ff
  md5: 9344155d33912347b37f0ae6c410a835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 264243
  timestamp: 1745264221534
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lerc-4.0.0-hfdc4d58_1.conda
  sha256: f01df5bbf97783fac9b89be602b4d02f94353f5221acfd80c424ec1c9a8d276c
  md5: 60dceb7e876f4d74a9cbd42bbbc6b9cf
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 227184
  timestamp: 1745265544057
- conda: https://conda.anaconda.org/conda-forge/linux-64/level-zero-1.23.0-h84d6215_0.conda
  sha256: 143e01d63c103baf6cd27372736c90c75d78814d24105adcfb34900abd2bcdd5
  md5: 917379a89f84d15d3e871909553c2320
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 598789
  timestamp: 1750920203501
- conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
  sha256: 65d5ca837c3ee67b9d769125c21dc857194d7f6181bb0e7bd98ae58597b457d0
  md5: 00290e549c5c8a32cc271020acc9ec6b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - abseil-cpp =20250127.1
  - libabseil-static =20250127.1=cxx17*
  license: Apache-2.0
  license_family: Apache
  size: 1325007
  timestamp: 1742369558286
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libabseil-20250127.1-cxx17_h18dbdb1_0.conda
  sha256: 55b7f9d8faa4a0a08f9fc7bcbd7f4cdd3c232120bafa2e8f7e19014ea4aa1278
  md5: 71b972e18b2747a9d47bbbafc346b765
  depends:
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - libabseil-static =20250127.1=cxx17*
  - abseil-cpp =20250127.1
  license: Apache-2.0
  license_family: Apache
  size: 1348653
  timestamp: 1742369595937
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-20.0.0-h019e7cd_8_cuda.conda
  build_number: 8
  sha256: 52eada3c2c4b8dba96ff41e6610f66f6c4fe437107623ebe52fdb696df3da4ce
  md5: f2cfbe6e135eec1e658b01c875f41520
  depends:
  - __glibc >=2.17,<3.0.a0
  - aws-crt-cpp >=0.32.10,<0.32.11.0a0
  - aws-sdk-cpp >=1.11.510,<1.11.511.0a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-identity-cpp >=1.10.0,<1.10.1.0a0
  - azure-storage-blobs-cpp >=12.13.0,<12.13.1.0a0
  - azure-storage-files-datalake-cpp >=12.12.0,<12.12.1.0a0
  - bzip2 >=1.0.8,<2.0a0
  - glog >=0.7.1,<0.8.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libbrotlidec >=1.1.0,<1.2.0a0
  - libbrotlienc >=1.1.0,<1.2.0a0
  - libgcc
  - libgcc-ng >=12
  - libgoogle-cloud >=2.36.0,<2.37.0a0
  - libgoogle-cloud-storage >=2.36.0,<2.37.0a0
  - libopentelemetry-cpp >=1.21.0,<1.22.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libre2-11 >=2024.7.2
  - libstdcxx
  - libstdcxx-ng >=12
  - libutf8proc >=2.10.0,<2.11.0a0
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - orc >=2.1.2,<2.1.3.0a0
  - re2
  - snappy >=1.2.1,<1.3.0a0
  - zstd >=1.5.7,<1.6.0a0
  constrains:
  - apache-arrow-proc =*=cuda
  - arrow-cpp <0.0a0
  - parquet-cpp <0.0a0
  license: Apache-2.0
  size: 8969353
  timestamp: 1750865838916
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libarrow-20.0.0-h82abc5a_8_cpu.conda
  build_number: 8
  sha256: 6aafbbe1ee008a2bee2531f1eca0761314fe031af2f09c63b7ba991a370c6f36
  md5: db2f913551a0a9df2270c32d3d55fc84
  depends:
  - aws-crt-cpp >=0.32.10,<0.32.11.0a0
  - aws-sdk-cpp >=1.11.510,<1.11.511.0a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-identity-cpp >=1.10.0,<1.10.1.0a0
  - azure-storage-blobs-cpp >=12.13.0,<12.13.1.0a0
  - azure-storage-files-datalake-cpp >=12.12.0,<12.12.1.0a0
  - bzip2 >=1.0.8,<2.0a0
  - glog >=0.7.1,<0.8.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libbrotlidec >=1.1.0,<1.2.0a0
  - libbrotlienc >=1.1.0,<1.2.0a0
  - libgcc >=13
  - libgoogle-cloud >=2.36.0,<2.37.0a0
  - libgoogle-cloud-storage >=2.36.0,<2.37.0a0
  - libopentelemetry-cpp >=1.21.0,<1.22.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libre2-11 >=2024.7.2
  - libstdcxx >=13
  - libutf8proc >=2.10.0,<2.11.0a0
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - orc >=2.1.2,<2.1.3.0a0
  - re2
  - snappy >=1.2.1,<1.3.0a0
  - zstd >=1.5.7,<1.6.0a0
  constrains:
  - apache-arrow-proc =*=cpu
  - parquet-cpp <0.0a0
  - arrow-cpp <0.0a0
  license: Apache-2.0
  size: 8405658
  timestamp: 1750865703374
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-20.0.0-hb826db4_8_cuda.conda
  build_number: 8
  sha256: 1738851640d3b63ccd45e5a77348a91f0b0de9939cb154bcbb4aec6d7d490df2
  md5: b7f0d9d6c6cbad5adcdfb6b00257901e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0 h019e7cd_8_cuda
  - libgcc
  - libgcc-ng >=12
  - libstdcxx
  - libstdcxx-ng >=12
  license: Apache-2.0
  size: 627008
  timestamp: 1750865911748
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libarrow-acero-20.0.0-h3b568fd_8_cpu.conda
  build_number: 8
  sha256: 06bc922564528ed008ba7cabc06124c33a4ec85c7efe9fbd072fd6356735de15
  md5: 6424931cf426378e8edc314bbf29b38f
  depends:
  - libarrow 20.0.0 h82abc5a_8_cpu
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  size: 605875
  timestamp: 1750865755003
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-20.0.0-hb826db4_8_cuda.conda
  build_number: 8
  sha256: 4a3cb4b8220f219d4bdc1ad9a270938d814df57b2e8fba925e0542a9304a27ce
  md5: 9948bbe038b9409bd0ed077ea920c261
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0 h019e7cd_8_cuda
  - libarrow-acero 20.0.0 hb826db4_8_cuda
  - libgcc
  - libgcc-ng >=12
  - libparquet 20.0.0 h3f30f2e_8_cuda
  - libstdcxx
  - libstdcxx-ng >=12
  license: Apache-2.0
  size: 603255
  timestamp: 1750865973612
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libarrow-dataset-20.0.0-h3b568fd_8_cpu.conda
  build_number: 8
  sha256: 46ff00a117013ec98b535564ec4f3abfc125b2fea09ae9b856e30011868a4f84
  md5: ec52a17051053347ebc37abaff174924
  depends:
  - libarrow 20.0.0 h82abc5a_8_cpu
  - libarrow-acero 20.0.0 h3b568fd_8_cpu
  - libgcc >=13
  - libparquet 20.0.0 hfc78867_8_cpu
  - libstdcxx >=13
  license: Apache-2.0
  size: 582818
  timestamp: 1750865819131
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-20.0.0-h69308b4_8_cuda.conda
  build_number: 8
  sha256: 5a31a8c1d6be22911f975456ebd607ebbe4313f32b24b1ec7315cf6c2fc13f1c
  md5: 1985d19932063a00ac4d9de2fc111ca4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libarrow 20.0.0 h019e7cd_8_cuda
  - libarrow-acero 20.0.0 hb826db4_8_cuda
  - libarrow-dataset 20.0.0 hb826db4_8_cuda
  - libgcc
  - libgcc-ng >=12
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx
  - libstdcxx-ng >=12
  license: Apache-2.0
  size: 503113
  timestamp: 1750866015240
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libarrow-substrait-20.0.0-hcd5cee9_8_cpu.conda
  build_number: 8
  sha256: 3936ca4fd786fff4017eb38e43fc26a586c30606ec4817c5a9e55f1d979278d8
  md5: 13fb3d0873083738872cd5afbcc4e8f1
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libarrow 20.0.0 h82abc5a_8_cpu
  - libarrow-acero 20.0.0 h3b568fd_8_cpu
  - libarrow-dataset 20.0.0 h3b568fd_8_cpu
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  license: Apache-2.0
  size: 516406
  timestamp: 1750865865306
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
  sha256: e30733a729eb6efd9cb316db0202897c882d46f6c20a0e647b4de8ec921b7218
  md5: 57566a81dd1e5aa3d98ac7582e8bfe03
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-or-later
  size: 53115
  timestamp: 1746228856865
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-0.24.1-h5e0f5ae_0.conda
  sha256: 969b002ab70bf2f27fc108c83c07d6e4f4a4fd1a1ad3a8028e625a78a748347b
  md5: e6dd5b99796423a24ecc42db08ab31da
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-or-later
  size: 53530
  timestamp: 1746228742482
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
  sha256: ccbfc465456133042eea3e8d69bae009893f57a47a786f772c0af382bda7ad99
  md5: 8f66ed2e34507b7ae44afa31c3e4ec79
  depends:
  - __glibc >=2.17,<3.0.a0
  - libasprintf 0.24.1 h8e693c7_0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 34680
  timestamp: 1746228884730
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-devel-0.24.1-h5e0f5ae_0.conda
  sha256: caec98b6fc50630741e80ed39c06421be84222c6da629e12248fbddccf54dd5e
  md5: b02fed476ae02f9bbd5ab534dbb57587
  depends:
  - libasprintf 0.24.1 h5e0f5ae_0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 34897
  timestamp: 1746228758454
- conda: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.3-h52826cd_2.conda
  sha256: 8a94e634de73be1e7548deaf6e3b992e0d30c628a24f23333af06ebb3a3e74cb
  md5: 01de25a48490709850221135890e09eb
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libzlib >=1.3.1,<2.0a0
  - libiconv >=1.18,<2.0a0
  - fribidi >=1.0.10,<2.0a0
  - freetype >=2.13.3,<3.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - harfbuzz >=11.0.0,<12.0a0
  license: ISC
  size: 152563
  timestamp: 1743206970222
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libass-0.17.3-h3c9f632_2.conda
  sha256: 72551f77103bd9725cc57a1e6dff71059970ccc76c48c45240cdfd1987dfebd8
  md5: e7714c1e8fdaf41d5125dd73b28667bc
  depends:
  - libgcc >=13
  - freetype >=2.13.3,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libzlib >=1.3.1,<2.0a0
  - libiconv >=1.18,<2.0a0
  - fribidi >=1.0.10,<2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  license: ISC
  size: 173682
  timestamp: 1743206972213
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_hfdb39a5_mkl.conda
  build_number: 32
  sha256: 7a04219d42b3b0b85ed9d019f481e4227efa2baa12ff48547758e90e2e208adc
  md5: eceb19ae9105bc4d0e8d5a321d66c426
  depends:
  - mkl >=2024.2.2,<2025.0a0
  constrains:
  - liblapack  3.9.0   32*_mkl
  - blas 2.132   mkl
  - liblapacke 3.9.0   32*_mkl
  - libcblas   3.9.0   32*_mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  size: 17657
  timestamp: 1750388671003
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libblas-3.9.0-32_h1a9f1db_openblas.conda
  build_number: 32
  sha256: a257f0c43dd142be7eab85bf78999a869a6938ddb2415202f74724ff51dff316
  md5: 833718ed1c0b597ce17e5f410bd9b017
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - libcblas   3.9.0   32*_openblas
  - liblapack  3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  - mkl <2025
  license: BSD-3-Clause
  license_family: BSD
  size: 17341
  timestamp: 1750388911474
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_3.conda
  sha256: 462a8ed6a7bb9c5af829ec4b90aab322f8bcd9d8987f793e6986ea873bbd05cf
  md5: cb98af5db26e3f482bebb80ce9d947d3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 69233
  timestamp: 1749230099545
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libbrotlicommon-1.1.0-h86ecc28_3.conda
  sha256: a974f63f71ccb198300c606204846a65a7d62abffcbfbc4f557f71d0243a1fab
  md5: 76295055ce278970227759bdf3490827
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 69590
  timestamp: 1749230272157
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_3.conda
  sha256: 3eb27c1a589cbfd83731be7c3f19d6d679c7a444c3ba19db6ad8bf49172f3d83
  md5: 1c6eecffad553bde44c5238770cfb7da
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 33148
  timestamp: 1749230111397
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libbrotlidec-1.1.0-h86ecc28_3.conda
  sha256: a9664ec3acc9dbb33425d057154f6802b0c4d723fbb7939ee40eb379dbe5150b
  md5: 3a4b4fc0864a4dc0f4012ac1abe069a9
  depends:
  - libbrotlicommon 1.1.0 h86ecc28_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 32248
  timestamp: 1749230286642
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_3.conda
  sha256: 76e8492b0b0a0d222bfd6081cae30612aa9915e4309396fdca936528ccf314b7
  md5: 3facafe58f3858eb95527c7d3a3fc578
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 282657
  timestamp: 1749230124839
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libbrotlienc-1.1.0-h86ecc28_3.conda
  sha256: 3a225e42ef7293217177ba9ca8559915f14b74ab238652c7aa32f20a3dbbee2d
  md5: 2b8199de1016a56c49bfced37c7f0882
  depends:
  - libbrotlicommon 1.1.0 h86ecc28_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 290695
  timestamp: 1749230300899
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
  sha256: 9c84448305e7c9cc44ccec7757cf5afcb5a021f4579aa750a1fa6ea398783950
  md5: c44c16d6976d2aebbd65894d7741e67e
  depends:
  - __glibc >=2.17,<3.0.a0
  - attr >=2.5.1,<2.6.0a0
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 120375
  timestamp: 1741176638215
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcap-2.75-h51d75a7_0.conda
  sha256: d77e8bd8d5714a80c1fa88037e71d5c29f21bae1e9281528006c9c5a6175ac1a
  md5: c5456e13665779bf7a62dc7724ca2938
  depends:
  - attr >=2.5.1,<2.6.0a0
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 108212
  timestamp: 1741177682469
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_h372d94f_mkl.conda
  build_number: 32
  sha256: d0449cdfb6c6e993408375bcabbb4c9630a9b8750c406455ce3a4865ec7a321c
  md5: 68b55daaf083682f58d9b7f5d52aeb37
  depends:
  - libblas 3.9.0 32_hfdb39a5_mkl
  constrains:
  - liblapack  3.9.0   32*_mkl
  - liblapacke 3.9.0   32*_mkl
  - blas 2.132   mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  size: 17280
  timestamp: 1750388682101
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcblas-3.9.0-32_hab92f65_openblas.conda
  build_number: 32
  sha256: e902de3cd34d4fc1f7d15b9c9c1d297d90043d5283d28db410d32e45ea4e1a33
  md5: 2f02a3ea0960118a0a8d45cdd348b039
  depends:
  - libblas 3.9.0 32_h1a9f1db_openblas
  constrains:
  - liblapack  3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17335
  timestamp: 1750388919351
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
  sha256: fd1d153962764433fe6233f34a72cdeed5dcf8a883a85769e8295ce940b5b0c5
  md5: c965a5aa0d5c1c37ffc62dff36e28400
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 20440
  timestamp: 1633683576494
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcrc32c-1.1.2-h01db608_0.tar.bz2
  sha256: b8b8c57a87da86b3ea24280fd6aa8efaf92f4e684b606bf2db5d3cb06ffbe2ea
  md5: 268ee639c17ada0002fb04dd21816cc2
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 18669
  timestamp: 1633683724891
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcublas-12.9.1.4-h9ab20c4_0.conda
  sha256: 38bc99de89687ec391750dc603203364bdedfb92c600dcb2916dd3cd8558f5f5
  md5: 605f995d88cdb64714bd9979aadc7cd4
  depends:
  - __glibc >=2.28,<3.0.a0
  - cuda-nvrtc
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 467680700
  timestamp: 1749227622432
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcublas-12.9.1.4-hd55a8e4_0.conda
  sha256: 8cf2b97177f7721df4b7e5ec8bd1afc2200d22d6fb193883ca3675a7184987f2
  md5: cd310792375ba4c0bca41ac6bcf10369
  depends:
  - __glibc >=2.28,<3.0.a0
  - arm-variant * sbsa
  - cuda-nvrtc
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 467869780
  timestamp: 1749227692081
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcudnn-9.10.1.4-h4840ae0_0.conda
  sha256: 5f21148b7bdfbcf5e40b4debaccd6d36b8a75405fdef1c66d75059a12d43bd0e
  md5: c19f7281266ca77da5458d2ccf17ba82
  depends:
  - __glibc >=2.28,<3.0.a0
  - cuda-nvrtc
  - cuda-version >=12,<13.0a0
  - libcublas
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - libcudnn-jit <0a
  license: LicenseRef-cuDNN-Software-License-Agreement
  size: 527020675
  timestamp: 1747773945760
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcudnn-9.10.1.4-hb67e6b9_0.conda
  sha256: 7009a6dacd59b07b665e7fa71c05ac4ce321f80806654cbac8efa787380b706b
  md5: be06c0e4fe5ca458ea20a2779a974e9c
  depends:
  - __glibc >=2.28,<3.0.a0
  - arm-variant * sbsa
  - cuda-nvrtc
  - cuda-version >=12,<13.0a0
  - libcublas
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - libcudnn-jit <0a
  license: LicenseRef-cuDNN-Software-License-Agreement
  size: 527052622
  timestamp: 1747773984135
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcudnn-dev-9.10.1.4-hcd2ec93_0.conda
  sha256: 34fb3c9fa9b67a18fd0b4d28518fdacf11dbed3ad3fbf24aec341d1b8490d3c0
  md5: bce8ec010b35f2c1e5db441f3f396754
  depends:
  - __glibc >=2.28,<3.0.a0
  - cuda-version >=12,<13.0a0
  - libcudnn 9.10.1.4 h4840ae0_0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - libcudnn-jit-dev <0a
  license: LicenseRef-cuDNN-Software-License-Agreement
  size: 44217
  timestamp: 1747774406255
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcudnn-dev-9.10.1.4-he9157cb_0.conda
  sha256: 3055a0d418c8feb575209b0a82d6581463b838d66b01101b18535c68111d15a8
  md5: 56aa28c91d288ffb2538561537e36736
  depends:
  - __glibc >=2.28,<3.0.a0
  - arm-variant * sbsa
  - cuda-version >=12,<13.0a0
  - libcudnn 9.10.1.4 hb67e6b9_0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - libcudnn-jit-dev <0a
  license: LicenseRef-cuDNN-Software-License-Agreement
  size: 44291
  timestamp: 1747774516969
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcudss-0.5.0.16-h14340ca_1.conda
  sha256: 0fb14ae71efe11429c24b2fa7d82e718fb52f4cf9cad9379dd7c0302e4294373
  md5: 290a26e7caf9bcbdde629db6612e212e
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - cuda-version >=12,<13.0a0
  - libcublas
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - libcudss-commlayer-nccl 0.5.0.16 hb92ee24_1
  - libcudss-commlayer-mpi 0.5.0.16 h2f16e9f_1
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 32293521
  timestamp: 1739909124258
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcudss-0.5.0.16-hdd5694c_1.conda
  sha256: 296fce0af3abf96ebecf6f55250c3e704711c676c962303eba6c5ee4bf6decea
  md5: c7ed069be634f85f768fbb40630d8cf0
  depends:
  - _openmp_mutex >=4.5
  - cuda-version >=12,<13.0a0
  - libcublas
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  - libcudss-commlayer-mpi 0.5.0.16 h09e6389_1
  - libcudss-commlayer-nccl 0.5.0.16 h19dda4b_1
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 32280958
  timestamp: 1739909103168
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcufft-11.4.1.4-h5888daf_0.conda
  sha256: fb4d2b0c23104d2c42400a3f69f311f087a3b71ab9c9c36bb249919e599b7e8d
  md5: 2da1a83a3b1951e7e8d1c9c3d1340c41
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 162077229
  timestamp: 1749221627451
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcufft-11.4.1.4-h3ae8b8a_0.conda
  sha256: 3f6f7bde03fddb66e2250017f9d08a3fb7d12a6f4dd7e85f370481b863bf44e3
  md5: 348d1c9223bab570ffefdba19a2338f4
  depends:
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 162467016
  timestamp: 1749221693807
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcufile-1.14.1.1-ha8da6e3_0.conda
  sha256: 4ea2d869d04c50459cab1a50167b28b52c22a0b86566f53d06ef14bddb135268
  md5: 0b4600c9d7f93339ae78d504a9188eb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  - rdma-core >=57.0
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 972484
  timestamp: 1749221601010
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcufile-1.14.1.1-h72d2f36_0.conda
  sha256: 46481904f9bbe945f9c7612ac6d8960a2fcca0106512bd0f895bb71ce0aa6ddb
  md5: 7ad52c02f9856c015543c5e05926f92c
  depends:
  - __glibc >=2.28,<3.0.a0
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  - rdma-core >=57.0
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 909879
  timestamp: 1749221669017
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcurand-10.3.10.19-h9ab20c4_0.conda
  sha256: c4576976b8b5ceb060b32d24fc08db5253606256c3c99b42ace343e9be2229db
  md5: c745bc0dd1f066e6752c8b2909216b62
  depends:
  - __glibc >=2.28,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 46161381
  timestamp: 1746193213392
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcurand-10.3.10.19-hd55a8e4_0.conda
  sha256: 92a8650a0628569047553ec66f731feb28e6ec49ed834d8133926cb9aa1922d5
  md5: 8876be17edbd5baa59a29a08f47ed294
  depends:
  - __glibc >=2.28,<3.0.a0
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 46221381
  timestamp: 1746193266187
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.14.1-h332b0f4_0.conda
  sha256: b6c5cf340a4f80d70d64b3a29a7d9885a5918d16a5cb952022820e6d3e79dc8b
  md5: 45f6713cb00f124af300342512219182
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libnghttp2 >=1.64.0,<2.0a0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 449910
  timestamp: 1749033146806
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcurl-8.14.1-h6702fde_0.conda
  sha256: 13f7cc9f6b4bdc9a3544339abf2662bc61018c415fe7a1518137db782eb85343
  md5: 1d92dbf43358f0774dc91764fa77a9f5
  depends:
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libnghttp2 >=1.64.0,<2.0a0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 469143
  timestamp: 1749033114882
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcusolver-11.7.5.82-h9ab20c4_0.conda
  sha256: fadacf0aacead8bb6264c4bce4051f4ef7830c218a4e867a67c02d3c4b28bd08
  md5: ecaa51e8bc0039aab1ac44c1270c70b8
  depends:
  - __glibc >=2.28,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libcublas >=12.9.1.4,<12.10.0a0
  - libcusparse >=12.5.10.65,<12.6.0a0
  - libgcc >=13
  - libnvjitlink >=12.9.86,<12.10.0a0
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 205162082
  timestamp: 1749232252911
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcusolver-11.7.5.82-hd55a8e4_0.conda
  sha256: 0cd51d962958da4d5db4ffcd2429fd2df4eaa9c47c13a65a8e01e7354469ef38
  md5: 8ebfea858d2e82ab1710d6d5ae175e85
  depends:
  - __glibc >=2.28,<3.0.a0
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libcublas >=12.9.1.4,<12.10.0a0
  - libcusparse >=12.5.10.65,<12.6.0a0
  - libgcc >=13
  - libnvjitlink >=12.9.86,<12.10.0a0
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 205025994
  timestamp: 1749232333785
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcusparse-12.5.10.65-h5888daf_0.conda
  sha256: 2e69a61c10633651c80dee982d7e46ed5aef6c06ee47622188403d6b9f99b889
  md5: 662ed6e77f131380286d772f6a364ac2
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libnvjitlink >=12.9.86,<12.10.0a0
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 208848587
  timestamp: 1749224709022
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcusparse-12.5.10.65-h3ae8b8a_0.conda
  sha256: 3f5ffa5086943e85530cc2f6576ecd2eac5b643d3d5d01e9391688d29a3b6975
  md5: 7265288b8a662481c62304bbf146ba67
  depends:
  - arm-variant * sbsa
  - cuda-version >=12.9,<12.10.0a0
  - libgcc >=13
  - libnvjitlink >=12.9.86,<12.10.0a0
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 208720840
  timestamp: 1749224738806
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
  sha256: 8420748ea1cc5f18ecc5068b4f24c7a023cc9b20971c99c824ba10641fb95ddf
  md5: 64f0c503da58ec25ebd359e4d990afa8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 72573
  timestamp: 1747040452262
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdeflate-1.24-he377734_0.conda
  sha256: dd0e4baa983803227ec50457731d6f41258b90b3530f579b5d3151d5a98af191
  md5: f0b3d6494663b3385bf87fc206d7451a
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 70417
  timestamp: 1747040440762
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
  sha256: f53458db897b93b4a81a6dbfd7915ed8fa4a54951f97c698dde6faa028aadfd2
  md5: 4c0ab57463117fbb8df85268415082f5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  size: 246161
  timestamp: 1749904704373
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdrm-2.4.125-h86ecc28_0.conda
  sha256: 4413fda35527cf7a746c5e386fa5406349c0948d51fc20f7896732795a369e5d
  md5: c5e4a8dad08e393b3616651e963304e5
  depends:
  - libgcc >=13
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  size: 252778
  timestamp: 1749904786465
- conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
  sha256: d789471216e7aba3c184cd054ed61ce3f6dac6f87a50ec69291b9297f8c18724
  md5: c277e0a4d549b03ac1e9d6cbbe3d017b
  depends:
  - ncurses
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 134676
  timestamp: 1738479519902
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libedit-3.1.20250104-pl5321h976ea20_0.conda
  sha256: c0b27546aa3a23d47919226b3a1635fccdb4f24b94e72e206a751b33f46fd8d6
  md5: fb640d776fc92b682a14e001980825b1
  depends:
  - ncurses
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 148125
  timestamp: 1738479808948
- conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
  sha256: 7fd5408d359d05a969133e47af580183fbf38e2235b562193d427bb9dad79723
  md5: c151d5eb730e9b7480e6d48c0fc44048
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  size: 44840
  timestamp: 1731330973553
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libegl-1.7.0-hd24410f_2.conda
  sha256: 8962abf38a58c235611ce356b9899f6caeb0352a8bce631b0bcc59352fda455e
  md5: cf105bce884e4ef8c8ccdca9fe6695e7
  depends:
  - libglvnd 1.7.0 hd24410f_2
  license: LicenseRef-libglvnd
  size: 53551
  timestamp: 1731330990477
- conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
  sha256: 1cd6048169fa0395af74ed5d8f1716e22c19a81a8a36f934c110ca3ad4dd27b4
  md5: 172bf1cd1ff8629f2b1179945ed45055
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 112766
  timestamp: 1702146165126
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libev-4.33-h31becfc_2.conda
  sha256: 973af77e297f1955dd1f69c2cbdc5ab9dfc88388a5576cd152cda178af0fd006
  md5: a9a13cb143bbaa477b1ebaefbe47a302
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 115123
  timestamp: 1702146237623
- conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
  sha256: 2e14399d81fb348e9d231a82ca4d816bf855206923759b69ad006ba482764131
  md5: a1cfcc585f0c42bf8d5546bb1dfb668d
  depends:
  - libgcc-ng >=12
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 427426
  timestamp: 1685725977222
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libevent-2.1.12-h4ba1bb4_1.conda
  sha256: 01333cc7d6e6985dd5700b43660d90e9e58049182017fd24862088ecbe1458e4
  md5: 96ae6083cd1ac9f6bc81631ac835b317
  depends:
  - libgcc-ng >=12
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 438992
  timestamp: 1685726046519
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
  sha256: 33ab03438aee65d6aa667cf7d90c91e5e7d734c19a67aa4c7040742c0a13d505
  md5: db0bfbe7dd197b68ad5f30333bae6ce0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  size: 74427
  timestamp: 1743431794976
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
  sha256: e3a0d95fe787cccf286f5dced9fa9586465d3cd5ec8e04f7ad7f0e72c4afd089
  md5: d41a057e7968705dae8dcb7c8ba2c8dd
  depends:
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  size: 73155
  timestamp: 1743432002397
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
  sha256: 608b8c8b0315423e524b48733d91edd43f95cb3354a765322ac306a858c2cd2e
  md5: 15a131f30cae36e9a655ca81fee9a285
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 55847
  timestamp: 1743434586764
- conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
  sha256: 65908b75fa7003167b8a8f0001e11e58ed5b1ef5e98b96ab2ba66d7c1b822c7d
  md5: ee48bf17cc83a00f59ca1494d5646869
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 394383
  timestamp: 1687765514062
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libflac-1.4.3-h2f0025b_0.conda
  sha256: b54935360349d3418b0663d787f20b3cba0b7ce3fcdf3ba5e7ef02b884759049
  md5: 520b12eab32a92e19b1f239ac545ec03
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 371550
  timestamp: 1687765491794
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
  sha256: 7be9b3dac469fe3c6146ff24398b685804dfc7a1de37607b84abd076f57cc115
  md5: 51f5be229d83ecd401fb369ab96ae669
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7693
  timestamp: 1745369988361
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype-2.13.3-h8af1aa0_1.conda
  sha256: c1bb6726b054b00ad509b9ace5e04f4bfe97e6fdaf5c4473c537e6c03d1f660b
  md5: 2d4a1c3dcabb80b4a56d5c34bdacea08
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7774
  timestamp: 1745370050680
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
  sha256: 7759bd5c31efe5fbc36a7a1f8ca5244c2eabdbeb8fc1bee4b99cf989f35c7d81
  md5: 3c255be50a506c50765a93a6644f32fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 380134
  timestamp: 1745369987697
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype6-2.13.3-he93130f_1.conda
  sha256: 9f189f75bb79f6b97c48804e89b4f1db5dc3fba5729551e4cbd2deca98580635
  md5: 51eae9012d75b8f7e4b0adfe61a83330
  depends:
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 408198
  timestamp: 1745370049871
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
  sha256: 59a87161212abe8acc57d318b0cc8636eb834cdfdfddcf1f588b5493644b39a3
  md5: 9e60c55e725c20d23125a5f0dd69af5d
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_3
  - libgomp 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 824921
  timestamp: 1750808216066
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
  sha256: a08e3f89d4cd7c5e18a7098419e378a8506ebfaf4dc1eaac59bf7b962ca66cdb
  md5: 409b902521be20c2efb69d2e0c5e3bc8
  depends:
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 15.1.0 he277a41_3
  - libgcc-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 510464
  timestamp: 1750808926824
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
  sha256: b0b0a5ee6ce645a09578fc1cb70c180723346f8a45fdb6d23b3520591c6d6996
  md5: e66f2b8ad787e7beb0f846e4bd7e8493
  depends:
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29033
  timestamp: 1750808224854
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
  sha256: 222eedd38467f7af8fb703c16cc1abf83038e7b6a09f707bbb4340e8ed589e14
  md5: 831062d3b6a4cdfdde1015be90016102
  depends:
  - libgcc 15.1.0 he277a41_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29009
  timestamp: 1750808930406
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
  sha256: dc9c7d7a6c0e6639deee6fde2efdc7e119e7739a6b229fa5f9049a449bae6109
  md5: 8504a291085c9fb809b66cabd5834307
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgpg-error >=1.55,<2.0a0
  license: LGPL-2.1-or-later
  size: 590353
  timestamp: 1747060639058
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcrypt-lib-1.11.1-h86ecc28_0.conda
  sha256: 5c572886ae3bf8f55fbc8f18275317679b559a9dd00cf1f128d24057dc6de70e
  md5: 50df370cbbbcfb4aa67556879e6643a1
  depends:
  - libgcc >=13
  - libgpg-error >=1.55,<2.0a0
  license: LGPL-2.1-or-later
  size: 652592
  timestamp: 1747060671875
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
  sha256: 104f2341546e295d1136ab3010e81391bd3fd5be0f095db59266e8eba2082d37
  md5: 2ee6d71b72f75d50581f2f68e965efdb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 171165
  timestamp: 1746228870846
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-0.24.1-h5ad3122_0.conda
  sha256: 3dc0c79b5780d1155ed38a9b4e484d5bd3ea8d5f7e825cff69741cf8dc53313f
  md5: 54bb1208da39417046f68daa603a37eb
  depends:
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 217039
  timestamp: 1746228750642
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
  sha256: a9a0cba030778eb2944a1f235dba51e503b66f8be0ce6f55f745173a515c3644
  md5: 8f04c7aae6a46503bc36d1ed5abc8c7c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 37234
  timestamp: 1746228897993
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-devel-0.24.1-h5ad3122_0.conda
  sha256: 5fbd5afb2a48282c9b147c068c513bfdfaab22ffdb5eae6c6bc573d44f08d791
  md5: 376a04253d0a8345e67fc01ceae504c5
  depends:
  - libgcc >=13
  - libgettextpo 0.24.1 h5ad3122_0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 37390
  timestamp: 1746228765836
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
  sha256: 77dd1f1efd327e6991e87f09c7c97c4ae1cfbe59d9485c41d339d6391ac9c183
  md5: bfbca721fd33188ef923dfe9ba172f29
  depends:
  - libgfortran5 15.1.0 hcea5267_3
  constrains:
  - libgfortran-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29057
  timestamp: 1750808257258
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran-15.1.0-he9431aa_3.conda
  sha256: 9459e5e273397ee6680ea2f1f4bd64161f58a198e36a9983737f494642e08535
  md5: 2987b138ed84460e6898daab172e9798
  depends:
  - libgfortran5 15.1.0 hbc25352_3
  constrains:
  - libgfortran-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29043
  timestamp: 1750808952391
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
  sha256: eea6c3cf22ad739c279b4d665e6cf20f8081f483b26a96ddd67d4df3c88dfa0a
  md5: 530566b68c3b8ce7eec4cd047eae19fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 1565627
  timestamp: 1750808236464
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran5-15.1.0-hbc25352_3.conda
  sha256: c835503233b6114387e552fc549437657f893e06b684e42aff3739fef2bae235
  md5: eb1421397fe5db5ad4c3f8d611dd5117
  depends:
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 1140270
  timestamp: 1750808938364
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
  sha256: dc2752241fa3d9e40ce552c1942d0a4b5eeb93740c9723873f6fcf8d39ef8d2d
  md5: 928b8be80851f5d8ffb016f9c81dae7a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - libglx 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  size: 134712
  timestamp: 1731330998354
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgl-1.7.0-hd24410f_2.conda
  sha256: 3e954380f16255d1c8ae5da3bd3044d3576a0e1ac2e3c3ff2fe8f2f1ad2e467a
  md5: 0d00176464ebb25af83d40736a2cd3bb
  depends:
  - libglvnd 1.7.0 hd24410f_2
  - libglx 1.7.0 hd24410f_2
  license: LicenseRef-libglvnd
  size: 145442
  timestamp: 1731331005019
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
  sha256: a6b5cf4d443044bc9a0293dd12ca2015f0ebe5edfdc9c4abdde0b9947f9eb7bd
  md5: 072ab14a02164b7c0c089055368ff776
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.2 *_0
  license: LGPL-2.1-or-later
  size: 3955066
  timestamp: 1747836671118
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglib-2.84.2-hc022ef1_0.conda
  sha256: a74d52adc3b913e75185c0afaf9403c85f47c2c6ad585fdbd16f29b6c364a848
  md5: 51323eab8e9f049d001424828c4c25a4
  depends:
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.2 *_0
  license: LGPL-2.1-or-later
  size: 4016850
  timestamp: 1747836804570
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
  sha256: 1175f8a7a0c68b7f81962699751bb6574e6f07db4c9f72825f978e3016f46850
  md5: 434ca7e50e40f4918ab701e3facd59a0
  depends:
  - __glibc >=2.17,<3.0.a0
  license: LicenseRef-libglvnd
  size: 132463
  timestamp: 1731330968309
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglvnd-1.7.0-hd24410f_2.conda
  sha256: 57ec3898a923d4bcc064669e90e8abfc4d1d945a13639470ba5f3748bd3090da
  md5: 9e115653741810778c9a915a2f8439e7
  license: LicenseRef-libglvnd
  size: 152135
  timestamp: 1731330986070
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
  sha256: 2d35a679624a93ce5b3e9dd301fff92343db609b79f0363e6d0ceb3a6478bfa7
  md5: c8013e438185f33b13814c5c488acd5c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - xorg-libx11 >=1.8.10,<2.0a0
  license: LicenseRef-libglvnd
  size: 75504
  timestamp: 1731330988898
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglx-1.7.0-hd24410f_2.conda
  sha256: 6591af640cb05a399fab47646025f8b1e1a06a0d4bbb4d2e320d6629b47a1c61
  md5: 1d4269e233636148696a67e2d30dad2a
  depends:
  - libglvnd 1.7.0 hd24410f_2
  - xorg-libx11 >=1.8.9,<2.0a0
  license: LicenseRef-libglvnd
  size: 77736
  timestamp: 1731330998960
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.36.0-hc4361e1_1.conda
  sha256: 3a56c653231d6233de5853dc01f07afad6a332799a39c3772c0948d2e68547e4
  md5: ae36e6296a8dd8e8a9a8375965bf6398
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libcurl >=8.12.1,<9.0a0
  - libgcc >=13
  - libgrpc >=1.71.0,<1.72.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - openssl >=3.4.1,<4.0a0
  constrains:
  - libgoogle-cloud 2.36.0 *_1
  license: Apache-2.0
  license_family: Apache
  size: 1246764
  timestamp: 1741878603939
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgoogle-cloud-2.36.0-h1c497bb_1.conda
  sha256: f6270b5116824a053a3436c3ccea1defc6de9929fb70bdc96989a8f2715c0cfa
  md5: 5bf0805e849a11096801c7380e6cf919
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libcurl >=8.12.1,<9.0a0
  - libgcc >=13
  - libgrpc >=1.71.0,<1.72.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - openssl >=3.4.1,<4.0a0
  constrains:
  - libgoogle-cloud 2.36.0 *_1
  license: Apache-2.0
  license_family: Apache
  size: 1246275
  timestamp: 1741879605378
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.36.0-h0121fbd_1.conda
  sha256: 54235d990009417bb20071f5ce7c8dcf186b19fa7d24d72bc5efd2ffb108001c
  md5: a0f7588c1f0a26d550e7bae4fb49427a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil
  - libcrc32c >=1.1.2,<1.2.0a0
  - libcurl
  - libgcc >=13
  - libgoogle-cloud 2.36.0 hc4361e1_1
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl
  license: Apache-2.0
  license_family: Apache
  size: 785719
  timestamp: 1741878763994
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgoogle-cloud-storage-2.36.0-hb9b2b65_1.conda
  sha256: 9051dc56bb62d0d97f05777aac21943dea5be7062162848489cae4630671d8cf
  md5: 246706d9277aa021c795b5b7f4f73137
  depends:
  - libabseil
  - libcrc32c >=1.1.2,<1.2.0a0
  - libcurl
  - libgcc >=13
  - libgoogle-cloud 2.36.0 h1c497bb_1
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl
  license: Apache-2.0
  license_family: Apache
  size: 740744
  timestamp: 1741879806791
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
  sha256: 697334de4786a1067ea86853e520c64dd72b11a05137f5b318d8a444007b5e60
  md5: 2bd47db5807daade8500ed7ca4c512a4
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  size: 312184
  timestamp: 1745575272035
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgpg-error-1.55-h5ad3122_0.conda
  sha256: a744c0a137a084af7cee4a33de9bffb988182b5be4edb8a45d51d2a1efd3724c
  md5: 39f742598d0f18c8e1cb01712bc03ee8
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  license: LGPL-2.1-only
  size: 327973
  timestamp: 1745575312848
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.71.0-h8e591d7_1.conda
  sha256: 37267300b25f292a6024d7fd9331085fe4943897940263c3a41d6493283b2a18
  md5: c3cfd72cbb14113abee7bbd86f44ad69
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.34.5,<2.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libre2-11 >=2024.7.2
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - re2
  constrains:
  - grpc-cpp =1.71.0
  license: Apache-2.0
  license_family: APACHE
  size: 7920187
  timestamp: 1745229332239
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgrpc-1.71.0-h107bb78_1.conda
  sha256: 8e50ffb17d098d0f120333666aeb431dc29a64cfe878825a1ceb25e6b4edf0d3
  md5: 519fc968f744e14b85ca9083e880a613
  depends:
  - c-ares >=1.34.5,<2.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libre2-11 >=2024.7.2
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - re2
  constrains:
  - grpc-cpp =1.71.0
  license: Apache-2.0
  license_family: APACHE
  size: 7810414
  timestamp: 1745191482846
- conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
  sha256: d14c016482e1409ae1c50109a9ff933460a50940d2682e745ab1c172b5282a69
  md5: 804ca9e91bcaea0824a341d55b1684f2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.13.4,<2.14.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 2423200
  timestamp: 1731374922090
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libhwloc-2.11.2-default_h2c612a5_1001.conda
  sha256: 8c7bf410afb4f068063c718a8691de611eeb75f3d0c6122698c7961e90820445
  md5: 8f42119cdfd1ac905e19f0eeebe9ccfa
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.13.4,<2.14.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 2436762
  timestamp: 1731374851939
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libiconv-1.18-hc99b53d_1.conda
  sha256: 3db14977036fe1f511a6dbecacbeff3fdb58482c5c0cf87a2ea3232f5a540836
  md5: 81541d85a45fbf4d0a29346176f1f21c
  depends:
  - libgcc >=13
  license: LGPL-2.1-only
  size: 718600
  timestamp: 1740130562607
- conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
  sha256: 98b399287e27768bf79d48faba8a99a2289748c65cd342ca21033fab1860d4a4
  md5: 9fa334557db9f63da6c9285fd2a48638
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 628947
  timestamp: 1745268527144
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libjpeg-turbo-3.1.0-h86ecc28_0.conda
  sha256: c7e4f017eeadcabb30e2a95dae95aad27271d633835e55e5dae23c932ae7efab
  md5: a689388210d502364b79e8b19e7fa2cb
  depends:
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 653054
  timestamp: 1745268199701
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_hc41d3b0_mkl.conda
  build_number: 32
  sha256: dc1be931203a71f5c84887cde24659fdd6fda73eb8c6cf56e67b68e3c7916efd
  md5: 6dc827963c12f90c79f5b2be4eaea072
  depends:
  - libblas 3.9.0 32_hfdb39a5_mkl
  constrains:
  - liblapacke 3.9.0   32*_mkl
  - blas 2.132   mkl
  - libcblas   3.9.0   32*_mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  size: 17284
  timestamp: 1750388691797
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblapack-3.9.0-32_h411afd4_openblas.conda
  build_number: 32
  sha256: 9d88f242d138e23bcaf3c1f2d41b53cef5594b1fd9da84dd35ec7e944a946de3
  md5: 8d143759d5a22e9975a996bd13eeb8f0
  depends:
  - libblas 3.9.0 32_h1a9f1db_openblas
  constrains:
  - libcblas   3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17308
  timestamp: 1750388926844
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
  sha256: 498ea4b29155df69d7f20990a7028d75d91dbea24d04b2eb8a3d6ef328806849
  md5: 7d362346a479256857ab338588190da0
  depends:
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 125103
  timestamp: 1749232230009
- conda: https://conda.anaconda.org/conda-forge/linux-64/libmagma-2.9.0-h19665d7_1.conda
  sha256: 13d50a4f7da02e6acce4b5b6df82072c0f447a2c5ba1f4a3190dfec3a9174965
  md5: 38b3447782263c96b0c0a7b92c97575e
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - cuda-cudart >=12.6.77,<13.0a0
  - cuda-version >=12.6,<13
  - libblas >=3.9.0,<4.0a0
  - libcublas >=12.6.4.1,<13.0a0
  - libcusparse >=12.5.4.2,<13.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 371275523
  timestamp: 1739994057566
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmagma-2.9.0-h95896e5_0.conda
  sha256: af53e177bac85e021104820aea8d954e1971ef891666f9990b58ecb1c0ebb2ae
  md5: 8c50cd0710fa5e41aff5df5721cc0b67
  depends:
  - _openmp_mutex >=4.5
  - cuda-cudart >=12.6.77,<13.0a0
  - cuda-version >=12.6,<13
  - libblas >=3.9.0,<4.0a0
  - libcublas >=12.6.4.1,<13.0a0
  - libcusparse >=12.5.4.2,<13.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 371184858
  timestamp: 1739897917458
- conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
  sha256: 3aa92d4074d4063f2a162cd8ecb45dccac93e543e565c01a787e16a43501f7ee
  md5: c7e925f37e3b40d893459e625f6a53f1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 91183
  timestamp: 1748393666725
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
  sha256: ef8697f934c80b347bf9d7ed45650928079e303bad01bd064995b0e3166d6e7a
  md5: 78cfed3f76d6f3f279736789d319af76
  depends:
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 114064
  timestamp: 1748393729243
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
  sha256: b0f2b3695b13a989f75d8fd7f4778e1c7aabe3b36db83f0fe80b2cd812c0e975
  md5: 19e57602824042dfd0446292ef90488b
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.32.3,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 647599
  timestamp: 1729571887612
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libnghttp2-1.64.0-hc8609a4_0.conda
  sha256: c093c6d370aadbf0409c20b6c54c488ee2f6fea976181919fcc63e87ee232673
  md5: f52c614fa214a8bedece9421c771670d
  depends:
  - c-ares >=1.32.3,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 714610
  timestamp: 1729571912479
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnl-3.11.0-hb9d3cd8_0.conda
  sha256: ba7c5d294e3d80f08ac5a39564217702d1a752e352e486210faff794ac5001b4
  md5: db63358239cbe1ff86242406d440e44a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 741323
  timestamp: 1731846827427
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libnl-3.11.0-h86ecc28_0.conda
  sha256: 2e603bf640738511faf80de284daa031f0e67de66b77bed7d0da1045ef062abf
  md5: bb24d3dd7d028b70f0bb5f6d6e1329c0
  depends:
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 768716
  timestamp: 1731846931826
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnvjitlink-12.9.86-h5888daf_0.conda
  sha256: 2df595ff4cd599446ed7ca01cdfaccc6bc8de89de45b834dd8d5b044ef1d0aea
  md5: 7bc06365942b9e4a037746c182feff4d
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 30525691
  timestamp: 1749219248901
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libnvjitlink-12.9.86-h3ae8b8a_0.conda
  sha256: fafe5dcb0fe7b65e85eebdac36021f4408bc80fa1dd3ff549ad0193697119829
  md5: c9ec6714cbfac2dfc16d1726855d56ad
  depends:
  - arm-variant * sbsa
  - cuda-version >=12,<12.10.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - arm-variant * sbsa
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  size: 30332052
  timestamp: 1749219299368
- conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
  sha256: ffb066ddf2e76953f92e06677021c73c85536098f1c21fcd15360dbc859e22e4
  md5: 68e52064ed3897463c0e958ab5c8f91b
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  size: 218500
  timestamp: 1745825989535
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libogg-1.3.5-h86ecc28_1.conda
  sha256: 2c1b7c59badc2fd6c19b6926eabfce906c996068d38c2972bd1cfbe943c07420
  md5: 319df383ae401c40970ee4e9bc836c7a
  depends:
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 220653
  timestamp: 1745826021156
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenblas-0.3.30-pthreads_h9d3fd7e_0.conda
  sha256: be2e84de39422a8a4241ceff5145913a475571a9ed5729f435c715ad8d9db266
  md5: 7c3670fbc19809070c27948efda30c4b
  depends:
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 4960761
  timestamp: 1750379264152
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-1.21.0-hd1b1c89_0.conda
  sha256: b88de51fa55513483e7c80c43d38ddd3559f8d17921879e4c99909ba66e1c16b
  md5: 4b25cd8720fd8d5319206e4f899f2707
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libcurl >=8.14.0,<9.0a0
  - libgrpc >=1.71.0,<1.72.0a0
  - libopentelemetry-cpp-headers 1.21.0 ha770c72_0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libzlib >=1.3.1,<2.0a0
  - nlohmann_json
  - prometheus-cpp >=1.3.0,<1.4.0a0
  constrains:
  - cpp-opentelemetry-sdk =1.21.0
  license: Apache-2.0
  license_family: APACHE
  size: 882002
  timestamp: 1748592427188
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopentelemetry-cpp-1.21.0-h77ebfab_0.conda
  sha256: 51d6ff7b26cb6d6e052ebc2eb95f424d2e7916153a16605caad327ff4ddde55d
  md5: 455617c1dcd0896ac3af1ed0a05829ee
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libcurl >=8.14.0,<9.0a0
  - libgrpc >=1.71.0,<1.72.0a0
  - libopentelemetry-cpp-headers 1.21.0 h8af1aa0_0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libzlib >=1.3.1,<2.0a0
  - nlohmann_json
  - prometheus-cpp >=1.3.0,<1.4.0a0
  constrains:
  - cpp-opentelemetry-sdk =1.21.0
  license: Apache-2.0
  license_family: APACHE
  size: 874554
  timestamp: 1748592365613
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-headers-1.21.0-ha770c72_0.conda
  sha256: dbd811e7a7bd9b96fccffe795ba539ac6ffcc5e564d0bec607f62aa27fa86a17
  md5: ********************************
  license: Apache-2.0
  license_family: APACHE
  size: 359509
  timestamp: 1748592389311
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopentelemetry-cpp-headers-1.21.0-h8af1aa0_0.conda
  sha256: 0b1175c58dc313995ec1f869d1207b502194cffe55a3ad34b1a5f2581c6d9afa
  md5: 73bcbf8a59534676229947fd769cbc94
  license: Apache-2.0
  license_family: APACHE
  size: 360117
  timestamp: 1748592343853
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2025.0.0-hdc3f47d_3.conda
  sha256: fe0e184141a3563d4c97134a1b7a60c66302cf0e2692d15d49c41382cdf61648
  md5: 3a88245058baa9d18ef4ea6df18ff63e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  size: 5698665
  timestamp: 1742046924817
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-2025.0.0-hd63d6c0_3.conda
  sha256: d4e774708a073ba4a240fd2bc0f524d8b6d9fe68a24074bc7affe70c7fd9d8b7
  md5: 97277bfdfcc0dd59c0a74869fb31269a
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  size: 5068959
  timestamp: 1742043279584
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-arm-cpu-plugin-2025.0.0-hd63d6c0_3.conda
  sha256: 1097bf9bfff8a9dade6b2a033b107aafed75d0dd2b4430a1754d8b89cb12f47d
  md5: 387c0cad41f9e9cf330da02e9f7d4898
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  size: 8652305
  timestamp: 1742043300690
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2025.0.0-h4d9b6c2_3.conda
  sha256: b4c61b3e8fc4d7090a94e3fd3936faf347eea07cac993417153dd99bd293c08d
  md5: 2e349bafc75b212879bf70ef80e0d08c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  size: 111823
  timestamp: 1742046947746
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-batch-plugin-2025.0.0-hf15766e_3.conda
  sha256: 829a98d1dd0859fec5536419c9d7b1b99a612a91c629f173f6e9f05003e85749
  md5: 70a507a1ce0a13f5562953631deec2fd
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  size: 109653
  timestamp: 1742043331132
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2025.0.0-h4d9b6c2_3.conda
  sha256: ae72903e0718897b85aae2110d9bb1bfa9490b0496522e3735b65c771e7da0ea
  md5: 74d074a3ac7af3378e16bfa6ff9cba30
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  size: 238973
  timestamp: 1742046961091
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-plugin-2025.0.0-hf15766e_3.conda
  sha256: af207ffa6f3a8a150620ca32c2996e941645689596ad2dc923115cef3ac1706d
  md5: 8399dc85b397fdb3770613c4b10f5a49
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  size: 227098
  timestamp: 1742043342711
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2025.0.0-h981d57b_3.conda
  sha256: b2c9ef97907f9c77817290bfb898897b476cc7ccf1737f0b1254437dda3d4903
  md5: 21f7997d68220d7356c1f80dc500bfad
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  size: 196083
  timestamp: 1742046974588
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-hetero-plugin-2025.0.0-ha8e9e04_3.conda
  sha256: 69c8e3a060a10900f6d35df32264f48560e153fe370c6a2ee7fcff1b969629bb
  md5: e12bff64bfd2ef9e282383afb3cccc13
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  size: 187049
  timestamp: 1742043354710
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2025.0.0-hdc3f47d_3.conda
  sha256: 9f6613906386a0c679c9a683ca97a5a2070111d9ada4f115c1806d921313e32d
  md5: 3385f38d15c7aebcc3b453e4d8dfb0fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  size: 12419296
  timestamp: 1742046988488
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2025.0.0-hdc3f47d_3.conda
  sha256: 8430f87a3cc65d3ef1ec8f9bfa990f6fb635601ad34ce08d70209099ff03f39c
  md5: f2d50e234edd843d9d695f7da34c7e96
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - ocl-icd >=2.3.2,<3.0a0
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  size: 10119530
  timestamp: 1742047030958
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-npu-plugin-2025.0.0-hdc3f47d_3.conda
  sha256: 37ec3e304bf14d2d7b7781c4b6a8b3a54deae90bc7275f6ae160589ef219bcef
  md5: f632cad865436394eebd41c3afa2cda3
  depends:
  - __glibc >=2.17,<3.0.a0
  - level-zero >=1.21.2,<2.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  size: 1092544
  timestamp: 1742047065987
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2025.0.0-h981d57b_3.conda
  sha256: 268716b5c1858c1fddd51d63c7fcd7f3544ef04f221371ab6a2f9c579ca001e4
  md5: 94f25cc6fe70f507897abb8e61603023
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  size: 206013
  timestamp: 1742047080381
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-ir-frontend-2025.0.0-ha8e9e04_3.conda
  sha256: 3901f6922cfbac4de21622445d8a201862f46f502c95251bd2cba11eb67bf839
  md5: a3edb4a113c03ec3a3db3f89c7dabfb8
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  size: 197852
  timestamp: 1742043366449
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2025.0.0-h0e684df_3.conda
  sha256: 5ce66c01f6ea365a497f488e8eecea8930b6a016f9809db7f33b8a1ebbe5644e
  md5: 7cd3272c3171c1d43ed1c2b3d6795269
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  size: 1668681
  timestamp: 1742047094228
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-onnx-frontend-2025.0.0-hd8f0270_3.conda
  sha256: 72097ef28507f41ff371cb10540261b7cbd433a9932a9c42d073f4d56335bfbe
  md5: cf46d328c1b254d16d18128999d31d61
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  size: 1466096
  timestamp: 1742043380485
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2025.0.0-h0e684df_3.conda
  sha256: 826507ac4ea2d496bdbec02dd9e3c8ed2eab253daa9d7f9119a8bc05c516d026
  md5: 5b66cbc9965b429922b8e69cd4e464d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  size: 690226
  timestamp: 1742047109935
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-paddle-frontend-2025.0.0-hd8f0270_3.conda
  sha256: b7666e63f7399e94599829b9b8901e1e6541d9d4d0c156359eb24846a434bcb7
  md5: 9d6043d6fae5342567173f949af80e4f
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  size: 625570
  timestamp: 1742043394408
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2025.0.0-h5888daf_3.conda
  sha256: fda07e70a23aac329be68ae488b790f548d687807f0e47bae7129df34f0adb5b
  md5: a6ece96eff7f60b2559ba699156b0edf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  size: 1123885
  timestamp: 1742047125703
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-pytorch-frontend-2025.0.0-h5ad3122_3.conda
  sha256: 174f630bdc3ffc6728fc83aefef15cf9a9a9fcd00712ce809df7a3b5c37dae99
  md5: d740a43f206611d7ab09488a6cb2f8eb
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  size: 1016003
  timestamp: 1742043406713
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2025.0.0-h684f15b_3.conda
  sha256: e02990fccd4676e362a026acff3d706b5839ebf6ae681d56a2903f62a63e03ef
  md5: e1aeb108f4731db088782c8a20abf40a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - snappy >=1.2.1,<1.3.0a0
  size: 1313789
  timestamp: 1742047140816
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-frontend-2025.0.0-h33e842c_3.conda
  sha256: 437fc934eaa6282258ac2dc3e58d276b208079ee2440264cd19b67a9b6ff6827
  md5: 9083c0e4a30698bdbab0598d964e4540
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - snappy >=1.2.1,<1.3.0a0
  size: 1204132
  timestamp: 1742043420133
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5888daf_3.conda
  sha256: 236569eb4d472d75412a3384c2aad92b006afed721feec23ca08730a25932da7
  md5: a6fe9c25b834988ac88651aff731dd31
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  size: 488142
  timestamp: 1742047155790
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5ad3122_3.conda
  sha256: e1328d5e6ef41e112c1e79d06e2309b89da302806a5ec7b18cf7bfe47d321be6
  md5: bb1da88624792f47b7ac93ae0bb8206e
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  size: 445050
  timestamp: 1742043433188
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
  sha256: 786d43678d6d1dc5f88a6bad2d02830cfd5a0184e84a8caa45694049f0e3ea5f
  md5: b64523fb87ac6f87f0790f324ad43046
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  size: 312472
  timestamp: 1744330953241
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopus-1.5.2-h86ecc28_0.conda
  sha256: c887543068308fb0fd50175183a3513f60cd8eb1defc23adc3c89769fde80d48
  md5: 44b2cfec6e1b94723a960f8a5e6206ae
  depends:
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 357115
  timestamp: 1744331282621
- conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-20.0.0-h3f30f2e_8_cuda.conda
  build_number: 8
  sha256: e9b7b4416b83b86de7028878cd6117cd369f5640b66d476e301a60e4589d8a26
  md5: c3f4f661fa6a55e6763b59fd494e4ede
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0 h019e7cd_8_cuda
  - libgcc
  - libgcc-ng >=12
  - libstdcxx
  - libstdcxx-ng >=12
  - libthrift >=0.21.0,<0.21.1.0a0
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  size: 1214865
  timestamp: 1750865956895
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libparquet-20.0.0-hfc78867_8_cpu.conda
  build_number: 8
  sha256: 0035475a6850523f6adda495c9892c503e27baeea6af119948d4a1e33d3da56e
  md5: fcb6894f2618f3cd55cd7efd3f7e07cc
  depends:
  - libarrow 20.0.0 h82abc5a_8_cpu
  - libgcc >=13
  - libstdcxx >=13
  - libthrift >=0.21.0,<0.21.1.0a0
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  size: 1153338
  timestamp: 1750865804505
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
  sha256: 0bd91de9b447a2991e666f284ae8c722ffb1d84acb594dbd0c031bd656fa32b2
  md5: 70e3400cbbfa03e96dcde7fc13e38c7b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 28424
  timestamp: 1749901812541
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpciaccess-0.18-h86ecc28_0.conda
  sha256: 7641dfdfe9bda7069ae94379e9924892f0b6604c1a016a3f76b230433bb280f2
  md5: 5044e160c5306968d956c2a0a2a440d6
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 29512
  timestamp: 1749901899881
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
  sha256: c8f5dc929ba5fcee525a66777498e03bbcbfefc05a0773e5163bb08ac5122f1a
  md5: 37511c874cf3b8d0034c8d24e73c0884
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 289506
  timestamp: 1750095629466
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpng-1.6.49-hec79eb8_0.conda
  sha256: d9b2e2c1b47ea8889fa4407ad2ffbf6388b9608031a98acd9f9876d0b15a20cc
  md5: a665eccfe09f815de0cdda657598a5b3
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 293525
  timestamp: 1750097792167
- conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
  sha256: 691af28446345674c6b3fb864d0e1a1574b6cc2f788e0f036d73a6b05dcf81cf
  md5: edb86556cf4a0c133e7932a1597ff236
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 3358788
  timestamp: 1745159546868
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libprotobuf-5.29.3-h4edc36e_1.conda
  sha256: 3dea67282f1e3442030ff9d4ee46747e5260dac3360db27f0e0227d913bbc744
  md5: 2f321e8f84944b3b41f7187bbc2bbedd
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 3196210
  timestamp: 1745158850256
- conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2024.07.02-hba17884_3.conda
  sha256: 392ec1e49370eb03270ffd4cc8d727f8e03e1e3a92b12f10c53f396ae4554668
  md5: 545e93a513c10603327c76c15485e946
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - re2 2024.07.02.*
  license: BSD-3-Clause
  license_family: BSD
  size: 210073
  timestamp: 1741121121238
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libre2-11-2024.07.02-h201e9ed_3.conda
  sha256: a11b9c6866d0e3c927c007328f72e371b227fd6ba0f11e152246d72732f51c6e
  md5: c387f2b8ce1dc1e00fcdc2b661e60306
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - re2 2024.07.02.*
  license: BSD-3-Clause
  license_family: BSD
  size: 203786
  timestamp: 1741121206498
- conda: https://conda.anaconda.org/conda-forge/noarch/librosa-0.10.2.post1-pyhd8ed1ab_1.conda
  sha256: 0228fc5394a5db6d41b565de21eb4ebdf08b83e61a0f8e1c3b4dac68a9f3334b
  md5: 38fa8d1ab982ec8046e96fa57ecaa12f
  depends:
  - audioread >=2.1.9
  - decorator >=4.3.0
  - joblib >=0.14.0
  - lazy_loader >=0.1
  - matplotlib-base >=3.5.0
  - msgpack-python >=1.0
  - numba >=0.51.0
  - numpy >=1.20.3,!=1.22.0,!=1.22.1,!=1.22.2
  - packaging >=20.0
  - pooch >=1.1
  - pysoundfile >=0.12.1
  - python >=3.9
  - scikit-learn >=0.20.0
  - scipy >=1.2.0
  - soxr-python >=0.3.2
  - typing_extensions >=4.1.1
  license: ISC
  size: 198799
  timestamp: 1734618456185
- conda: https://conda.anaconda.org/conda-forge/noarch/librosa-0.11.0-pyhd8ed1ab_0.conda
  sha256: e791136e2254d73fb0c9624823eaf78778415e15c26e3950916c502141871c38
  md5: dc9d871c7bc97f21b25a350d0508ecd7
  depends:
  - audioread >=2.1.9
  - decorator >=4.3.0
  - joblib >=1.0
  - lazy_loader >=0.1
  - matplotlib-base >=3.5.0
  - msgpack-python >=1.0
  - numba >=0.51.0
  - numpy >=1.22.3
  - packaging >=20.0
  - pooch >=1.1
  - pysoundfile >=0.12.1
  - python >=3.9
  - scikit-learn >=1.1.0
  - scipy >=1.6.0
  - soxr-python >=0.3.2
  - standard-aifc
  - standard-sunau
  - typing_extensions >=4.1.1
  license: ISC
  size: 199376
  timestamp: 1741797667827
- conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-he92a37e_3.conda
  sha256: a45ef03e6e700cc6ac6c375e27904531cf8ade27eb3857e080537ff283fb0507
  md5: d27665b20bc4d074b86e628b3ba5ab8b
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - freetype >=2.13.3,<3.0a0
  - gdk-pixbuf >=2.42.12,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libxml2 >=2.13.7,<2.14.0a0
  - pango >=1.56.3,<2.0a0
  constrains:
  - __glibc >=2.17
  license: LGPL-2.1-or-later
  size: 6543651
  timestamp: 1743368725313
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/librsvg-2.58.4-h3ac5bce_3.conda
  sha256: e305cf09ec904625a66c7db1305595691c633276b7e34521537cef88edc5249a
  md5: b115c14b3919823fbe081366d2b15d86
  depends:
  - cairo >=1.18.4,<2.0a0
  - freetype >=2.13.3,<3.0a0
  - gdk-pixbuf >=2.42.12,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libxml2 >=2.13.7,<2.14.0a0
  - pango >=1.56.3,<2.0a0
  constrains:
  - __glibc >=2.17
  license: LGPL-2.1-or-later
  size: 6274749
  timestamp: 1743376660664
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
  sha256: f709cbede3d4f3aee4e2f8d60bd9e256057f410bd60b8964cb8cf82ec1457573
  md5: ef1910918dd895516a769ed36b5b3a4e
  depends:
  - lame >=3.100,<3.101.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 354372
  timestamp: 1695747735668
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsndfile-1.2.2-h79657aa_1.conda
  sha256: 8fcd5e45d6fb071e8baf492ebb8710203fd5eedf0cb791e007265db373c89942
  md5: ad8e62c0faec46b1442f960489c80b49
  depends:
  - lame >=3.100,<3.101.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 396501
  timestamp: 1695747749825
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
  sha256: 0105bd108f19ea8e6a78d2d994a6d4a8db16d19a41212070d2d1d48a63c34161
  md5: a587892d3c13b6621a6091be690dbca2
  depends:
  - libgcc-ng >=12
  license: ISC
  size: 205978
  timestamp: 1716828628198
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsodium-1.0.20-h68df207_0.conda
  sha256: 448df5ea3c5cf1af785aad46858d7a5be0522f4234a4dc9bb764f4d11ff3b981
  md5: 2e4a8f23bebdcb85ca8e5a0fbe75666a
  depends:
  - libgcc-ng >=12
  license: ISC
  size: 177394
  timestamp: 1716828514515
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
  sha256: 9a9e5bf30178f821d4f8de25eac0ae848915bfde6a78a66ae8b77d9c33d9d0e5
  md5: c7c4888059a8324e52de475d1e7bdc53
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  size: 919723
  timestamp: 1750925531920
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.1-h4970e9a_7.conda
  sha256: 772350ba2252059871247c5f78410882fa1a25476e31e55da39aa4a634893e76
  md5: d758d556022c04b155a3525fe365e5c2
  depends:
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  size: 922189
  timestamp: 1750925625197
- conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
  sha256: fa39bfd69228a13e553bd24601332b7cfeb30ca11a3ca50bb028108fe90a7661
  md5: eecce068c7e4eddeb169591baac20ac4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 304790
  timestamp: 1745608545575
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libssh2-1.11.1-h18c354c_0.conda
  sha256: 1e289bcce4ee6a5817a19c66e296f3c644dcfa6e562e5c1cba807270798814e7
  md5: eecc495bcfdd9da8058969656f916cc2
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 311396
  timestamp: 1745609845915
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
  sha256: 7650837344b7850b62fdba02155da0b159cf472b9ab59eb7b472f7bd01dff241
  md5: 6d11a5edae89fe413c0569f16d308f5a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 3896407
  timestamp: 1750808251302
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
  sha256: 916a8c2530992140d23c4d3f63502f250ff36df7298ed9a8b72d3629c347d4ce
  md5: 4e2d5a407e0ecfe493d8b2a65a437bd8
  depends:
  - libgcc 15.1.0 he277a41_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 3833339
  timestamp: 1750808947966
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
  sha256: bbaea1ecf973a7836f92b8ebecc94d3c758414f4de39d2cc6818a3d10cb3216b
  md5: 57541755b5a51691955012b8e197c06c
  depends:
  - libstdcxx 15.1.0 h8f9b012_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29093
  timestamp: 1750808292700
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
  sha256: cb93360dce004fda2f877fd6071c8c0d19ab67b161ff406d5c0d63b7658ad77c
  md5: f981af71cbd4c67c9e6acc7d4cc3f163
  depends:
  - libstdcxx 15.1.0 h3f4de04_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29078
  timestamp: 1750808974598
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
  sha256: e26b22c0ae40fb6ad4356104d5fa4ec33fe8dd8a10e6aef36a9ab0c6a6f47275
  md5: 1e12c8aa74fa4c3166a9bdc135bc4abf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  - libgcrypt-lib >=1.11.1,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: LGPL-2.1-or-later
  size: 487969
  timestamp: 1750949895969
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsystemd0-257.7-h2bb824b_0.conda
  sha256: 35ecfc98c22d4f035b051fe72398206607d48944e7bd4f60431e63eb95538e0d
  md5: 63b49a2d12a1739f72be430c2ed58727
  depends:
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  - libgcrypt-lib >=1.11.1,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: LGPL-2.1-or-later
  size: 510879
  timestamp: 1750949944203
- conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.21.0-h0e7cc3e_0.conda
  sha256: ebb395232973c18745b86c9a399a4725b2c39293c9a91b8e59251be013db42f0
  md5: dcb95c0a98ba9ff737f7ae482aef7833
  depends:
  - __glibc >=2.17,<3.0.a0
  - libevent >=2.1.12,<2.1.13.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 425773
  timestamp: 1727205853307
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libthrift-0.21.0-h154c74f_0.conda
  sha256: f04ab1417aca1687edff9c30d8423ace285eb8c053dc16d595c6e47cfeefb274
  md5: c28792bf37f4ecdce8e3cb9e40750650
  depends:
  - libevent >=2.1.12,<2.1.13.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 417329
  timestamp: 1727205944238
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
  sha256: 7fa6ddac72e0d803bb08e55090a8f2e71769f1eb7adbd5711bdd7789561601b1
  md5: e79a094918988bb1807462cd42c83962
  depends:
  - __glibc >=2.17,<3.0.a0
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 429575
  timestamp: 1747067001268
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtiff-4.7.0-h7c15681_5.conda
  sha256: 4b2c6f5cd5199d5e345228a0422ecb31a4340ff69579db49faccba14186bb9a2
  md5: 264a9aac20276b1784dac8c5f8d3704a
  depends:
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 466229
  timestamp: 1747067015512
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtorch-2.7.0-cuda126_mkl_h99b69db_300.conda
  sha256: b4e8c062ddc343be1ff84346ef4f90b258a87d67e747e50a3644a81d1978eb40
  md5: 67d004faec95b8fff704681eae9ccf40
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - cuda-cudart >=12.6.77,<13.0a0
  - cuda-cupti >=12.6.80,<13.0a0
  - cuda-nvrtc >=12.6.85,<13.0a0
  - cuda-nvtx >=12.6.77,<13.0a0
  - cuda-version >=12.6,<13
  - cudnn >=9.8.0.87,<10.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libblas * *mkl
  - libcblas >=3.9.0,<4.0a0
  - libcublas >=12.6.4.1,<13.0a0
  - libcudss >=0.5.0.16,<0.5.1.0a0
  - libcufft >=11.3.0.4,<12.0a0
  - libcufile >=1.11.1.6,<2.0a0
  - libcurand >=10.3.7.77,<11.0a0
  - libcusolver >=11.7.1.2,<12.0a0
  - libcusparse >=12.5.4.2,<13.0a0
  - libgcc >=13
  - libmagma >=2.9.0,<2.9.1.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libuv >=1.50.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.4
  - mkl >=2024.2.2,<2025.0a0
  - nccl >=2.26.5.1,<3.0a0
  - sleef >=3.8,<4.0a0
  constrains:
  - pytorch-gpu 2.7.0
  - pytorch-cpu <0.0a0
  - pytorch 2.7.0 cuda126_mkl_*_300
  license: BSD-3-Clause
  license_family: BSD
  size: 594396124
  timestamp: 1746283375271
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtorch-2.7.0-cuda126_generic_h5977fb4_200.conda
  sha256: 43a2b5136291c85b91b6590948723a4e54957bde362bf622744ee88d1ec62f90
  md5: f058c30d11d4ce1a9864fa8aa49ed262
  depends:
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - cuda-cudart >=12.6.77,<13.0a0
  - cuda-cupti >=12.6.80,<13.0a0
  - cuda-nvrtc >=12.6.85,<13.0a0
  - cuda-nvtx >=12.6.77,<13.0a0
  - cuda-version >=12.6,<13
  - cudnn >=9.8.0.87,<10.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libcublas >=12.6.4.1,<13.0a0
  - libcudss >=0.5.0.16,<0.5.1.0a0
  - libcufft >=11.3.0.4,<12.0a0
  - libcufile >=1.11.1.6,<2.0a0
  - libcurand >=10.3.7.77,<11.0a0
  - libcusolver >=11.7.1.2,<12.0a0
  - libcusparse >=12.5.4.2,<13.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libmagma >=2.9.0,<2.9.1.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libuv >=1.50.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.4
  - nccl >=2.26.5.1,<3.0a0
  - sleef >=3.8,<4.0a0
  constrains:
  - openblas * openmp_*
  - pytorch 2.7.0 cuda126_generic_*_200
  - pytorch-cpu <0.0a0
  - pytorch-gpu 2.7.0
  license: BSD-3-Clause
  license_family: BSD
  size: 581386054
  timestamp: 1746516421849
- conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
  sha256: 3fca2655f4cf2ce6b618a2b52e3dce92f27f429732b88080567d5bbeea404c82
  md5: 5a23e52bd654a5297bd3e247eebab5a3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 143533
  timestamp: 1750949902296
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libudev1-257.7-h7b9e449_0.conda
  sha256: 8c9847a934e251479f343f0be3b771836cdccfcf132145bd2da34946acd01988
  md5: d19d804623b40d7ab5f807c240b4caaf
  depends:
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 154447
  timestamp: 1750949949664
- conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
  sha256: f2ac872920833960e514ce9efd8f7c08ce66dd870738d73839d1bce1ac497de6
  md5: a730b2badd586580c5752cc73842e068
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 75491
  timestamp: 1638450786937
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libunwind-1.6.2-h01db608_0.tar.bz2
  sha256: 7862d36ffc9f6b2ed3381ce77c78b9e5691d7353a19dd2050630868e192adf6f
  md5: 93b7bbf9099cfe09e67c0abe34bb7885
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 90479
  timestamp: 1638452154070
- conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
  sha256: d1922de78ead6a9d19b7a4f82cf1fff7332e9012fd9968aa835c89888628d3d7
  md5: 1a11973f25f6168f4f6a65883cf7bb2a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 124944
  timestamp: 1748686602857
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liburing-2.10-h17cf362_0.conda
  sha256: 658d4138e5cd76794c81c3561361fd9d2091c9a13a329cb5b61a83371feeef4e
  md5: 49a98ebf80bbc4661df8a26706d8b3b5
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 126811
  timestamp: 1748687566969
- conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
  sha256: 89c84f5b26028a9d0f5c4014330703e7dff73ba0c98f90103e9cef6b43a5323c
  md5: d17e3fb595a9f24fa9e149239a33475d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libudev1 >=257.4
  license: LGPL-2.1-or-later
  size: 89551
  timestamp: 1748856210075
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libusb-1.0.29-h06eaf92_0.conda
  sha256: a60aae6b529cd7caa7842f9781ef95b93014e618f71fb005e404af434d76a33f
  md5: 9a86e7473e16fe25c5c47f6c1376ac82
  depends:
  - libgcc >=13
  - libudev1 >=257.4
  license: LGPL-2.1-or-later
  size: 93129
  timestamp: 1748856228398
- conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.10.0-h202a827_0.conda
  sha256: c4ca78341abb308134e605476d170d6f00deba1ec71b0b760326f36778972c0e
  md5: 0f98f3e95272d118f7931b6bef69bfe5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 83080
  timestamp: 1748341697686
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libutf8proc-2.10.0-hb15dbc2_0.conda
  sha256: dd96200001a5c0cbfa3f5a45eb2ff70d1f4509f6f35c814d291cbefee1651572
  md5: 4f9f12d268e0d74e7ac839869fbeb313
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 83418
  timestamp: 1748341820649
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
  sha256: 616277b0c5f7616c2cdf36f6c316ea3f9aa5bb35f2d4476a349ab58b9b91675f
  md5: 000e30b09db0b7c775b21695dff30969
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 35720
  timestamp: 1680113474501
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb9d3cd8_0.conda
  sha256: 770ca175d64323976c9fe4303042126b2b01c1bd54c8c96cafeaba81bdb481b8
  md5: 1349c022c92c5efd3fd705a79a5804d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 890145
  timestamp: 1748304699136
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuv-1.51.0-h86ecc28_0.conda
  sha256: 2b3811ac29005edb63b85e4eb24d13e04b93e3c9b33dcb11b11a11681af0665d
  md5: bd76e353d6a09ae834fc9056343f2f73
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 645133
  timestamp: 1748304599853
- conda: https://conda.anaconda.org/conda-forge/linux-64/libva-2.22.0-h4f16b4b_2.conda
  sha256: e0df324fb02fa05a05824b8db886b06659432b5cff39495c59e14a37aa23d40f
  md5: 2c65566e79dc11318ce689c656fb551c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libdrm >=2.4.124,<2.5.0a0
  - libegl >=1.7.0,<2.0a0
  - libgcc >=13
  - libgl >=1.7.0,<2.0a0
  - libglx >=1.7.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - wayland >=1.23.1,<2.0a0
  - wayland-protocols
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  size: 217567
  timestamp: 1740897682004
- conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
  sha256: 53080d72388a57b3c31ad5805c93a7328e46ff22fab7c44ad2a86d712740af33
  md5: 309dec04b70a3cc0f1e84a4013683bc0
  depends:
  - libgcc-ng >=9.3.0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  size: 286280
  timestamp: 1610609811627
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvorbis-1.3.7-h01db608_0.tar.bz2
  sha256: 1ade4727be5c52b287001b8094d02af66342dfe0ba13ef69222aaaf2e9be4342
  md5: c2863ff72c6d8a59054f8b9102c206e9
  depends:
  - libgcc-ng >=9.3.0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  size: 292082
  timestamp: 1610616294416
- conda: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.1-hac33072_0.conda
  sha256: e7d2daf409c807be48310fcc8924e481b62988143f582eb3a58c5523a6763b13
  md5: cde393f461e0c169d9ffb2fc70f81c33
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 1022466
  timestamp: 1717859935011
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvpx-1.14.1-h0a1ffab_0.conda
  sha256: 918493354f78cb3bb2c3d91264afbcb312b2afe287237e7d1c85ee7e96d15b47
  md5: 3cb63f822a49e4c406639ebf8b5d87d7
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 1211700
  timestamp: 1717859955539
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
  sha256: c45283fd3e90df5f0bd3dbcd31f59cdd2b001d424cf30a07223655413b158eaf
  md5: 63f790534398730f59e1b899c3644d4a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  size: 429973
  timestamp: 1734777489810
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libwebp-base-1.5.0-h0886dbf_0.conda
  sha256: b3d881a0ae08bb07fff7fa8ead506c8d2e0388733182fe4f216f3ec5d61ffcf0
  md5: 95ef4a689b8cc1b7e18b53784d88f96b
  depends:
  - libgcc >=13
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  size: 362623
  timestamp: 1734779054659
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxcb-1.17.0-h262b8f6_0.conda
  sha256: 461cab3d5650ac6db73a367de5c8eca50363966e862dcf60181d693236b1ae7b
  md5: cd14ee5cca2464a425b1dbfc24d90db2
  depends:
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 397493
  timestamp: 1727280745441
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
  sha256: a8043a46157511b3ceb6573a99952b5c0232313283f2d6a066cec7c8dcaed7d0
  md5: fedf6bfe5d21d21d2b1785ec00a8889a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  size: 707156
  timestamp: 1747911059945
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxkbcommon-1.10.0-hbab7b08_0.conda
  sha256: 620f16864f4f9d7181d89fa4266dba0b18cb9bca72f930500cf9307e549e4247
  md5: 36cd1db31e923c6068b7e0e6fce2cd7b
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  size: 719116
  timestamp: 1747911079432
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
  sha256: b0b3a96791fa8bb4ec030295e8c8bf2d3278f33c0f9ad540e73b5e538e6268e7
  md5: 14dbe05b929e329dbaa6f2d0aa19466d
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 690864
  timestamp: 1746634244154
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxml2-2.13.8-he060846_0.conda
  sha256: b453be503923e213ce5132de0b2e2bc89549d742dcc403acba8dcc4a0ced740c
  md5: c73dfe6886cc8d39a09c357a36f91fb2
  depends:
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 733785
  timestamp: 1746634366734
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
  sha256: 5a2c1eeef69342e88a98d1d95bff1603727ab1ff4ee0e421522acd8813439b84
  md5: 08aad7cbe9f5a6b460d0976076b6ae64
  depends:
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 66657
  timestamp: 1727963199518
- conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-20.1.7-h024ca30_0.conda
  sha256: 10f2f6be8ba4c018e1fc741637a8d45c0e58bea96954c25e91fbe4238b7c9f60
  md5: b9c9b2f494533250a9eb7ece830f4422
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - openmp 20.1.7|20.1.7.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  size: 4165732
  timestamp: 1749892194931
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/llvm-openmp-20.1.7-h013ceaa_0.conda
  sha256: 809cc723aca13fe094234a429c7252b448c61f2adcc1691c75050a2a6e5eedf1
  md5: 77264b8967a0be28d6a6bfe6021f7597
  constrains:
  - openmp 20.1.7|20.1.7.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  size: 3093436
  timestamp: 1749892327189
- conda: https://conda.anaconda.org/conda-forge/linux-64/llvmlite-0.44.0-py313h1b76d92_1.conda
  sha256: 16fe60ac27ba104c1817e2302b8278324e03226670538bc07e643a2a753f4b95
  md5: 22837ab06ba7099cb71bb27e8d667277
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-2-Clause
  license_family: BSD
  size: 30004763
  timestamp: 1742815892040
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/llvmlite-0.44.0-py313h8a79bcf_1.conda
  sha256: ade9bb92c7336e5e92a0393794ab58328adf60ea94911e53b7edd2e3e6180c5f
  md5: 0ba5402d73097a9e387fefbe277384b4
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: BSD-2-Clause
  license_family: BSD
  size: 31763599
  timestamp: 1742815958574
- conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
  sha256: 47326f811392a5fd3055f0f773036c392d26fdb32e4d8e7a8197eed951489346
  md5: 9de5350a85c4a20c685259b889aa6393
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 167055
  timestamp: 1733741040117
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lz4-c-1.10.0-h5ad3122_1.conda
  sha256: 67e55058d275beea76c1882399640c37b5be8be4eb39354c94b610928e9a0573
  md5: 6654e411da94011e8fbe004eacb8fe11
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 184953
  timestamp: 1733740984533
- conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py313h8060acc_1.conda
  sha256: d812caf52efcea7c9fd0eafb21d45dadfd0516812f667b928bee50e87634fae5
  md5: 21b62c55924f01b6eef6827167b46acb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  size: 24856
  timestamp: 1733219782830
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/markupsafe-3.0.2-py313h7815b11_1.conda
  sha256: d91212c3c12c4dd9500ca85973c1bad28e2204e8c838e823d87c86a791bef866
  md5: 1dbae17ceddb4e6ab2f239a00c7b5e52
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  size: 25269
  timestamp: 1733220943845
- conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-base-3.10.3-py313h129903b_0.conda
  sha256: eb23d6945d34836b62add0ca454f287cadb74b4b771cdd7196a1f51def425014
  md5: 4f8816d006b1c155ec416bcf7ff6cee2
  depends:
  - __glibc >=2.17,<3.0.a0
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.21,<3
  - numpy >=1.23
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.13,<3.14.0a0
  - python-dateutil >=2.7
  - python_abi 3.13.* *_cp313
  - qhull >=2020.2,<2020.3.0a0
  - tk >=8.6.13,<8.7.0a0
  license: PSF-2.0
  license_family: PSF
  size: 8479847
  timestamp: 1746820689093
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/matplotlib-base-3.10.3-py313h16bfeab_0.conda
  sha256: 9a8bfe31b03b8ae8435a90a7fe4511ca25f2a1835e8a4b9b6ebdc2ea047aa05b
  md5: 1293d1d86537161617103af4e5e05fce
  depends:
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.21,<3
  - numpy >=1.23
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python-dateutil >=2.7
  - python_abi 3.13.* *_cp313
  - qhull >=2020.2,<2020.3.0a0
  - tk >=8.6.13,<8.7.0a0
  license: PSF-2.0
  license_family: PSF
  size: 8420417
  timestamp: 1746820805174
- conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 311e01e00ce7302eb97c263c021700c7baa1a6d9be9477f60edcfd17fbf4b49d
  depends:
  - max-core ==25.5.0.dev2025062705 release
  - max-python ==25.5.0.dev2025062705 release
  - mojo-jupyter ==25.5.0.dev2025062705 release
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 9411
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025062705-release.conda
  sha256: be1ef195c01fd7253ad1fb4b4ec647708bd53cbe31a2e97318943fed4062f92c
  depends:
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 222775006
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/linux-aarch64/max-core-25.5.0.dev2025062705-release.conda
  sha256: d7aa1cc232011221751258767cf91a7a6facd799cae6b1503c467b87d0980eda
  depends:
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 224027056
  timestamp: 1751001477655
- conda: https://conda.modular.com/max-nightly/linux-64/max-python-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 9204326c477affb8fd8f9c84de91591a21bd1cf24f0d4390716b3ca642cdb711
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025062705 release
  constrains:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 30541775
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/linux-aarch64/max-python-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 9341747ee3d40a553adf8ec0c9608dd5efefb4dfee6367587ac4e2bdfbd73d10
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025062705 release
  constrains:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 19347501
  timestamp: 1751001477655
- conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 20b01c20917066a7f186385ae19a017c091e094dbddb4c59f1737bf2eed0a9fe
  depends:
  - python >=3.9,<3.14
  - click >=8.0.0
  - mypy_extensions >=0.4.3
  - packaging >=22.0
  - pathspec >=0.9.0
  - platformdirs >=2
  - typing_extensions >=v4.12.2
  - python
  license: MIT
  size: 131254
  timestamp: 1751001417267
- conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2024.2.2-ha957f24_16.conda
  sha256: 77906b0acead8f86b489da46f53916e624897338770dbf70b04b8f673c9273c1
  md5: 1459379c79dda834673426504d52b319
  depends:
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - llvm-openmp >=19.1.2
  - tbb 2021.*
  license: LicenseRef-IntelSimplifiedSoftwareOct2022
  license_family: Proprietary
  size: 124718448
  timestamp: 1730231808335
- conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 27842df9aea46c60c8d6487e38c44b18c5aa533e7e84f3d98c5ee7fd978862a4
  depends:
  - max-core ==25.5.0.dev2025062705 release
  - python >=3.9,<3.14
  - jupyter_client >=8.6.2,<8.7
  - python
  license: LicenseRef-Modular-Proprietary
  size: 22485
  timestamp: 1751001417267
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-h24ddda3_1.conda
  sha256: 1bf794ddf2c8b3a3e14ae182577c624fa92dea975537accff4bc7e5fea085212
  md5: aa14b9a5196a6d8dd364164b7ce56acf
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpfr >=4.2.1,<5.0a0
  license: LGPL-3.0-or-later
  license_family: LGPL
  size: 116777
  timestamp: 1725629179524
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpc-1.3.1-h783934e_1.conda
  sha256: b5b674f496ed28c0b2d08533c6f11eaf1840bf7d9c830655f51514f2f9d9a9c8
  md5: d3758cd24507dc1bda3483ce051d48ac
  depends:
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpfr >=4.2.1,<5.0a0
  license: LGPL-3.0-or-later
  license_family: LGPL
  size: 132799
  timestamp: 1725629168783
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
  sha256: f25d2474dd557ca66c6231c8f5ace5af312efde1ba8290a6ea5e1732a4e669c0
  md5: 2eeb50cab6652538eee8fc0bc3340c81
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  license: LGPL-3.0-only
  license_family: LGPL
  size: 634751
  timestamp: 1725746740014
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpfr-4.2.1-h2305555_3.conda
  sha256: abb35c37de2ec6c9ee89995142b1cfea9e6547202ba5578e5307834eca6d436f
  md5: 65b21e8d5f0ec6a2f7e87630caed3318
  depends:
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  license: LGPL-3.0-only
  license_family: LGPL
  size: 1841314
  timestamp: 1725746723157
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
  sha256: 39c4700fb3fbe403a77d8cc27352fa72ba744db487559d5d44bf8411bb4ea200
  md5: c7f302fd11eeb0987a6a5e1f3aed6a21
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-only
  license_family: LGPL
  size: 491140
  timestamp: 1730581373280
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpg123-1.32.9-h65af167_0.conda
  sha256: d65d5a00278544639ba4f99887154be00a1f57afb0b34d80b08e5cba40a17072
  md5: cdf140c7690ab0132106d3bc48bce47d
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-only
  license_family: LGPL
  size: 558708
  timestamp: 1730581372400
- conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
  sha256: 7d7aa3fcd6f42b76bd711182f3776a02bef09a68c5f117d66b712a6d81368692
  md5: 3585aa87c43ab15b167b574cd73b057b
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 439705
  timestamp: 1733302781386
- conda: https://conda.anaconda.org/conda-forge/linux-64/msgpack-python-1.1.1-py313h33d0bda_0.conda
  sha256: b0e1b68a6e74d77986190f7296187c799a3f56119cb06663f7a57b15a7b1bd98
  md5: 009fb5ad03d4506be5f1e5c2f875f1c2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 102677
  timestamp: 1749813320003
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/msgpack-python-1.1.1-py313h44a8f36_0.conda
  sha256: da5b1871386956c1717226f8e515dcb148693020e2935fbbf609224b5f7c5046
  md5: 6c8957a1465a4138e94a827e217ced02
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 99761
  timestamp: 1749813391464
- conda: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.5.1-py313h8060acc_0.conda
  sha256: 554dcc07affac8a946130fd5fa8b31673a00dd00de7f9f1c7f066f60e0959004
  md5: b26e546e9eb05247526bb539a2490af5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  size: 94652
  timestamp: 1750832559641
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/multidict-6.5.1-py313h857f82b_0.conda
  sha256: 3b572e7b19e3c6ab5807d1cb5c19405022563b0f83eac656c51d182c948d0466
  md5: a98b73c0f56930e62809bd764fba0a5c
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  size: 96734
  timestamp: 1750832457895
- conda: https://conda.anaconda.org/conda-forge/linux-64/multiprocess-0.70.16-py313h536fd9c_1.conda
  sha256: 916c6fb35f30ef3631b43657a160528fabaa00b10fdd679b10500410b806ca39
  md5: 224f2700fa997c2d0d13328f3d02f4b6
  depends:
  - __glibc >=2.17,<3.0.a0
  - dill >=0.3.8
  - libgcc >=13
  - python >=3.13.0rc1,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 349052
  timestamp: 1724954746625
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/multiprocess-0.70.16-py313h31d5739_1.conda
  sha256: d0f746193e788a1cc39bab6e7f29ce9770c406bf4dd4ccf495019d795bf55bad
  md5: 081ae9dc600d1ec96433b21da11250c3
  depends:
  - dill >=0.3.8
  - libgcc >=13
  - python >=3.13.0rc1,<3.14.0a0
  - python >=3.13.0rc1,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 347590
  timestamp: 1724954860028
- conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
  sha256: d09c47c2cf456de5c09fa66d2c3c5035aa1fa228a1983a433c47b876aa16ce90
  md5: 37293a85a0f4f77bbd9cf7aaefc62609
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 15851
  timestamp: 1749895533014
- conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
  sha256: 6ed158e4e5dd8f6a10ad9e525631e35cee8557718f83de7a4e3966b1f772c4b1
  md5: e9c622e0d00fa24a6292279af3ab6d06
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11766
  timestamp: 1745776666688
- conda: https://conda.anaconda.org/conda-forge/linux-64/nccl-********-h9b8ff78_0.conda
  sha256: d94d3252a0c1b893947e4e2cb3ff9184868645d6156dec6609c6832c3b5d9fe5
  md5: cac7e04909de453bca7ef38beb28e3c5
  depends:
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=12,<13.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 213637740
  timestamp: 1750419797926
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/nccl-********-h3544485_0.conda
  sha256: a0394588a166f57087021b357a104da696a753cf80a48513f5e9a60825bf06ef
  md5: 6d9738c88a078865a68c4b30f3967e4d
  depends:
  - cuda-version >=12,<13.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 213619530
  timestamp: 1750421370287
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
  sha256: 91cfb655a68b0353b2833521dc919188db3d8a7f4c64bea2c6a7557b24747468
  md5: 182afabe009dc78d8b73100255ee6868
  depends:
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  size: 926034
  timestamp: 1738196018799
- conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.5-pyhe01879c_0.conda
  sha256: 02019191a2597865940394ff42418b37bc585a03a1c643d7cea9981774de2128
  md5: 16bff3d37a4f99e3aa089c36c2b8d650
  depends:
  - python >=3.11
  - python
  constrains:
  - numpy >=1.25
  - scipy >=1.11.2
  - matplotlib >=3.8
  - pandas >=2.0
  license: BSD-3-Clause
  license_family: BSD
  size: 1564462
  timestamp: 1749078300258
- conda: https://conda.anaconda.org/conda-forge/linux-64/nlohmann_json-3.12.0-h3f2d84a_0.conda
  sha256: e2fc624d6f9b2f1b695b6be6b905844613e813aa180520e73365062683fe7b49
  md5: d76872d096d063e226482c99337209dc
  license: MIT
  license_family: MIT
  size: 135906
  timestamp: 1744445169928
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/nlohmann_json-3.12.0-h5ad3122_0.conda
  sha256: ba0e4e4f0b0b7fa1f7b7e3abe95823daf915d73ddd976e73a5f9ade2060760dd
  md5: 92016ee90e17c57a1d2f47333d4bc92f
  license: MIT
  license_family: MIT
  size: 136733
  timestamp: 1744445179648
- conda: https://conda.anaconda.org/conda-forge/noarch/nomkl-1.0-h5ca1d4c_0.tar.bz2
  sha256: d38542a151a90417065c1a234866f97fd1ea82a81de75ecb725955ab78f88b4b
  md5: 9a66894dfd07c4510beb6b3f9672ccc0
  constrains:
  - mkl <0.a0
  license: BSD-3-Clause
  license_family: BSD
  size: 3843
  timestamp: 1582593857545
- conda: https://conda.anaconda.org/conda-forge/linux-64/numba-0.61.2-py313h50b8c88_1.conda
  sha256: e588053a9d8e73fd68a0cdc00b9893800258f376175ed91a05de162a235099f9
  md5: 53c79b7cdee329ed4c77cafe27600cdb
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libstdcxx >=13
  - llvmlite >=0.44.0,<0.45.0a0
  - numpy >=1.21,<3
  - numpy >=1.24,<2.3
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - cuda-version >=11.2
  - scipy >=1.0
  - tbb >=2021.6.0
  - cuda-python >=11.6
  - cudatoolkit >=11.2
  - libopenblas !=0.3.6
  license: BSD-2-Clause
  license_family: BSD
  size: 5864595
  timestamp: 1749491444304
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/numba-0.61.2-py313h83e9d6b_1.conda
  sha256: 38bd813f22286c4274fdc16f7d9acf2822781ab7df2ba72eae824264ef7c7c87
  md5: 849947140609efd2e6eafe681a93aab1
  depends:
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libstdcxx >=13
  - llvmlite >=0.44.0,<0.45.0a0
  - numpy >=1.21,<3
  - numpy >=1.24,<2.3
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - cuda-python >=11.6
  - tbb >=2021.6.0
  - cudatoolkit >=11.2
  - cuda-version >=11.2
  - scipy >=1.0
  license: BSD-2-Clause
  license_family: BSD
  size: 5852926
  timestamp: 1749491510408
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.2.6-py313h17eae1a_0.conda
  sha256: 7da9ebd80a7311e0482c4c6393be0eddf0012b3846df528e375037409b3d2b3d
  md5: 7a2d2f9adecd86ed5c29c2115354f615
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 8517250
  timestamp: 1747545080496
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/numpy-2.2.6-py313hcf1be6b_0.conda
  sha256: 9a23a172dac09b67c49d453fc8db721cb2592207ddac56474761fa62b9132e70
  md5: 08fbc8832428e4010a8b38efce6563d2
  depends:
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 7151540
  timestamp: 1747545079947
- conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
  sha256: 2254dae821b286fb57c61895f2b40e3571a070910fdab79a948ff703e1ea807b
  md5: 56f8947aa9d5cf37b0b3d43b83f34192
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - opencl-headers >=2024.10.24
  license: BSD-2-Clause
  license_family: BSD
  size: 106742
  timestamp: 1743700382939
- conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
  sha256: 2b6ce54174ec19110e1b3c37455f7cd138d0e228a75727a9bba443427da30a36
  md5: 45c3d2c224002d6d0d7769142b29f986
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  size: 55357
  timestamp: 1749853464518
- conda: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.6.0-hc22cd8d_0.conda
  sha256: 3f231f2747a37a58471c82a9a8a80d92b7fece9f3fce10901a5ac888ce00b747
  md5: b28cf020fd2dead0ca6d113608683842
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 731471
  timestamp: 1739400677213
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openh264-2.6.0-h0564a2a_0.conda
  sha256: 3b7a519e3b7d7721a0536f6cba7f1909b878c71962ee67f02242958314748341
  md5: 0abed5d78c07a64e85c54f705ba14d30
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 774512
  timestamp: 1739400731652
- conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
  sha256: 5bee706ea5ba453ed7fd9da7da8380dd88b865c8d30b5aaec14d2b6dd32dbc39
  md5: 9e5816bc95d285c115a3ebc2f8563564
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.44,<1.7.0a0
  - libstdcxx >=13
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 342988
  timestamp: 1733816638720
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openjpeg-2.5.3-h3f56577_0.conda
  sha256: 92d310033e20538e896f4e4b1ea4205eb6604eee7c5c651c4965a0d8d3ca0f1d
  md5: 04231368e4af50d11184b50e14250993
  depends:
  - libgcc >=13
  - libpng >=1.6.44,<1.7.0a0
  - libstdcxx >=13
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 377796
  timestamp: 1733816683252
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
  sha256: b4491077c494dbf0b5eaa6d87738c22f2154e9277e5293175ec187634bd808a0
  md5: de356753cfdbffcde5bb1e86e3aa6cd0
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 3117410
  timestamp: 1746223723843
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.0-hd08dc88_1.conda
  sha256: 58120daf06a52ba203f94eccb43900213a9f2b3cc310bbaa868505ccd7afbdaa
  md5: ee68fdc3a8723e9c58bdd2f10544658f
  depends:
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 3642633
  timestamp: 1746225726804
- conda: https://conda.anaconda.org/conda-forge/linux-64/optree-0.16.0-py313h33d0bda_0.conda
  sha256: 528603488f908e7da6699f0ce5a0157a63e1d78ae7a8fd71ff8b942478caa824
  md5: 5c211bb056e1a3263a163ba21e3fbf73
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - typing-extensions >=4.6
  license: Apache-2.0
  license_family: Apache
  size: 435778
  timestamp: 1748442698453
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/optree-0.16.0-py313h44a8f36_0.conda
  sha256: e67218269c39a03285643ed2127ced66d3c14888a2c34e7bd4ab712a06b81aa2
  md5: f4dfb9f75774dbfa87fe57b96363a817
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - typing-extensions >=4.6
  license: Apache-2.0
  license_family: Apache
  size: 401194
  timestamp: 1748442854883
- conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.1.2-h17f744e_0.conda
  sha256: f6ff644e27f42f2beb877773ba3adc1228dbb43530dbe9426dd672f3b847c7c5
  md5: ef7f9897a244b2023a066c22a1089ce4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - snappy >=1.2.1,<1.3.0a0
  - tzdata
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0
  license_family: Apache
  size: 1242887
  timestamp: 1746604310927
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/orc-2.1.2-h38030b9_0.conda
  sha256: c39010f67efb144df421a829e85bd54cba6af6e835053f5c2c1a000b2e83091f
  md5: 1dd927aeacd16f906fcffeb4f4f1bb38
  depends:
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - snappy >=1.2.1,<1.3.0a0
  - tzdata
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0
  license_family: Apache
  size: 1226212
  timestamp: 1746604293581
- conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
  sha256: 289861ed0c13a15d7bbb408796af4de72c2fe67e2bcb0de98f4c3fce259d7991
  md5: 58335b26c38bf4a20f399384c33cbcf9
  depends:
  - python >=3.8
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 62477
  timestamp: 1745345660407
- conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.0-py313ha87cce1_0.conda
  sha256: c4a6e9bc13454c5afd17600c2ee2b6b07fee8b2629cb1c193c22c048faa9bdcc
  md5: 8664b4fa9b5b23b0d1cdc55c7195fcfe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.21,<3
  - numpy >=1.22.4
  - python >=3.13,<3.14.0a0
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.13.* *_cp313
  - pytz >=2020.1
  constrains:
  - zstandard >=0.19.0
  - sqlalchemy >=2.0.0
  - pyqt5 >=5.15.9
  - pyxlsb >=1.0.10
  - qtpy >=2.3.0
  - odfpy >=1.4.1
  - python-calamine >=0.1.7
  - pytables >=3.8.0
  - numexpr >=2.8.4
  - s3fs >=2022.11.0
  - html5lib >=1.1
  - pyarrow >=10.0.1
  - xarray >=2022.12.0
  - lxml >=4.9.2
  - openpyxl >=3.1.0
  - fastparquet >=2022.12.0
  - fsspec >=2022.11.0
  - matplotlib >=3.6.3
  - scipy >=1.10.0
  - pandas-gbq >=0.19.0
  - xlsxwriter >=3.0.5
  - blosc >=1.21.3
  - xlrd >=2.0.1
  - bottleneck >=1.3.6
  - numba >=0.56.4
  - beautifulsoup4 >=4.11.2
  - pyreadstat >=1.2.0
  - tabulate >=0.9.0
  - tzdata >=2022.7
  - gcsfs >=2022.11.0
  - psycopg2 >=2.9.6
  license: BSD-3-Clause
  license_family: BSD
  size: 14991000
  timestamp: 1749100101435
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pandas-2.3.0-py313h7b4b6ea_0.conda
  sha256: 69bb155c76946a10ddd21a21ff92aa6a1c3d731a0eab6677a615a54cd626c0d7
  md5: 1ec9da64e38218029f5f68de3fd47978
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.21,<3
  - numpy >=1.22.4
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.13.* *_cp313
  - pytz >=2020.1
  constrains:
  - fastparquet >=2022.12.0
  - psycopg2 >=2.9.6
  - numexpr >=2.8.4
  - openpyxl >=3.1.0
  - qtpy >=2.3.0
  - pytables >=3.8.0
  - pyxlsb >=1.0.10
  - sqlalchemy >=2.0.0
  - bottleneck >=1.3.6
  - scipy >=1.10.0
  - matplotlib >=3.6.3
  - beautifulsoup4 >=4.11.2
  - gcsfs >=2022.11.0
  - xarray >=2022.12.0
  - xlrd >=2.0.1
  - tabulate >=0.9.0
  - lxml >=4.9.2
  - python-calamine >=0.1.7
  - zstandard >=0.19.0
  - fsspec >=2022.11.0
  - tzdata >=2022.7
  - numba >=0.56.4
  - blosc >=1.21.3
  - pyreadstat >=1.2.0
  - pandas-gbq >=0.19.0
  - s3fs >=2022.11.0
  - pyqt5 >=5.15.9
  - html5lib >=1.1
  - pyarrow >=10.0.1
  - xlsxwriter >=3.0.5
  - odfpy >=1.4.1
  license: BSD-3-Clause
  license_family: BSD
  size: 14641471
  timestamp: 1749100216037
- conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h9ac818e_1.conda
  sha256: 9c00bbc8871b9ce00d1a1f0c1a64f76c032cf16a56a28984b9bb59e46af3932d
  md5: 21899b96828014270bd24fd266096612
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.13.3,<3.0a0
  - fribidi >=1.0.10,<2.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  license: LGPL-2.1-or-later
  size: 453100
  timestamp: 1743352484196
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pango-1.56.3-h1e6a6fd_1.conda
  sha256: 85f3863d264c28665e43c3084ff93273f93b67637c4b28aa8d3645d789e27a33
  md5: bc1598700c29f7d1fe8e3218acef401e
  depends:
  - cairo >=1.18.4,<2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.13.3,<3.0a0
  - fribidi >=1.0.10,<2.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  license: LGPL-2.1-or-later
  size: 465939
  timestamp: 1743354615490
- conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9f64009cdf5b8e529995f18e03665b03f5d07c0b17445b8badef45bde76249ee
  md5: 617f15191456cc6a13db418a275435e5
  depends:
  - python >=3.9
  license: MPL-2.0
  license_family: MOZILLA
  size: 41075
  timestamp: 1733233471940
- conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
  sha256: 27c4014f616326240dcce17b5f3baca3953b6bc5f245ceb49c3fa1e6320571eb
  md5: b90bece58b4c2bf25969b70f3be42d25
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 1197308
  timestamp: 1745955064657
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pcre2-10.45-hf4ec17f_0.conda
  sha256: d5aecfcb64514719600e35290cc885098dbfef8e9c037eea6afc43d1acc65c2e
  md5: ad22a9a9497f7aedce73e0da53cd215f
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 1134832
  timestamp: 1745955178803
- conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.2.1-py313h8db990d_0.conda
  sha256: f35e9bef2dd00361b871deb015cd50c3ff2847b957af16ab98651443eab1010c
  md5: 91b00afee98d72d29dc3d1c1ab0008d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  size: 42836283
  timestamp: 1746646372487
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pillow-11.2.1-py313h96bbe82_0.conda
  sha256: d5a62f2d7166a4117cf77786ebb149d3afdeb2df5d6a534705808ddeabae968d
  md5: 325f7944f19f9acce65f4bf5a791c4c2
  depends:
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  size: 42758706
  timestamp: 1746648583536
- conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
  sha256: 6cb261595b5f0ae7306599f2bb55ef6863534b6d4d1bc0dcfdfa5825b0e4e53d
  md5: 39b4228a867772d610c02e06f939a5b8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 402222
  timestamp: 1749552884791
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pixman-0.46.2-h86a87f0_0.conda
  sha256: df60bb320bbec8df804780c0310b471478a245192c16568769fc96269ce15445
  md5: 019114cf59c0cce5a08f6661179a1d65
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 310404
  timestamp: 1749554318638
- conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
  sha256: 0f48999a28019c329cd3f6fd2f01f09fc32cc832f7d6bbe38087ddac858feaa3
  md5: 424844562f5d337077b445ec6b1398a7
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 23531
  timestamp: 1746710438805
- conda: https://conda.anaconda.org/conda-forge/noarch/pooch-1.8.2-pyhd8ed1ab_1.conda
  sha256: bedda6b36e8e42b0255179446699a0cf08051e6d9d358dd0dd0e787254a3620e
  md5: b3e783e8e8ed7577cf0b6dee37d1fbac
  depends:
  - packaging >=20.0
  - platformdirs >=2.5.0
  - python >=3.9
  - requests >=2.19.0
  license: BSD-3-Clause
  license_family: BSD
  size: 54116
  timestamp: 1733421432357
- conda: https://conda.anaconda.org/conda-forge/linux-64/prometheus-cpp-1.3.0-ha5d0236_0.conda
  sha256: 013669433eb447548f21c3c6b16b2ed64356f726b5f77c1b39d5ba17a8a4b8bc
  md5: a83f6a2fdc079e643237887a37460668
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.10.1,<9.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: MIT
  license_family: MIT
  size: 199544
  timestamp: 1730769112346
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/prometheus-cpp-1.3.0-h7938499_0.conda
  sha256: 9350d7bbc3982a732ff13a7fd17b585509e3b7d0191ac7b810cc3224868e3648
  md5: 10f4301290e51c49979ff98d1bdf2556
  depends:
  - libcurl >=8.10.1,<9.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: MIT
  license_family: MIT
  size: 211335
  timestamp: 1730769181127
- conda: https://conda.anaconda.org/conda-forge/linux-64/propcache-0.3.1-py313h8060acc_0.conda
  sha256: 49ec7b35291bff20ef8af0cf0a7dc1c27acf473bfbc121ccb816935b8bf33934
  md5: b62867739241368f43f164889b45701b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 53174
  timestamp: 1744525061828
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/propcache-0.3.1-py313h857f82b_0.conda
  sha256: e09f5bec992467e13a27020d9f86790f8bab4a9e278a6066359154701db712b0
  md5: 3c8d0e94c825ec08728e98a5448d8493
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 52793
  timestamp: 1744525116411
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pthread-stubs-0.4-h86ecc28_1002.conda
  sha256: 977dfb0cb3935d748521dd80262fe7169ab82920afd38ed14b7fee2ea5ec01ba
  md5: bb5a90c93e3bac3d5690acf76b4a6386
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 8342
  timestamp: 1726803319942
- conda: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.15-h3f63f65_0.conda
  sha256: 23c98a5000356e173568dc5c5770b53393879f946f3ace716bbdefac2a8b23d2
  md5: b11a4c6bf6f6f44e5e143f759ffa2087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 118488
  timestamp: 1736601364156
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pugixml-1.15-h6ef32b0_0.conda
  sha256: adc17205a87e064508d809fe5542b7cf49f9b9a458418f8448e2fc895fcd04f3
  md5: 53e14f45d38558aa2b9a15b07416e472
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 113424
  timestamp: 1737355438448
- conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
  sha256: d2377bb571932f2373f593b7b2fc3b9728dc6ae5b993b1b65d7f2c8bb39a0b49
  md5: 66b1fa9608d8836e25f9919159adc9c6
  depends:
  - __glibc >=2.17,<3.0.a0
  - dbus >=1.13.6,<2.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libiconv >=1.18,<2.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libsystemd0 >=257.4
  - libxcb >=1.17.0,<2.0a0
  constrains:
  - pulseaudio 17.0 *_1
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 764231
  timestamp: 1742507189208
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pulseaudio-client-17.0-h2f84921_1.conda
  sha256: 0294728d0a2fc0bdfbcfda98b7ada2d8bb420f76c944fa041ece4140858c2ee5
  md5: a617203ec445f510a3a8651810c735e0
  depends:
  - dbus >=1.13.6,<2.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libiconv >=1.18,<2.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libsystemd0 >=257.4
  - libxcb >=1.17.0,<2.0a0
  constrains:
  - pulseaudio 17.0 *_1
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 763814
  timestamp: 1742507234837
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-20.0.0-py313h78bf25f_0.conda
  sha256: 61b27da2d9512f2c0ddad4a86725fa1d04f482b6bad374f3535d8bf21ea4b84e
  md5: 6b8d388845ce750fe2ad8436669182f3
  depends:
  - libarrow-acero 20.0.0.*
  - libarrow-dataset 20.0.0.*
  - libarrow-substrait 20.0.0.*
  - libparquet 20.0.0.*
  - pyarrow-core 20.0.0 *_0_*
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 25773
  timestamp: 1746000973456
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyarrow-20.0.0-py313h1258fbd_0.conda
  sha256: 5af71765ed9350cab9a7cfcb0b8b1bd816aebf0897f00fd6997a7f2553dabee4
  md5: f0c8bc85eebf7b4a7df822a203eff21f
  depends:
  - libarrow-acero 20.0.0.*
  - libarrow-dataset 20.0.0.*
  - libarrow-substrait 20.0.0.*
  - libparquet 20.0.0.*
  - pyarrow-core 20.0.0 *_0_*
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 25885
  timestamp: 1746001238626
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-core-20.0.0-py313hc6b0d6e_0_cuda.conda
  sha256: d371c15d7ea75c590ecda6eceed1d6d049011cde353374dc6f2ac8ca1b15a83e
  md5: 1c4818094966a02d4136f73e9b873da8
  depends:
  - __cuda >=11.8
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0.* *cuda
  - libgcc
  - libgcc-ng >=12
  - libstdcxx
  - libstdcxx-ng >=12
  - libzlib >=1.3.1,<2.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - apache-arrow-proc * cuda
  - numpy >=1.21,<3
  license: Apache-2.0
  license_family: APACHE
  size: 5239080
  timestamp: 1746000927141
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyarrow-core-20.0.0-py313h656e22b_0_cpu.conda
  sha256: f1cdd035ee0a75c476d78b94e8bca6b4828f9d901fcf2b2e97b6d1eca295ac7d
  md5: f53ddb734b1c05706ef8a36d9c1941bb
  depends:
  - libarrow 20.0.0.* *cpu
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy >=1.21,<3
  - apache-arrow-proc * cpu
  license: Apache-2.0
  license_family: APACHE
  size: 4516436
  timestamp: 1746000779268
- conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-2.13.6-pyhc790b64_3.conda
  sha256: d429f6f255fbe49f09b9ae1377aa8cbc4d9285b8b220c17ae2ad9c4894c91317
  md5: 1594696beebf1ecb6d29a1136f859a74
  depends:
  - pybind11-global 2.13.6 *_3
  - python >=3.9
  constrains:
  - pybind11-abi ==4
  license: BSD-3-Clause
  license_family: BSD
  size: 186821
  timestamp: 1747935138653
- conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-2.13.6-pyh217bc35_3.conda
  sha256: c044cfcbe6ef0062d0960e9f9f0de5f8818cec84ed901219ff9994b9a9e57237
  md5: 730a5284e26d6bdb73332dafb26aec82
  depends:
  - __unix
  - python >=3.9
  constrains:
  - pybind11-abi ==4
  license: BSD-3-Clause
  license_family: BSD
  size: 180116
  timestamp: 1747934418811
- conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
  sha256: 79db7928d13fab2d892592223d7570f5061c192f27b9febd1a418427b719acc6
  md5: 12c566707c80111f9799308d9e265aef
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 110100
  timestamp: 1733195786147
- conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
  sha256: b92afb79b52fcf395fd220b29e0dd3297610f2059afac45298d44e00fcbf23b6
  md5: 513d3c262ee49b54a8fec85c5bc99764
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 95988
  timestamp: 1743089832359
- conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
  sha256: ba3b032fa52709ce0d9fd388f63d330a026754587a2f461117cac9ab73d8d0d8
  md5: 461219d1a5bd61342293efa2c0c90eac
  depends:
  - __unix
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 21085
  timestamp: 1733217331982
- conda: https://conda.anaconda.org/conda-forge/noarch/pysoundfile-0.13.1-pyhd8ed1ab_0.conda
  sha256: 09379ca52a8b119013acc325df3c609526094f923a9b3e3ac297404ff24dbba1
  md5: 41d15bdedf4987e95200d8a980294915
  depends:
  - cffi
  - libsndfile >=1.2
  - numpy
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 31155
  timestamp: 1737836367601
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
  build_number: 102
  sha256: c2cdcc98ea3cbf78240624e4077e164dc9d5588eefb044b4097c3df54d24d504
  md5: 89e07d92cf50743886f41638d58c4328
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  size: 33273132
  timestamp: 1750064035176
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
  build_number: 102
  sha256: 2eb3ce8b2acf036bd30d4d41cfb45766ad817e26479f18177cfb950c0af6f27b
  md5: ed5b16381ac28233a65c549a59d97b68
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-aarch64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  size: 33764400
  timestamp: 1750062474929
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
  sha256: a50052536f1ef8516ed11a844f9413661829aa083304dc624c5925298d078d79
  md5: 5ba79d7c71f03c678c8ead841f347d6e
  depends:
  - python >=3.9
  - six >=1.5
  license: Apache-2.0
  license_family: APACHE
  size: 222505
  timestamp: 1733215763718
- conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
  sha256: ac6cf618100c2e0cad1cabfe2c44bf4a944aa07bb1dc43abff73373351a7d079
  md5: 2eabcede0db21acee23c181db58b4128
  depends:
  - cpython 3.13.5.*
  - python_abi * *_cp313
  license: Python-2.0
  size: 47572
  timestamp: 1750062593102
- conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
  sha256: e8392a8044d56ad017c08fec2b0eb10ae3d1235ac967d0aab8bd7b41c4a5eaf0
  md5: 88476ae6ebd24f39261e0854ac244f33
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 144160
  timestamp: 1742745254292
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.5.0-py313h536fd9c_2.conda
  sha256: 24814b5a0162ef996c71d1eaad40b4be482c16a38a557dde93d3a413f1a96814
  md5: 992cc5204a44f3d2314c4c7912f4ac53
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - xxhash >=0.8.3,<0.8.4.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 23426
  timestamp: 1740594911070
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-xxhash-3.5.0-py313h6a51379_2.conda
  sha256: 64c57ca5d61df3f11c66e479d8618a6bd9d4a806f170ce6ca8dc6d1b6cd77abc
  md5: 1e1858923a9fa40a331c2bd3a087e913
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - xxhash >=0.8.3,<0.8.4.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 23485
  timestamp: 1740596012429
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
  build_number: 7
  sha256: 0595134584589064f56e67d3de1d8fcbb673a972946bce25fb593fb092fdcd97
  md5: e84b44e6300f1703cb25d29120c5b1d8
  constrains:
  - python 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 6988
  timestamp: 1745258852285
- conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-2.7.0-cuda126_mkl_py313_he20fe19_300.conda
  sha256: ed42fecc9dad7672e87167fbf27ebbacfe0b0a6950ee4ef19d1d4b65a7c948bd
  md5: 604e009302822518ceb496ca96e8798c
  depends:
  - __cuda
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - cuda-cudart >=12.6.77,<13.0a0
  - cuda-cupti >=12.6.80,<13.0a0
  - cuda-nvrtc >=12.6.85,<13.0a0
  - cuda-nvtx >=12.6.77,<13.0a0
  - cuda-version >=12.6,<13
  - cudnn >=9.8.0.87,<10.0a0
  - filelock
  - fsspec
  - jinja2
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libblas * *mkl
  - libcblas >=3.9.0,<4.0a0
  - libcublas >=12.6.4.1,<13.0a0
  - libcudss >=0.5.0.16,<0.5.1.0a0
  - libcufft >=11.3.0.4,<12.0a0
  - libcufile >=1.11.1.6,<2.0a0
  - libcurand >=10.3.7.77,<11.0a0
  - libcusolver >=11.7.1.2,<12.0a0
  - libcusparse >=12.5.4.2,<13.0a0
  - libgcc >=13
  - libmagma >=2.9.0,<2.9.1.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libtorch 2.7.0 cuda126_mkl_h99b69db_300
  - libuv >=1.50.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.4
  - mkl >=2024.2.2,<2025.0a0
  - nccl >=2.26.5.1,<3.0a0
  - networkx
  - numpy >=1.21,<3
  - optree >=0.13.0
  - pybind11
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - setuptools <76
  - sleef >=3.8,<4.0a0
  - sympy >=1.13.3
  - triton 3.3.0.*
  - typing_extensions >=4.10.0
  constrains:
  - pytorch-gpu 2.7.0
  - pytorch-cpu <0.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 29196008
  timestamp: 1746286432756
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pytorch-2.7.0-cuda126_generic_py313_h982c678_200.conda
  sha256: 25fbb141671bcc12c1fd41af687f7ac79d5ef780dc9a0711fddb9f910adf111c
  md5: b63065e2dd0a739a4e4770952aa632f9
  depends:
  - __cuda
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - cuda-cudart >=12.6.77,<13.0a0
  - cuda-cupti >=12.6.80,<13.0a0
  - cuda-nvrtc >=12.6.85,<13.0a0
  - cuda-nvtx >=12.6.77,<13.0a0
  - cuda-version >=12.6,<13
  - cudnn >=9.8.0.87,<10.0a0
  - filelock
  - fsspec
  - jinja2
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libcblas >=3.9.0,<4.0a0
  - libcublas >=12.6.4.1,<13.0a0
  - libcudss >=0.5.0.16,<0.5.1.0a0
  - libcufft >=11.3.0.4,<12.0a0
  - libcufile >=1.11.1.6,<2.0a0
  - libcurand >=10.3.7.77,<11.0a0
  - libcusolver >=11.7.1.2,<12.0a0
  - libcusparse >=12.5.4.2,<13.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libmagma >=2.9.0,<2.9.1.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libtorch 2.7.0 cuda126_generic_h5977fb4_200
  - libuv >=1.50.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.4
  - nccl >=2.26.5.1,<3.0a0
  - networkx
  - nomkl
  - numpy >=1.21,<3
  - optree >=0.13.0
  - pybind11
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - setuptools <76
  - sleef >=3.8,<4.0a0
  - sympy >=1.13.3
  - triton 3.3.0.*
  - typing_extensions >=4.10.0
  constrains:
  - pytorch-cpu <0.0a0
  - pytorch-gpu 2.7.0
  license: BSD-3-Clause
  license_family: BSD
  size: 29084279
  timestamp: 1746518477456
- conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-gpu-2.7.0-cuda126_mkl_ha999a5f_300.conda
  sha256: e1162a51e77491abae15f6b651ba8f064870181d57d40f9168747652d0f70cb0
  md5: 84ecafc34c6f8933c2c9b00204832e38
  depends:
  - pytorch 2.7.0 cuda*_mkl*300
  license: BSD-3-Clause
  license_family: BSD
  size: 47219
  timestamp: 1746288556375
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pytorch-gpu-2.7.0-cuda126_generic_hbf71451_200.conda
  sha256: c3b1e7b4dddf85b68609e719923b6ce48cb46d9aa96ba7bfa5d660fcdddd7f1c
  md5: 85df7e75e213a38e3e5c1935435479cd
  depends:
  - pytorch 2.7.0 cuda*_generic*200
  license: BSD-3-Clause
  license_family: BSD
  size: 47274
  timestamp: 1746523575482
- conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
  sha256: 8d2a8bf110cc1fc3df6904091dead158ba3e614d8402a83e51ed3a8aa93cdeb0
  md5: bc8e3267d44011051f2eb14d22fb0960
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 189015
  timestamp: 1742920947249
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py313h8060acc_2.conda
  sha256: 6826217690cfe92d6d49cdeedb6d63ab32f51107105d6a459d30052a467037a0
  md5: 50992ba61a8a1f8c2d346168ae1c86df
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 205919
  timestamp: 1737454783637
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyyaml-6.0.2-py313h857f82b_2.conda
  sha256: b00d3ad06ee129b66743bfffd0781b4d5fdd9bcb5b4dd20d8f10a4c74da2001b
  md5: 0c4fb65bc2e7e2e7a209a5aa15d5f1f1
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 198985
  timestamp: 1737454849472
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py313h8e95178_0.conda
  sha256: 6446721c43ba540c02ced4dde564f5a9a0131e40aa406e8af6313084c4a2024f
  md5: c912a00e5cb59357ef55b7930a48cf48
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 384549
  timestamp: 1749898593849
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyzmq-27.0.0-py313h6e72e74_0.conda
  sha256: dbe4072369da72df5017204457d3dbe1c7a302ac8aca9a4fe52015f4b6ad2c2d
  md5: d901488aaa8c5f0dbdd2d5848796c9ff
  depends:
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 381357
  timestamp: 1749900828011
- conda: https://conda.anaconda.org/conda-forge/linux-64/qhull-2020.2-h434a139_5.conda
  sha256: 776363493bad83308ba30bcb88c2552632581b143e8ee25b1982c8c743e73abc
  md5: 353823361b1d27eb3960efb076dfcaf6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LicenseRef-Qhull
  size: 552937
  timestamp: 1720813982144
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/qhull-2020.2-h70be974_5.conda
  sha256: 49f777bdf3c5e030a8c7b24c58cdfe9486b51d6ae0001841079a3228bdf9fb51
  md5: bb138086d938e2b64f5f364945793ebf
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LicenseRef-Qhull
  size: 554571
  timestamp: 1720813941183
- conda: https://conda.anaconda.org/conda-forge/linux-64/rdma-core-57.0-h5888daf_0.conda
  sha256: fbb4599ba969c49d2280c84af196c514c49a3ad1529c693f4b6ac6c705998ec8
  md5: e5be997517f19a365b8b111b888be426
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libnl >=3.11.0,<4.0a0
  - libstdcxx >=13
  - libsystemd0 >=257.4
  - libudev1 >=257.4
  license: Linux-OpenIB
  license_family: BSD
  size: 1238038
  timestamp: 1745325325058
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/rdma-core-57.0-h1d056c8_0.conda
  sha256: b6fd3d56e7a55edc0bf13ba518cc8f318f4c4415d1a16bfe97e32723ca8828c7
  md5: d52b528775d2f2061e4cf599295c2ba6
  depends:
  - libgcc >=13
  - libnl >=3.11.0,<4.0a0
  - libstdcxx >=13
  - libsystemd0 >=257.4
  - libudev1 >=257.4
  license: Linux-OpenIB
  license_family: BSD
  size: 1289713
  timestamp: 1745325369363
- conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2024.07.02-h9925aae_3.conda
  sha256: 66d34e3b4881f856486d11914392c585713100ca547ccfc0947f3a4765c2c486
  md5: 6f445fb139c356f903746b2b91bbe786
  depends:
  - libre2-11 2024.07.02 hba17884_3
  license: BSD-3-Clause
  license_family: BSD
  size: 26811
  timestamp: 1741121137599
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/re2-2024.07.02-haa97905_3.conda
  sha256: 38de5a807b423b11b86a90991fcd1dca59c076ac556c5ac3d53da1cd8076932a
  md5: 923f30767c0008039b95fc2b01c5e0e1
  depends:
  - libre2-11 2024.07.02 h201e9ed_3
  license: BSD-3-Clause
  license_family: BSD
  size: 26874
  timestamp: 1741121224344
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
  sha256: 54bed3a3041befaa9f5acde4a37b1a02f44705b7796689574bcf9d7beaad2959
  md5: c0f08fc2737967edde1a272d4bf41ed9
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 291806
  timestamp: 1740380591358
- conda: https://conda.anaconda.org/conda-forge/linux-64/regex-2024.11.6-py313h536fd9c_0.conda
  sha256: 89a311b8f129e19493773788da338cdf342495f8482248142131a22422bf63ea
  md5: 2b80e596f4964f14b2317f8439a9df3e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Python-2.0
  license_family: PSF
  size: 403874
  timestamp: 1730952346266
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/regex-2024.11.6-py313h31d5739_0.conda
  sha256: bcd648a9140d9f51c2e86b1e4c9564d05eb5ed01860fbf5b304559483cb1342a
  md5: 3b96da4e2447fea250238a0dbd452208
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: Python-2.0
  license_family: PSF
  size: 400022
  timestamp: 1730952356169
- conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
  sha256: 9866aaf7a13c6cfbe665ec7b330647a0fb10a81e6f9b8fee33642232a1920e18
  md5: f6082eae112814f1447b56a5e1f6ed05
  depends:
  - certifi >=2017.4.17
  - charset-normalizer >=2,<4
  - idna >=2.5,<4
  - python >=3.9
  - urllib3 >=1.21.1,<3
  constrains:
  - chardet >=3.0.2,<6
  license: Apache-2.0
  license_family: APACHE
  size: 59407
  timestamp: 1749498221996
- conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.5.21-h7ab7c64_0.conda
  sha256: c8b252398b502a5cc6ea506fd2fafe7e102e7c9e2ef48b7813566e8a72ce2205
  md5: 28b5a7895024a754249b2ad7de372faa
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 358164
  timestamp: 1749095480268
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/s2n-1.5.21-hcc03fc0_0.conda
  sha256: 47f76a9d703dacb1030ab6d353044a0beca024ebd57cce08cb03fbf8e0f330c1
  md5: a4f53042f314b5abc59c60b8ce1690d6
  depends:
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 350372
  timestamp: 1749095522673
- conda: https://conda.anaconda.org/conda-forge/linux-64/safetensors-0.5.3-py313h920b4c0_0.conda
  sha256: 13a54e151a12179f236e2c6d37c1c222cb975b68e641701f1d524fe510760442
  md5: 8567744ae20dfb943979146e3a45d812
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 432818
  timestamp: 1740651691391
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/safetensors-0.5.3-py313h8aa417a_0.conda
  sha256: 2abd7b89bcec8fa1d8c651c018f7ce43d3b3ecebc83e7826ad6c6290eef19dc7
  md5: 2810b16c310d0586a37207b880ac4287
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 418305
  timestamp: 1740651676124
- conda: https://conda.anaconda.org/conda-forge/linux-64/scikit-learn-1.7.0-py313h8ef605b_1.conda
  sha256: c98af964f400284cc629826ff4e76ab5e3993644c8e789b1bba0b2d74505d3e5
  md5: c1d43ff645df6f6423bc74601581fd92
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - joblib >=1.2.0
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.22.0
  - numpy >=1.23,<3
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - scipy >=1.8.0
  - threadpoolctl >=3.1.0
  license: BSD-3-Clause
  license_family: BSD
  size: 10448220
  timestamp: 1749488497680
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/scikit-learn-1.7.0-py313h2a234e8_1.conda
  sha256: 5e8fd29c90e721c7bf66d43db13222d6571d6a5e9390ab33e4fa20e73d672f02
  md5: 1ac756af319ee8500027fe6a981c4337
  depends:
  - _openmp_mutex >=4.5
  - joblib >=1.2.0
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.22.0
  - numpy >=1.23,<3
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - scipy >=1.8.0
  - threadpoolctl >=3.1.0
  license: BSD-3-Clause
  license_family: BSD
  size: 10088154
  timestamp: 1749488305820
- conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.15.2-py313h86fcf2b_0.conda
  sha256: c3052b04397f76188611c8d853ac749986874d6a5869292b92ebae7ce093c798
  md5: ca68acd9febc86448eeed68d0c6c8643
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libgfortran
  - libgfortran5 >=13.3.0
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - numpy <2.5
  - numpy >=1.21,<3
  - numpy >=1.23.5
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 17233404
  timestamp: 1739791996980
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/scipy-1.15.2-py313hfdb6400_0.conda
  sha256: def4915a63c6cf406ad42214a28409910e3cc445edc7a524ffcb0efe5c077bf5
  md5: 2ec685deaeb5e60f15dc4d291065c104
  depends:
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libgfortran
  - libgfortran5 >=13.3.0
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - numpy <2.5
  - numpy >=1.21,<3
  - numpy >=1.23.5
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 17065470
  timestamp: 1739792960657
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
  sha256: 7cd82ca1d1989de6ac28e72ba0bfaae1c055278f931b0c7ef51bb1abba3ddd2f
  md5: 91f8537d64c4d52cbbb2910e8bd61bd2
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - sdl3 >=3.2.10,<4.0a0
  - libgl >=1.7.0,<2.0a0
  - libegl >=1.7.0,<2.0a0
  license: Zlib
  size: 587053
  timestamp: 1745799881584
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2-2.32.54-h5ad3122_0.conda
  sha256: d83c13fc35ed447d186150d32b8bc48bdd73a047280ba6e06f151d4cce52639d
  md5: 6b38021cb802b4e5bede7fe38c547883
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - libegl >=1.7.0,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - sdl3 >=3.2.10,<4.0a0
  license: Zlib
  size: 597383
  timestamp: 1745799910298
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
  sha256: 7fe5ff84801d1ad0713efbb1a9c39c3c4245ccee5586bd62fc4604d0f23ce0df
  md5: c3ab38fdbcf36625620c9a4df786320a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - dbus >=1.16.2,<2.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - libxkbcommon >=1.10.0,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - wayland >=1.23.1,<2.0a0
  - libusb >=1.0.29,<2.0a0
  - libudev1 >=257.6
  - libegl >=1.7.0,<2.0a0
  - liburing >=2.10,<2.11.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxscrnsaver >=1.2.4,<2.0a0
  - libdrm >=2.4.124,<2.5.0a0
  - libunwind >=1.6.2,<1.7.0a0
  license: Zlib
  size: 1941645
  timestamp: 1748911618893
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl3-3.2.16-h7e2c5d6_0.conda
  sha256: 782c6ac4f96e2b75cf55afdd223ffd043fc459a5a7c7b460c4077764733d5bd9
  md5: 37fbfe4c4baa10eca13823b535ec5d1a
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - libxkbcommon >=1.10.0,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - libunwind >=1.6.2,<1.7.0a0
  - libusb >=1.0.29,<2.0a0
  - libudev1 >=257.6
  - wayland >=1.23.1,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - dbus >=1.16.2,<2.0a0
  - liburing >=2.10,<2.11.0a0
  - libegl >=1.7.0,<2.0a0
  - libdrm >=2.4.124,<2.5.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  license: Zlib
  size: 1899812
  timestamp: 1748911660322
- conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-75.8.2-pyhff2d567_0.conda
  sha256: 91d664ace7c22e787775069418daa9f232ee8bafdd0a6a080a5ed2395a6fa6b2
  md5: 9bddfdbf4e061821a1a443f93223be61
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 777736
  timestamp: 1740654030775
- conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
  sha256: 41db0180680cc67c3fa76544ffd48d6a5679d96f4b71d7498a759e94edc9a2db
  md5: a451d576819089b0d672f18768be0f65
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 16385
  timestamp: 1733381032766
- conda: https://conda.anaconda.org/conda-forge/linux-64/sleef-3.8-h1b44611_0.conda
  sha256: c998d5a29848ce9ff1c53ba506e7d01bbd520c39bbe72e2fb7cdf5a53bad012f
  md5: aec4dba5d4c2924730088753f6fa164b
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libstdcxx >=13
  license: BSL-1.0
  size: 1920152
  timestamp: 1738089391074
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sleef-3.8-h8fb0607_0.conda
  sha256: 86a19e4457bbc20d6c52a7cf0a010b2d99b704e78ae396f9fae4f4da9ae42cf3
  md5: 76647a886ead58ea5d82990117aafce2
  depends:
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libstdcxx >=13
  license: BSL-1.0
  size: 1172391
  timestamp: 1738090891356
- conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
  sha256: ec91e86eeb2c6bbf09d51351b851e945185d70661d2ada67204c9a6419d282d3
  md5: 3b3e64af585eadfb52bb90b553db5edf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 42739
  timestamp: 1733501881851
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/snappy-1.2.1-hd4fb6f5_1.conda
  sha256: c4a07ae5def8d55128f25a567a296ef9d7bf99a3bc79d46bd5160c076a5f50af
  md5: 2fcc6cd1e5550deb509073fd2e6693e1
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 43032
  timestamp: 1733501964775
- conda: https://conda.anaconda.org/conda-forge/linux-64/soxr-0.1.3-h0b41bf4_3.conda
  sha256: 141e3364d26f162bfeae8491787c0d8796ada6c29a8cd567c1cd17c3c6b418f9
  md5: e8d261785be19b1575d23ccbbeae4ddd
  depends:
  - _openmp_mutex >=4.5
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  size: 131370
  timestamp: 1674059502792
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/soxr-0.1.3-hb4cce97_3.conda
  sha256: 58b006195863f200efdb5785a4a954f8d0284b5984703d4d74ec4c09343e58e2
  md5: 13e1d5bd316dec4077351f4246b9b2a8
  depends:
  - _openmp_mutex >=4.5
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  size: 111209
  timestamp: 1674059588618
- conda: https://conda.anaconda.org/conda-forge/linux-64/soxr-python-0.5.0.post1-py313h46c70d0_1.conda
  sha256: f659c9363ddbbf6920631ee9d2e25f9c11a40ea989cf8e5ff0e3670771f752c7
  md5: 64f2f317f3b934762b7d95db094d69dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - numpy
  - python >=3.13.0rc1,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - soxr >=0.1.3,<0.1.4.0a0
  license: LGPL-2.1-or-later
  size: 88203
  timestamp: 1725345840432
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/soxr-python-0.5.0.post1-py313hb6a6212_1.conda
  sha256: 4f7043d547e3e547c61a093e42cecfc6211cc45e1afc880bf264f1b0b9668b3a
  md5: 4c6a8b79d1ecf2491b32d8effde35c44
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - numpy
  - python >=3.13.0rc1,<3.14.0a0
  - python >=3.13.0rc1,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - soxr >=0.1.3,<0.1.4.0a0
  license: LGPL-2.1-or-later
  size: 83490
  timestamp: 1725345848551
- conda: https://conda.anaconda.org/conda-forge/linux-64/standard-aifc-3.13.0-py313h78bf25f_1.conda
  sha256: 24cba4ffb8e7a46f294fb1a6f1d530384fb6be92701b50cb8584fc2a53608580
  md5: cdecfeefb4b8f01fbb463e5ddbb94369
  depends:
  - audioop-lts
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - standard-chunk
  license: BSD-4-Clause
  size: 28940
  timestamp: 1749243174022
- conda: https://conda.anaconda.org/conda-forge/noarch/standard-chunk-3.13.0-pyhd8ed1ab_0.conda
  sha256: 2a06b01bae0f58bde0cd02bee9e3aab6e33fc15e4ad2670e2809436f93bd6318
  md5: baaf1cc992662d1defb4aa0a60743d74
  depends:
  - python >=3.9
  license: BSD-4-Clause
  size: 11125
  timestamp: 1741793582002
- conda: https://conda.anaconda.org/conda-forge/linux-64/standard-sunau-3.13.0-py313h78bf25f_1.conda
  sha256: a2d0d434058fd9cddca19901623f7e63bc3108bd2d5eccbc2c49a1762e1fd226
  md5: f85e5838ece4e9150893a9cfcd1f3e7b
  depends:
  - audioop-lts
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-4-Clause
  size: 20617
  timestamp: 1749160894177
- conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
  sha256: fb4b97a3fd259eff4849b2cfe5678ced0c5792b697eb1f7bcd93a4230e90e80e
  md5: 0096882bd623e6cc09e8bf920fc8fb47
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 2750235
  timestamp: 1742907589246
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/svt-av1-3.0.2-h5ad3122_0.conda
  sha256: 6d2ac9e4f68355ba3b42395054a7558b9eb6bcf3d70e91bb99ada1450a74d2f6
  md5: 4fafb3aafa73a875312cb4a1099d2a46
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 1975547
  timestamp: 1742910351387
- conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
  sha256: 09d3b6ac51d437bc996ad006d9f749ca5c645c1900a854a6c8f193cbd13f03a8
  md5: 8c09fac3785696e1c477156192d64b91
  depends:
  - __unix
  - cpython
  - gmpy2 >=2.0.8
  - mpmath >=0.19
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 4616621
  timestamp: 1745946173026
- conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.13.0-hceb3a55_1.conda
  sha256: 65463732129899770d54b1fbf30e1bb82fdebda9d7553caf08d23db4590cd691
  md5: ba7726b8df7b9d34ea80e82b097a4893
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libhwloc >=2.11.2,<2.11.3.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  size: 175954
  timestamp: 1732982638805
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tbb-2022.1.0-hf6e3e71_0.conda
  sha256: 3dea624f5495c4cfe1bd5a35d67f5995c3a4cde42024ec57855ddab502e1ea3c
  md5: 0d08f8f53a51b2dfbcda8ebe3be28103
  depends:
  - libgcc >=13
  - libhwloc >=2.11.2,<2.11.3.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  size: 144738
  timestamp: 1743581521035
- conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
  sha256: 6016672e0e72c4cf23c0cf7b1986283bd86a9c17e8d319212d78d8e9ae42fdfd
  md5: 9d64911b31d57ca443e9f1e36b04385f
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 23869
  timestamp: 1741878358548
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3285204
  timestamp: 1748387766691
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
  sha256: 46e10488e9254092c655257c18fcec0a9864043bdfbe935a9fbf4fb2028b8514
  md5: 2562c9bfd1de3f9c590f0fe53858d85c
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3342845
  timestamp: 1748393219221
- conda: https://conda.anaconda.org/conda-forge/linux-64/tokenizers-0.21.2-py313h1191936_0.conda
  sha256: c26cb6e6d9315d8a358643f5ca7ec684a90f54adabde2a0546e73e9b0ac5102c
  md5: 79a6cabf80a181cd4371dea204bf470a
  depends:
  - __glibc >=2.17,<3.0.a0
  - huggingface_hub >=0.16.4,<1.0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.5.0,<4.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 2370910
  timestamp: 1750798328425
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tokenizers-0.21.2-py313h5e73428_0.conda
  sha256: 6b7618bde6edaf72e77d57fd5fcdfc5d1c748c45cd3a7d186b4fd8c28794b8f0
  md5: 9460c99dd589c77c0225e62d0e3d58ee
  depends:
  - huggingface_hub >=0.16.4,<1.0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.5.0,<4.0a0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 2429921
  timestamp: 1750798415451
- conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py313h536fd9c_0.conda
  sha256: 282c9c3380217119c779fc4c432b0e4e1e42e9a6265bfe36b6f17f6b5d4e6614
  md5: e9434a5155db25c38ade26f71a2f5a48
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 873269
  timestamp: 1748003477089
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tornado-6.5.1-py313h6a51379_0.conda
  sha256: 819497d044a23d6d69fa09aaf7f4d59b6c9db6443d6e32691ccb3361849e3979
  md5: efd003285041d23e604630b18f24b9dd
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 876988
  timestamp: 1748005374481
- conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
  sha256: 11e2c85468ae9902d24a27137b6b39b4a78099806e551d390e394a8c34b48e40
  md5: 9efbfdc37242619130ea42b1cc4ed861
  depends:
  - colorama
  - python >=3.9
  license: MPL-2.0 or MIT
  size: 89498
  timestamp: 1735661472632
- conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
  sha256: f39a5620c6e8e9e98357507262a7869de2ae8cc07da8b7f84e517c9fd6c2b959
  md5: 019a7385be9af33791c989871317e1ed
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 110051
  timestamp: 1733367480074
- conda: https://conda.anaconda.org/conda-forge/noarch/transformers-4.53.0-pyhd8ed1ab_0.conda
  sha256: d3526ea2617dc8650a2c7fd01d7568cda7a709472eb6881e08a4d8e4d68124db
  md5: 42c5cc096057a22b882b8fa92c5e8883
  depends:
  - datasets !=2.5.0
  - filelock
  - huggingface_hub >=0.30.0,<1.0
  - numpy >=1.17
  - packaging >=20.0
  - python >=3.9
  - pyyaml >=5.1
  - regex !=2019.12.17
  - requests
  - safetensors >=0.4.1
  - tokenizers >=0.21,<0.22
  - tqdm >=4.27
  license: Apache-2.0
  license_family: APACHE
  size: 3916160
  timestamp: 1750964780590
- conda: https://conda.anaconda.org/conda-forge/linux-64/triton-3.3.0-cuda126py313hdd23915_1.conda
  sha256: 79e6878c8455b938d2802e7e01bf5d14c6b3698bde08cc942dc22171b38e7cf6
  md5: 26c4262aab423f4402928d66f4e677ce
  depends:
  - python
  - setuptools
  - cuda-nvcc-tools
  - cuda-cuobjdump
  - cuda-cudart
  - cuda-cupti
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - cuda-version >=12.6,<13
  - cuda-cupti >=12.6.80,<13.0a0
  - zstd >=1.5.7,<1.6.0a0
  - python_abi 3.13.* *_cp313
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 163143152
  timestamp: 1746164209732
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/triton-3.3.0-cuda126py313h3a9e705_1.conda
  sha256: 31ca3b85263cc5a4f0e1597f2a21c31d666f591fd2ab59e676d0a57398be7c34
  md5: 3eb05cb1010bc814db97142d683ba910
  depends:
  - python
  - setuptools
  - cuda-nvcc-tools
  - cuda-cuobjdump
  - cuda-cudart
  - cuda-cupti
  - libstdcxx >=13
  - libgcc >=13
  - python 3.13.* *_cp313
  - cuda-version >=12.6,<13
  - libzlib >=1.3.1,<2.0a0
  - cuda-cupti >=12.6.80,<13.0a0
  - python_abi 3.13.* *_cp313
  - zstd >=1.5.7,<1.6.0a0
  license: MIT
  license_family: MIT
  size: 172138867
  timestamp: 1746164353448
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.0-h32cad80_0.conda
  sha256: b8cabfa54432b0f124c0af6b6facdf8110892914fa841ac2e80ab65ac52c1ba4
  md5: a1cdd40fc962e2f7944bc19e01c7e584
  depends:
  - typing_extensions ==4.14.0 pyhe01879c_0
  license: PSF-2.0
  license_family: PSF
  size: 90310
  timestamp: 1748959427551
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
  sha256: 8561db52f278c5716b436da6d4ee5521712a49e8f3c70fcae5350f5ebb4be41c
  md5: 2adcd9bb86f656d3d43bf84af59a1faf
  depends:
  - python >=3.9
  - python
  license: PSF-2.0
  license_family: PSF
  size: 50978
  timestamp: 1748959427551
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
  sha256: 4fb9789154bd666ca74e428d973df81087a697dbb987775bc3198d2215f240f8
  md5: 436c165519e140cb08d246a4472a9d6a
  depends:
  - brotli-python >=1.0.9
  - h2 >=4,<5
  - pysocks >=1.5.6,<2.0,!=1.5.7
  - python >=3.9
  - zstandard >=0.18.0
  license: MIT
  license_family: MIT
  size: 101735
  timestamp: 1750271478254
- conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
  sha256: 73d809ec8056c2f08e077f9d779d7f4e4c2b625881cad6af303c33dc1562ea01
  md5: a37843723437ba75f42c9270ffe800b1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 321099
  timestamp: 1745806602179
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/wayland-1.23.1-h698ed42_1.conda
  sha256: b4f36d5acd06945d0fe4da61ec469fc0f0948458172d13013dabb30854f1ccd3
  md5: 229b00f81a229af79547a7e4776ccf6e
  depends:
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 324694
  timestamp: 1745806658759
- conda: https://conda.anaconda.org/conda-forge/noarch/wayland-protocols-1.45-hd8ed1ab_0.conda
  sha256: ****************************************************************
  md5: 6db9be3b67190229479780eeeee1b35b
  license: MIT
  license_family: MIT
  size: 138011
  timestamp: 1749836220507
- conda: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
  sha256: 175315eb3d6ea1f64a6ce470be00fa2ee59980108f246d3072ab8b977cb048a5
  md5: 6c99772d483f566d59e25037fea2c4b1
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  size: 897548
  timestamp: 1660323080555
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x264-1!164.3095-h4e544f5_2.tar.bz2
  sha256: b48f150db8c052c197691c9d76f59e252d3a7f01de123753d51ebf2eed1cf057
  md5: 0efaf807a0b5844ce5f605bd9b668281
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  size: 1000661
  timestamp: 1660324722559
- conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
  sha256: 76c7405bcf2af639971150f342550484efac18219c0203c5ee2e38b8956fe2a0
  md5: e7f6ed84d4623d52ee581325c1587a6b
  depends:
  - libgcc-ng >=10.3.0
  - libstdcxx-ng >=10.3.0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 3357188
  timestamp: 1646609687141
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x265-3.5-hdd96247_3.tar.bz2
  sha256: cb2227f2441499900bdc0168eb423d7b2056c8fd5a3541df4e2d05509a88c668
  md5: 786853760099c74a1d4f0da98dd67aea
  depends:
  - libgcc-ng >=10.3.0
  - libstdcxx-ng >=10.3.0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 1018181
  timestamp: 1646610147365
- conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
  sha256: a5d4af601f71805ec67403406e147c48d6bad7aaeae92b0622b7e2396842d3fe
  md5: 397a013c2dc5145a70737871aaa87e98
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  size: 392406
  timestamp: 1749375847832
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xkeyboard-config-2.45-h86ecc28_0.conda
  sha256: 730ff2f6fbfecce94db54bbf3f1ae0ce79c54b6abc089f8a65a041525228d454
  md5: 01251d1503a253e39be4fa9bcf447d63
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  size: 392754
  timestamp: 1749375869926
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
  sha256: c12396aabb21244c212e488bbdc4abcdef0b7404b15761d9329f5a4a39113c4b
  md5: fb901ff28063514abb6046c9ec2c4a45
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 58628
  timestamp: 1734227592886
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libice-1.1.2-h86ecc28_0.conda
  sha256: a2ba1864403c7eb4194dacbfe2777acf3d596feae43aada8d1b478617ce45031
  md5: c8d8ec3e00cd0fd8a231789b91a7c5b7
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 60433
  timestamp: 1734229908988
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
  sha256: 277841c43a39f738927145930ff963c5ce4c4dacf66637a3d95d802a64173250
  md5: 1c74ff8c35dcadf952a16f752ca5aa49
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  size: 27590
  timestamp: 1741896361728
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libsm-1.2.6-h0808dbd_0.conda
  sha256: b86a819cd16f90c01d9d81892155126d01555a20dabd5f3091da59d6309afd0a
  md5: 2d1409c50882819cb1af2de82e2b7208
  depends:
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  size: 28701
  timestamp: 1741897678254
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
  sha256: 51909270b1a6c5474ed3978628b341b4d4472cd22610e5f22b506855a5e20f67
  md5: db038ce880f100acc74dba10302b5630
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  size: 835896
  timestamp: 1741901112627
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libx11-1.8.12-hca56bd8_0.conda
  sha256: 452977d8ad96f04ec668ba74f46e70a53e00f99c0e0307956aeca75894c8131d
  md5: 3df132f0048b9639bc091ef22937c111
  depends:
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  size: 864850
  timestamp: 1741901264068
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxau-1.0.12-h86ecc28_0.conda
  sha256: 7829a0019b99ba462aece7592d2d7f42e12d12ccd3b9614e529de6ddba453685
  md5: d5397424399a66d33c80b1f2345a36a6
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 15873
  timestamp: 1734230458294
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
  sha256: 832f538ade441b1eee863c8c91af9e69b356cd3e9e1350fff4fe36cc573fc91a
  md5: 2ccd714aa2242315acaf0a67faea780b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  size: 32533
  timestamp: 1730908305254
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxcursor-1.2.3-h86ecc28_0.conda
  sha256: c5d3692520762322a9598e7448492309f5ee9d8f3aff72d787cf06e77c42507f
  md5: f2054759c2203d12d0007005e1f1296d
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.9,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  size: 34596
  timestamp: 1730908388714
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxdmcp-1.1.5-h57736b2_0.conda
  sha256: efcc150da5926cf244f757b8376d96a4db78bc15b8d90ca9f56ac6e75755971f
  md5: 25a5a7b797fe6e084e04ffe2db02fc62
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 20615
  timestamp: 1727796660574
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
  sha256: da5dc921c017c05f38a38bd75245017463104457b63a1ce633ed41f214159c14
  md5: febbab7d15033c913d53c7a2c102309d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 50060
  timestamp: 1727752228921
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxext-1.3.6-h57736b2_0.conda
  sha256: 8e216b024f52e367463b4173f237af97cf7053c77d9ce3e958bc62473a053f71
  md5: bd1e86dd8aa3afd78a4bfdb4ef918165
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.9,<2.0a0
  license: MIT
  license_family: MIT
  size: 50746
  timestamp: 1727754268156
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
  sha256: 2fef37e660985794617716eb915865ce157004a4d567ed35ec16514960ae9271
  md5: 4bdb303603e9821baf5fe5fdff1dc8f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 19575
  timestamp: 1727794961233
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxfixes-6.0.1-h57736b2_0.conda
  sha256: f5c71e0555681a82a65c483374b91d91b2cb9a9903b3a22ddc00f36719fce549
  md5: 78f8715c002cc66991d7c11e3cf66039
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.9,<2.0a0
  license: MIT
  license_family: MIT
  size: 20289
  timestamp: 1727796500830
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
  sha256: 044c7b3153c224c6cedd4484dd91b389d2d7fd9c776ad0f4a34f099b3389f4a1
  md5: 96d57aba173e878a2089d5638016dc5e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 33005
  timestamp: 1734229037766
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxrender-0.9.12-h86ecc28_0.conda
  sha256: ffd77ee860c9635a28cfda46163dcfe9224dc6248c62404c544ae6b564a0be1f
  md5: ae2c2dd0e2d38d249887727db2af960e
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 33649
  timestamp: 1734229123157
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
  sha256: 58e8fc1687534124832d22e102f098b5401173212ac69eb9fd96b16a3e2c8cb2
  md5: 303f7a0e9e0cd7d250bb6b952cecda90
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  license: MIT
  license_family: MIT
  size: 14412
  timestamp: 1727899730073
- conda: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.3-hb47aa4a_0.conda
  sha256: 08e12f140b1af540a6de03dd49173c0e5ae4ebc563cabdd35ead0679835baf6f
  md5: 607e13a8caac17f9a664bcab5302ce06
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 108219
  timestamp: 1746457673761
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xxhash-0.8.3-hd794028_0.conda
  sha256: db8cc7186ecb1cf4cb92977822ad17698fa2cd5fc87935de2afd9e99d2cbb507
  md5: f2accdfbd632e2be9a63bed23cb08045
  depends:
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 105762
  timestamp: 1746457675564
- conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
  sha256: a4e34c710eeb26945bdbdaba82d3d74f60a78f54a874ec10d373811a5d217535
  md5: 4cb3ad778ec2d5a7acbdf254eb1c42ae
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 89141
  timestamp: 1641346969816
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/yaml-0.2.5-hf897c2e_2.tar.bz2
  sha256: 8bc601d6dbe249eba44b3c456765265cd8f42ef1e778f8df9b0c9c88b8558d7e
  md5: b853307650cb226731f653aa623936a4
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 92927
  timestamp: 1641347626613
- conda: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.20.1-py313h8060acc_0.conda
  sha256: 4254322f6ed246ee3ddd6d4d80173ef44f8f82f3c2d31d9d23ce33849247ad94
  md5: b3659ec61a97eb6f64aeca04effb999d
  depends:
  - __glibc >=2.17,<3.0.a0
  - idna >=2.0
  - libgcc >=13
  - multidict >=4.0
  - propcache >=0.2.1
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 149483
  timestamp: 1749554958820
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/yarl-1.20.1-py313h857f82b_0.conda
  sha256: 7e53fb851e74fceee39156102e9458887c25827ef01b6e0ff55d948b2d819a11
  md5: 2b22585bfea48e8ca3c66e3f8e0f5917
  depends:
  - idna >=2.0
  - libgcc >=13
  - multidict >=4.0
  - propcache >=0.2.1
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 149513
  timestamp: 1749555070974
- conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
  sha256: a4dc72c96848f764bb5a5176aa93dd1e9b9e52804137b99daeebba277b31ea10
  md5: 3947a35e916fcc6b9825449affbf4214
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  size: 335400
  timestamp: 1731585026517
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zeromq-4.3.5-h5efb499_7.conda
  sha256: a6003096dc0570a86492040ba32b04ce7662b159600be2252b7a0dfb9414e21c
  md5: f2f3282559a4b87b7256ecafb4610107
  depends:
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  size: 371419
  timestamp: 1731589490850
- conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
  sha256: 7560d21e1b021fd40b65bfb72f67945a3fcb83d78ad7ccf37b8b3165ec3b68ad
  md5: df5e78d904988eb55042c0c97446079f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 22963
  timestamp: 1749421737203
- conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
  sha256: 5d7c0e5f0005f74112a34a7425179f4eb6e73c92f5d109e6af4ddeca407c92ab
  md5: c9f075ab2f33b3bbee9e62d4ad0a6cd8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib 1.3.1 hb9d3cd8_2
  license: Zlib
  license_family: Other
  size: 92286
  timestamp: 1727963153079
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zlib-1.3.1-h86ecc28_2.conda
  sha256: b4f649aa3ecdae384d5dad7074e198bff120edd3dfb816588e31738fc6d627b1
  md5: bc230abb5d21b63ff4799b0e75204783
  depends:
  - libgcc >=13
  - libzlib 1.3.1 h86ecc28_2
  license: Zlib
  license_family: Other
  size: 95582
  timestamp: 1727963203597
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py313h536fd9c_2.conda
  sha256: ea9c542ef78c9e3add38bf1032e8ca5d18703114db353f6fca5c498f923f8ab8
  md5: a026ac7917310da90a98eac2c782723c
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.11
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 736909
  timestamp: 1745869790689
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstandard-0.23.0-py313h31d5739_2.conda
  sha256: 421164e0f3f95720030c14f9a337212da33de48421f4d9ef9ef80524a57c452e
  md5: 08212d1111359076fb5147223f4f373b
  depends:
  - cffi >=1.11
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 703643
  timestamp: 1745869847897
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 567578
  timestamp: 1742433379869
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstd-1.5.7-hbcf94c1_2.conda
  sha256: 0812e7b45f087cfdd288690ada718ce5e13e8263312e03b643dd7aa50d08b51b
  md5: 5be90c5a3e4b43c53e38f50a85e11527
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 551176
  timestamp: 1742433378347
