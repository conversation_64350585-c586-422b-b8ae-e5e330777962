[project]
authors = ["Modular <<EMAIL>>"]
channels = ["conda-forge", "https://conda.modular.com/max-nightly/"]
description = "Add a short description here"
name = "hello-magic"
platforms = ["linux-64", "linux-aarch64", "osx-arm64"]
version = "0.1.0"

[tasks]
hello = "mojo hello.mojo"
dev-server = "fastapi dev main.py"
format = "mojo format ."

[dependencies]
max = "*"
python = ">=3.9,<3.14"
pytorch = ">=2.5.1,<3"
httpx = "*"

[pypi-dependencies]
fastapi = ">=0.115.0"

[feature.test.tasks]
test = "pytest"

[feature.test.pypi-dependencies]
pytest = ">=8.3.2, <9"

[environments]
default = { solve-group = "default" }
test = { features = ["test"], solve-group = "default" }
