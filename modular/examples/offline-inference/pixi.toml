[project]
authors = ["Modular <<EMAIL>>"]
channels = ["conda-forge", "https://conda.modular.com/max-nightly/"]
description = "Examples exercising the max stack offline and in python"
name = "Python Offline Inference Examples"
platforms = ["osx-arm64", "linux-aarch64", "linux-64"]
version = "0.1.0"

[tasks]
basic = "python basic.py"
test = "python basic.py"

[dependencies]
python = ">=3.9,<3.14"
modular = "*"
