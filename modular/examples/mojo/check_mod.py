# ===----------------------------------------------------------------------=== #
# Copyright (c) 2025, Modular Inc. All rights reserved.
#
# Licensed under the Apache License v2.0 with LLVM Exceptions:
# https://llvm.org/LICENSE.txt
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ===----------------------------------------------------------------------=== #

import shutil
import subprocess
from importlib.util import find_spec

FIX = """
-------------------------------------------------------------------------
fix following the steps here:
    https://github.com/modular/modular/issues/1085#issuecomment-**********
-------------------------------------------------------------------------
"""


def install_if_missing(name: str) -> None:
    if find_spec(name):
        return

    print(f"{name} not found, installing...")
    try:
        if shutil.which("python3"):
            python = "python3"
        elif shutil.which("python"):
            python = "python"
        else:
            raise ImportError("python not on path" + FIX)
        subprocess.check_call([python, "-m", "pip", "install", name])
        return
    except:
        raise ImportError(f"{name} not found" + FIX)
