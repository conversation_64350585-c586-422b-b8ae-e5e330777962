load("//bazel:api.bzl", "modular_run_binary_test", "mojo_binary")

mojo_binary(
    name = "basic_layouts",
    srcs = ["basic_layouts.mojo"],
    deps = [
        "@mojo//:layout",
        "@mojo//:stdlib",
    ],
)

modular_run_binary_test(
    name = "basic_layouts_test",
    binary = "basic_layouts",
)

mojo_binary(
    name = "tiled_layouts",
    srcs = ["tiled_layouts.mojo"],
    deps = [
        "@mojo//:layout",
        "@mojo//:stdlib",
    ],
)

modular_run_binary_test(
    name = "tiled_layouts_test",
    binary = "tiled_layouts",
)
