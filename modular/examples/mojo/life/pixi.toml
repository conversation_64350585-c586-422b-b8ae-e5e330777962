[project]
authors = ["Modular <<EMAIL>>"]
channels = ["conda-forge", "https://conda.modular.com/max-nightly/"]
description = "Introduction to <PERSON><PERSON>: <PERSON>'s Game of Life"
name = "life"
platforms = ["osx-arm64", "linux-64", "linux-aarch64"]
version = "0.1.0"

[dependencies]
max = "*"
python = ">=3.11,<3.14"
pygame = ">=2.6.1,<3"

[tasks]
lifev1 = "mojo run lifev1.mojo"
lifev2 = "mojo run lifev2.mojo"
main = "mojo run benchmark.mojo"
benchmark = "mojo run benchmark.mojo"
test = "mojo test -I . test"
