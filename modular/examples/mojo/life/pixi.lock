version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.modular.com/max-nightly/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fluidsynth-2.3.7-hd992666_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/jack-1.9.22-hf4617a5_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libavif16-1.3.0-h766b0b6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_h59b9bed_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_he106b2a_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_h7ac8fdf_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmad-0.15.1b-h0b41bf4_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.0-py313h17eae1a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/opusfile-0.12-h3358134_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/portaudio-19.6.0-h7c63dc7_9.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/portmidi-2.0.6-hf4617a5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pygame-2.6.1-py313h5fadf01_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py313h8e95178_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rav1e-0.7.1-h8fae777_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2_image-2.8.2-h06ee604_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2_mixer-2.6.3-h8830914_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2_ttf-2.24.0-hc1d668e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      linux-aarch64:
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/alsa-lib-1.2.14-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aom-3.9.1-hcccb83c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/attr-2.5.1-h4e544f5_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cairo-1.18.4-h83712da_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dav1d-1.2.1-h31becfc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dbus-1.16.2-heda779d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fluidsynth-2.3.7-h4f58cef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fontconfig-2.15.0-h8dda3cd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/freetype-2.13.3-h8af1aa0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-0.24.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-tools-0.24.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/graphite2-1.3.14-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/harfbuzz-11.2.1-h405b6a2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/jack-1.9.22-h9d01bbc_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/keyutils-1.6.1-h4e544f5_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/krb5-1.21.3-h50a48e9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lame-3.100-h4e544f5_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.43-h5e2c951_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lerc-4.0.0-hfdc4d58_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-0.24.1-h5e0f5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-devel-0.24.1-h5e0f5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libavif16-1.3.0-hb72faec_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libblas-3.9.0-32_h1a9f1db_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcap-2.75-h51d75a7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcblas-3.9.0-32_hab92f65_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdeflate-1.24-he377734_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdrm-2.4.125-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libedit-3.1.20250104-pl5321h976ea20_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libegl-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libflac-1.4.3-h2f0025b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype-2.13.3-h8af1aa0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype6-2.13.3-he93130f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcrypt-lib-1.11.1-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-0.24.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-devel-0.24.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran5-15.1.0-hbc25352_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgl-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglib-2.84.2-hc022ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglvnd-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglx-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgomp-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgpg-error-1.55-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libiconv-1.18-hc99b53d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libjpeg-turbo-3.1.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblapack-3.9.0-32_h411afd4_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmad-0.15.1b-hb4cce97_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libogg-1.3.5-h86ecc28_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenblas-0.3.30-pthreads_h9d3fd7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopus-1.5.2-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpciaccess-0.18-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpng-1.6.49-hec79eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsndfile-1.2.2-h79657aa_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsodium-1.0.20-h68df207_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.1-h4970e9a_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsystemd0-257.7-h2bb824b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtiff-4.7.0-h7c15681_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libudev1-257.7-h7b9e449_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libunwind-1.6.2-h01db608_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liburing-2.10-h17cf362_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libusb-1.0.29-h06eaf92_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvorbis-1.3.7-h01db608_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libwebp-base-1.5.0-h0886dbf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxcb-1.17.0-h262b8f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxkbcommon-1.10.0-hbab7b08_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxml2-2.13.8-he060846_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lz4-c-1.10.0-h5ad3122_1.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-aarch64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-aarch64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpg123-1.32.9-h65af167_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/numpy-2.3.0-py313hcf1be6b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.0-hd08dc88_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/opusfile-0.12-hf55b2d5_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pcre2-10.45-hf4ec17f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pixman-0.46.2-h86a87f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/portaudio-19.6.0-h5c6c0ed_9.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/portmidi-2.0.6-h9d01bbc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pthread-stubs-0.4-h86ecc28_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pulseaudio-client-17.0-h2f84921_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pygame-2.6.1-py313h50090c8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyzmq-27.0.0-py313h6e72e74_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/rav1e-0.7.1-ha3529ed_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2-2.32.54-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2_image-2.8.2-hd95cb85_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2_mixer-2.6.3-h422cae6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2_ttf-2.24.0-hc709ae0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl3-3.2.16-h7e2c5d6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/svt-av1-3.0.2-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tornado-6.5.1-py313h6a51379_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/wayland-1.23.1-h698ed42_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xkeyboard-config-2.45-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libice-1.1.2-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libsm-1.2.6-h0808dbd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libx11-1.8.12-hca56bd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxau-1.0.12-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxcursor-1.2.3-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxdmcp-1.1.5-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxext-1.3.6-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxfixes-6.0.1-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxrender-0.9.12-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zeromq-4.3.5-h5efb499_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstd-1.5.7-hbcf94c1_2.conda
      osx-arm64:
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/aom-3.9.1-h7bae524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/cairo-1.18.4-h6a3b0d2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/dav1d-1.2.1-hb547adb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/dbus-1.16.2-hda038a8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/fluidsynth-2.3.7-h80fea77_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/fontconfig-2.15.0-h1383a14_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/freetype-2.13.3-hce30654_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/gettext-0.24.1-h3dcc1bd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/gettext-tools-0.24.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/graphite2-1.3.14-h286801f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/harfbuzz-11.2.1-hab40de2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/icu-75.1-hfee45f7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/krb5-1.21.3-h237132a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/lame-3.100-h1a8c8d9_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/lerc-4.0.0-hd64df32_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libasprintf-0.24.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libasprintf-devel-0.24.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libavif16-1.3.0-hf1e31eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-32_h10e41b3_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-32_hb3479ef_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-20.1.7-ha82da77_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libdeflate-1.24-h5773f1b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libedit-3.1.20250104-pl5321hafb1f1b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.0-h286801f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libflac-1.4.3-hb765f3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype-2.13.3-hce30654_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype6-2.13.3-h1d14073_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgettextpo-0.24.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgettextpo-devel-0.24.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-5.0.0-14_2_0_h6c33f7e_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-14.2.0-h6c33f7e_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libglib-2.84.2-hbec27ea_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libiconv-1.18-hfe07756_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libintl-0.24.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libintl-devel-0.24.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libjpeg-turbo-3.1.0-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-32_hc9a63f6_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libmad-0.15.1b-h1a8c8d9_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libmpdec-4.0.0-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libogg-1.3.5-h48c0fde_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_hf332438_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopus-1.5.2-h48c0fde_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libpng-1.6.49-h3783ad8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsndfile-1.2.2-h9739721_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsodium-1.0.20-h99b78c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.1-h6fb428d_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libtiff-4.7.0-h2f21f7c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libusb-1.0.29-hbc156a2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libvorbis-1.3.7-h9f76cd9_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libwebp-base-1.5.0-h2471fea_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-20.1.7-hdb05f8b_0.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/osx-arm64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/osx-arm64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/mpg123-1.32.9-hf642e45_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.0-py313h41a2e72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.0-h81ee809_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/opusfile-0.12-h5643135_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pcre2-10.45-ha881caa_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pixman-0.46.2-h2f9eb0b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/portaudio-19.6.0-h13dd4ca_9.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/portmidi-2.0.6-h286801f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pygame-2.6.1-py313h55b729e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.13.5-hf3f3da0_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyzmq-27.0.0-py313he6960b1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/rav1e-0.7.1-h0716509_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl2-2.32.54-ha1acc90_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl2_image-2.8.2-h376e2e1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl2_mixer-2.6.3-h4fe3bdc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl2_ttf-2.24.0-h49b19f9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl3-3.2.16-h92d3ae7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/svt-av1-3.0.2-h8ab69cd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tornado-6.5.1-py313h90d716c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zeromq-4.3.5-hc1bb282_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstd-1.5.7-h6491c7d_2.conda
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  size: 23621
  timestamp: 1650670423406
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: 3702bef2f0a4d38bd8288bbe54aace623602a1343c2cfbefd3fa188e015bebf0
  md5: 6168d71addc746e8f2b8d57dfd2edcea
  depends:
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  size: 23712
  timestamp: 1650670790230
- conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
  sha256: b9214bc17e89bf2b691fad50d952b7f029f6148f4ac4fe7c60c08f093efdf745
  md5: 76df83c2a9035c54df5d04ff81bcc02d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  size: 566531
  timestamp: 1744668655747
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/alsa-lib-1.2.14-h86ecc28_0.conda
  sha256: 0aa836f6dd9132f243436898ed8024f408910f65220bafbfc95f71ab829bb395
  md5: a696b24c1b473ecc4774bcb5a6ac6337
  depends:
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  size: 595290
  timestamp: 1744668754404
- conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
  sha256: b08ef033817b5f9f76ce62dfcac7694e7b6b4006420372de22494503decac855
  md5: 346722a0be40f6edc53f12640d301338
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 2706396
  timestamp: 1718551242397
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aom-3.9.1-hcccb83c_0.conda
  sha256: ac438ce5d3d3673a9188b535fc7cda413b479f0d52536aeeac1bd82faa656ea0
  md5: cc744ac4efe5bcaa8cca51ff5b850df0
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 3250813
  timestamp: 1718551360260
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/aom-3.9.1-h7bae524_0.conda
  sha256: ec238f18ce8140485645252351a0eca9ef4f7a1c568a420f240a585229bc12ef
  md5: 7adba36492a1bb22d98ffffe4f6fc6de
  depends:
  - __osx >=11.0
  - libcxx >=16
  license: BSD-2-Clause
  license_family: BSD
  size: 2235747
  timestamp: 1718551382432
- conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
  sha256: 82c13b1772c21fc4a17441734de471d3aabf82b61db9b11f4a1bd04a9c4ac324
  md5: d9c69a24ad678ffce24c6543a0176b00
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  size: 71042
  timestamp: 1660065501192
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/attr-2.5.1-h4e544f5_1.tar.bz2
  sha256: 2c793b48e835a8fac93f1664c706442972a0206963bf8ca202e83f7f4d29a7d7
  md5: 1ef6c06fec1b6f5ee99ffe2152e53568
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  size: 74992
  timestamp: 1660065534958
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
  sha256: 2258b0b33e1cb3a9852d47557984abb6e7ea58e3d7f92706ec1f8e879290c4cb
  md5: 56398c28220513b9ea13d7b450acfb20
  depends:
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 189884
  timestamp: 1720974504976
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
  sha256: adfa71f158cbd872a36394c56c3568e6034aa55c623634b37a4836bd036e6b91
  md5: fc6948412dbbbe9a4c9ddbbcfe0a79ab
  depends:
  - __osx >=11.0
  license: bzip2-1.0.6
  license_family: BSD
  size: 122909
  timestamp: 1720974522888
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
  sha256: 7cfec9804c84844ea544d98bda1d9121672b66ff7149141b8415ca42dfcd44f6
  md5: 72525f07d72806e3b639ad4504c30ce5
  depends:
  - __unix
  license: ISC
  size: 151069
  timestamp: 1749990087500
- conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
  sha256: 3bd6a391ad60e471de76c0e9db34986c4b5058587fbf2efa5a7f54645e28c2c7
  md5: 09262e66b19567aff4f592fb53b28760
  depends:
  - __glibc >=2.17,<3.0.a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  size: 978114
  timestamp: 1741554591855
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cairo-1.18.4-h83712da_0.conda
  sha256: 37cfff940d2d02259afdab75eb2dbac42cf830adadee78d3733d160a1de2cc66
  md5: cd55953a67ec727db5dc32b167201aa6
  depends:
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  size: 966667
  timestamp: 1741554768968
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/cairo-1.18.4-h6a3b0d2_0.conda
  sha256: 00439d69bdd94eaf51656fdf479e0c853278439d22ae151cabf40eb17399d95f
  md5: 38f6df8bc8c668417b904369a01ba2e2
  depends:
  - __osx >=11.0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libcxx >=18
  - libexpat >=2.6.4,<3.0a0
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  license: LGPL-2.1-only or MPL-1.1
  size: 896173
  timestamp: 1741554795915
- conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
  sha256: 8aee789c82d8fdd997840c952a586db63c6890b00e88c4fb6e80a38edd5f51c0
  md5: 94b550b8d3a614dbd326af798c7dfb40
  depends:
  - __unix
  - python >=3.10
  license: BSD-3-Clause
  license_family: BSD
  size: 87749
  timestamp: 1747811451319
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
  noarch: generic
  sha256: 058c8156ff880b1180a36b94307baad91f9130d0e3019ad8c7ade035852016fb
  md5: 0401f31e3c9e48cebf215472aa3e7104
  depends:
  - python >=3.13,<3.14.0a0
  - python_abi * *_cp313
  license: Python-2.0
  size: 47560
  timestamp: 1750062514868
- conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
  sha256: 22053a5842ca8ee1cf8e1a817138cdb5e647eb2c46979f84153f6ad7bde73020
  md5: 418c6ca5929a611cbd69204907a83995
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 760229
  timestamp: 1685695754230
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dav1d-1.2.1-h31becfc_0.conda
  sha256: 33fe66d025cf5bac7745196d1a3dd7a437abcf2dbce66043e9745218169f7e17
  md5: 6e5a87182d66b2d1328a96b61ca43a62
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 347363
  timestamp: 1685696690003
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/dav1d-1.2.1-hb547adb_0.conda
  sha256: 93e077b880a85baec8227e8c72199220c7f87849ad32d02c14fb3807368260b8
  md5: 5a74cdee497e6b65173e10d94582fae6
  license: BSD-2-Clause
  license_family: BSD
  size: 316394
  timestamp: 1685695959391
- conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
  sha256: 3b988146a50e165f0fa4e839545c679af88e4782ec284cc7b6d07dd226d6a068
  md5: 679616eb5ad4e521c83da4650860aba7
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - libglib >=2.84.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 437860
  timestamp: 1747855126005
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dbus-1.16.2-heda779d_0.conda
  sha256: 5c9166bbbe1ea7d0685a1549aad4ea887b1eb3a07e752389f86b185ef8eac99a
  md5: 9203b74bb1f3fa0d6f308094b3b44c1e
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libglib >=2.84.2,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 469781
  timestamp: 1747855172617
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/dbus-1.16.2-hda038a8_0.conda
  sha256: 2ef01ab52dedb477cb7291994ad556279b37c8ad457521e75c47cad20248ea30
  md5: 80c663e4f6b0fd8d6723ff7d68f09429
  depends:
  - __osx >=11.0
  - libcxx >=18
  - libzlib >=1.3.1,<2.0a0
  - libglib >=2.84.2,<3.0a0
  - libexpat >=2.7.0,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 384376
  timestamp: 1747855177419
- conda: https://conda.anaconda.org/conda-forge/linux-64/fluidsynth-2.3.7-hd992666_0.conda
  sha256: 0bf26d25ae79e6f5f01a49a00e9ba3b60b10dd4c12ec43bdba51055c26bc9dd6
  md5: dd6c7b8a1b217ef7522ca987c465651d
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.12,<1.3.0a0
  - jack >=1.9.22,<1.10.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libstdcxx >=13
  - portaudio >=19.6.0,<19.7.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - readline >=8.2,<9.0a0
  - sdl2 >=2.30.7,<3.0a0
  license: GPL-2.0-or-later
  license_family: LGPL
  size: 279996
  timestamp: 1729590344462
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fluidsynth-2.3.7-h4f58cef_0.conda
  sha256: e557d4fcb40f224180d61cc5e57fe3d5d5793a255c7d5a29546e524ef2ffa298
  md5: 0207cc67431f4d12605cae60d8d323d7
  depends:
  - alsa-lib >=1.2.12,<1.3.0a0
  - jack >=1.9.22,<1.10.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libstdcxx >=13
  - portaudio >=19.6.0,<19.7.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - readline >=8.2,<9.0a0
  - sdl2 >=2.30.7,<3.0a0
  license: GPL-2.0-or-later
  license_family: LGPL
  size: 292770
  timestamp: 1729590405853
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/fluidsynth-2.3.7-h80fea77_0.conda
  sha256: e4c39119797493d4f085cd6274e123372bec77e05ef93203ccf5dee714bfd9c3
  md5: dc2fc082a05af6a790d3e8e3e6489e6c
  depends:
  - __osx >=11.0
  - libcxx >=17
  - libglib >=2.82.2,<3.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - portaudio >=19.6.0,<19.7.0a0
  - readline >=8.2,<9.0a0
  - sdl2 >=2.30.7,<3.0a0
  license: GPL-2.0-or-later
  license_family: LGPL
  size: 234967
  timestamp: 1729590579216
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
  sha256: 58d7f40d2940dd0a8aa28651239adbf5613254df0f75789919c4e6762054403b
  md5: 0c96522c6bdaed4b1566d11387caaf45
  license: BSD-3-Clause
  license_family: BSD
  size: 397370
  timestamp: 1566932522327
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
  sha256: c52a29fdac682c20d252facc50f01e7c2e7ceac52aa9817aaf0bb83f7559ec5c
  md5: 34893075a5c9e55cdafac56607368fc6
  license: OFL-1.1
  license_family: Other
  size: 96530
  timestamp: 1620479909603
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
  sha256: 00925c8c055a2275614b4d983e1df637245e19058d79fc7dd1a93b8d9fb4b139
  md5: 4d59c254e01d9cde7957100457e2d5fb
  license: OFL-1.1
  license_family: Other
  size: 700814
  timestamp: 1620479612257
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
  sha256: 2821ec1dc454bd8b9a31d0ed22a7ce22422c0aef163c59f49dfdf915d0f0ca14
  md5: 49023d73832ef61042f6a237cb2687e7
  license: LicenseRef-Ubuntu-Font-Licence-Version-1.0
  license_family: Other
  size: 1620504
  timestamp: 1727511233259
- conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
  sha256: 7093aa19d6df5ccb6ca50329ef8510c6acb6b0d8001191909397368b65b02113
  md5: 8f5b0b297b59e1ac160ad4beec99dbee
  depends:
  - __glibc >=2.17,<3.0.a0
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 265599
  timestamp: 1730283881107
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fontconfig-2.15.0-h8dda3cd_1.conda
  sha256: fe023bb8917c8a3138af86ef537b70c8c5d60c44f93946a87d1e8bb1a6634b55
  md5: 112b71b6af28b47c624bcbeefeea685b
  depends:
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 277832
  timestamp: 1730284967179
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/fontconfig-2.15.0-h1383a14_1.conda
  sha256: f79d3d816fafbd6a2b0f75ebc3251a30d3294b08af9bb747194121f5efa364bc
  md5: 7b29f48742cea5d1ccb5edd839cb5621
  depends:
  - __osx >=11.0
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 234227
  timestamp: 1730284037572
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
  sha256: a997f2f1921bb9c9d76e6fa2f6b408b7fa549edd349a77639c9fe7a23ea93e61
  md5: fee5683a3f04bd15cbd8318b096a27ab
  depends:
  - fonts-conda-forge
  license: BSD-3-Clause
  license_family: BSD
  size: 3667
  timestamp: 1566974674465
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
  sha256: 53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38
  md5: f766549260d6815b0c52253f1fb1bb29
  depends:
  - font-ttf-dejavu-sans-mono
  - font-ttf-inconsolata
  - font-ttf-source-code-pro
  - font-ttf-ubuntu
  license: BSD-3-Clause
  license_family: BSD
  size: 4102
  timestamp: 1566932280397
- conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
  sha256: 7ef7d477c43c12a5b4cddcf048a83277414512d1116aba62ebadfa7056a7d84f
  md5: 9ccd736d31e0c6e41f54e704e5312811
  depends:
  - libfreetype 2.13.3 ha770c72_1
  - libfreetype6 2.13.3 h48d6fc4_1
  license: GPL-2.0-only OR FTL
  size: 172450
  timestamp: 1745369996765
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/freetype-2.13.3-h8af1aa0_1.conda
  sha256: 3b3ff45ac1fc880fbc8268477d29901a8fead32fb2241f98e4f2a1acffe6eea2
  md5: 71c4cbe1b384a8e7b56993394a435343
  depends:
  - libfreetype 2.13.3 h8af1aa0_1
  - libfreetype6 2.13.3 he93130f_1
  license: GPL-2.0-only OR FTL
  size: 172259
  timestamp: 1745370055170
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/freetype-2.13.3-hce30654_1.conda
  sha256: 6b63c72ea51a41d41964841404564c0729fdddd3e952e2715839fd759b7cfdfc
  md5: e684de4644067f1956a580097502bf03
  depends:
  - libfreetype 2.13.3 hce30654_1
  - libfreetype6 2.13.3 h1d14073_1
  license: GPL-2.0-only OR FTL
  size: 172220
  timestamp: 1745370149658
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
  sha256: 88db27c666e1f8515174bf622a3e2ad983c94d69e3a23925089e476b9b06ad00
  md5: c63e7590d4d6f4c85721040ed8b12888
  depends:
  - __glibc >=2.17,<3.0.a0
  - gettext-tools 0.24.1 h5888daf_0
  - libasprintf 0.24.1 h8e693c7_0
  - libasprintf-devel 0.24.1 h8e693c7_0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  - libgettextpo-devel 0.24.1 h5888daf_0
  - libstdcxx >=13
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  size: 511988
  timestamp: 1746228987123
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-0.24.1-h5ad3122_0.conda
  sha256: 027499618b9e9c8306490c4af11a329059642ba16086c8a4c3c2596652770a6f
  md5: 7c2185682ee955c32d65f35822ccc06f
  depends:
  - gettext-tools 0.24.1 h5ad3122_0
  - libasprintf 0.24.1 h5e0f5ae_0
  - libasprintf-devel 0.24.1 h5e0f5ae_0
  - libgcc >=13
  - libgettextpo 0.24.1 h5ad3122_0
  - libgettextpo-devel 0.24.1 h5ad3122_0
  - libstdcxx >=13
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  size: 509273
  timestamp: 1746228828727
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/gettext-0.24.1-h3dcc1bd_0.conda
  sha256: 2f02be79abd983d4ce21c54ed6aea706487d315f6e35e2f350318bb014b33ec0
  md5: 6b5294d638ac03333bd7a176b97825a0
  depends:
  - __osx >=11.0
  - gettext-tools 0.24.1 h493aca8_0
  - libasprintf 0.24.1 h493aca8_0
  - libasprintf-devel 0.24.1 h493aca8_0
  - libcxx >=18
  - libgettextpo 0.24.1 h493aca8_0
  - libgettextpo-devel 0.24.1 h493aca8_0
  - libiconv >=1.18,<2.0a0
  - libintl 0.24.1 h493aca8_0
  - libintl-devel 0.24.1 h493aca8_0
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  size: 514509
  timestamp: 1746229411356
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
  sha256: 3ba33868630b903e3cda7a9176363cdf02710fb8f961efed5f8200c4d53fb4e3
  md5: d54305672f0361c2f3886750e7165b5f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 3129801
  timestamp: 1746228937647
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-tools-0.24.1-h5ad3122_0.conda
  sha256: 4176d66aa44d070db71f7e80145d2b423faeef702473469bb82cd894dd589c66
  md5: 66459c6f8b54cea68654198f03d33e7d
  depends:
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 3530002
  timestamp: 1746228795208
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/gettext-tools-0.24.1-h493aca8_0.conda
  sha256: b0aac8a3cc5453912af515c70340fd3b2857f59bd412817d45f750a77d934d99
  md5: 8a3a2c7c4a60f8b179f46d3568bb9f70
  depends:
  - __osx >=11.0
  - libiconv >=1.18,<2.0a0
  - libintl 0.24.1 h493aca8_0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 3100065
  timestamp: 1746229361943
- conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
  sha256: cac69f3ff7756912bbed4c28363de94f545856b35033c0b86193366b95f5317d
  md5: 951ff8d9e5536896408e89d63230b8d5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 98419
  timestamp: 1750079957535
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/graphite2-1.3.14-h5ad3122_0.conda
  sha256: 957d9bcf7f8b2d8925a9af238189b372ba42c0fdbda4248cd8bd76684781af3d
  md5: 087ecf989fc23fc50944a06fddf5f3bc
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 101397
  timestamp: 1750080039341
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/graphite2-1.3.14-h286801f_0.conda
  sha256: e1c431b66b0a632e8fcc2b886cccde4eb5ec5eb8a3d84e89b7639d603c174646
  md5: 64d15e1dfe86fa13cf0d519d1074dcd9
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 81566
  timestamp: 1750080158744
- conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
  sha256: 5bd0f3674808862838d6e2efc0b3075e561c34309c5c2f4c976f7f1f57c91112
  md5: 0e6e192d4b3d95708ad192d957cf3163
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - freetype
  - graphite2
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libglib >=2.84.1,<3.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 1730226
  timestamp: 1747091044218
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/harfbuzz-11.2.1-h405b6a2_0.conda
  sha256: 2f7754c197fc1b7e57cf5b4063298834818889561c0c462b7fe363742defdbd5
  md5: b55680fc90e9747dc858e7ceb0abc2b2
  depends:
  - cairo >=1.18.4,<2.0a0
  - freetype
  - graphite2
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libglib >=2.84.1,<3.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 1746366
  timestamp: 1747094097917
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/harfbuzz-11.2.1-hab40de2_0.conda
  sha256: 244e4071229aa3b824dd2a9814c0e8b4c2b40dfb28914ec2247bf27c5c681584
  md5: 12f4520f618ff6e398a2c8e0bed1e580
  depends:
  - __osx >=11.0
  - cairo >=1.18.4,<2.0a0
  - freetype
  - graphite2
  - icu >=75.1,<76.0a0
  - libcxx >=18
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libglib >=2.84.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 1395282
  timestamp: 1747091793921
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12129203
  timestamp: 1720853576813
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
  sha256: 813298f2e54ef087dbfc9cc2e56e08ded41de65cff34c639cc8ba4e27e4540c9
  md5: 268203e8b983fddb6412b36f2024e75c
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12282786
  timestamp: 1720853454991
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/icu-75.1-hfee45f7_0.conda
  sha256: 9ba12c93406f3df5ab0a43db8a4b4ef67a5871dfd401010fbe29b218b2cbe620
  md5: 5eb22c1d7b3fc4abb50d92d621583137
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 11857802
  timestamp: 1720853997952
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
  sha256: c18ab120a0613ada4391b15981d86ff777b5690ca461ea7e9e49531e8f374745
  md5: 63ccfdc3a3ce25b027b8767eb722fca8
  depends:
  - python >=3.9
  - zipp >=3.20
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 34641
  timestamp: 1747934053147
- conda: https://conda.anaconda.org/conda-forge/linux-64/jack-1.9.22-hf4617a5_3.conda
  sha256: 69f6b7228aa43177c82e21b90497adb1d7c61e70f1b8dd38d8ca8baa74a0cbf7
  md5: a071738556dc29fa1c844fb440506dc8
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.14,<1.3.0a0
  - libgcc >=13
  - libopus >=1.5.2,<2.0a0
  - libstdcxx >=13
  license: LGPL-2.0-only
  license_family: LGPL
  size: 461260
  timestamp: 1747574434594
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/jack-1.9.22-h9d01bbc_3.conda
  sha256: 4254ec5f005a8f5b78b9c893467bb0c58d2bfbd3c78a8a194802a494dd5f3e67
  md5: 6716b2d771e2568d14949bd376fe62fa
  depends:
  - alsa-lib >=1.2.14,<1.3.0a0
  - libgcc >=13
  - libopus >=1.5.2,<2.0a0
  - libstdcxx >=13
  license: LGPL-2.0-only
  license_family: LGPL
  size: 488216
  timestamp: 1747576147517
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
  sha256: 19d8bd5bb2fde910ec59e081eeb59529491995ce0d653a5209366611023a0b3a
  md5: 4ebae00eae9705b0c3d6d1018a81d047
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-dateutil >=2.8.2
  - pyzmq >=23.0
  - tornado >=6.2
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 106342
  timestamp: 1733441040958
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
  sha256: 56a7a7e907f15cca8c4f9b0c99488276d4cb10821d2d15df9245662184872e81
  md5: b7d89d860ebcda28a5303526cdee68ab
  depends:
  - __unix
  - platformdirs >=2.5
  - python >=3.8
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 59562
  timestamp: 1748333186063
- conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 117831
  timestamp: 1646151697040
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/keyutils-1.6.1-h4e544f5_0.tar.bz2
  sha256: 6d4233d97a9b38acbb26e1268bcf8c10a8e79c2aed7e5a385ec3769967e3e65b
  md5: 1f24853e59c68892452ef94ddd8afd4b
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 112327
  timestamp: 1646166857935
- conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
  sha256: 99df692f7a8a5c27cd14b5fb1374ee55e756631b9c3d659ed3ee60830249b238
  md5: 3f43953b7d3fb3aaa1d0d0723d91e368
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1370023
  timestamp: 1719463201255
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/krb5-1.21.3-h50a48e9_0.conda
  sha256: 0ec272afcf7ea7fbf007e07a3b4678384b7da4047348107b2ae02630a570a815
  md5: 29c10432a2ca1472b53f299ffb2ffa37
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1474620
  timestamp: 1719463205834
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/krb5-1.21.3-h237132a_0.conda
  sha256: 4442f957c3c77d69d9da3521268cad5d54c9033f1a73f99cde0a3658937b159b
  md5: c6dc8a0fdec13a0565936655c33069a1
  depends:
  - __osx >=11.0
  - libcxx >=16
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1155530
  timestamp: 1719463474401
- conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
  sha256: aad2a703b9d7b038c0f745b853c6bb5f122988fe1a7a096e0e606d9cbec4eaab
  md5: a8832b479f93521a9e7b5b743803be51
  depends:
  - libgcc-ng >=12
  license: LGPL-2.0-only
  license_family: LGPL
  size: 508258
  timestamp: 1664996250081
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lame-3.100-h4e544f5_1003.tar.bz2
  sha256: 2502904a42df6d94bd743f7b73915415391dd6d31d5f50cb57c0a54a108e7b0a
  md5: ab05bcf82d8509b4243f07e93bada144
  depends:
  - libgcc-ng >=12
  license: LGPL-2.0-only
  license_family: LGPL
  size: 604863
  timestamp: 1664997611416
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/lame-3.100-h1a8c8d9_1003.tar.bz2
  sha256: f40ce7324b2cf5338b766d4cdb8e0453e4156a4f83c2f31bbfff750785de304c
  md5: bff0e851d66725f78dc2fd8b032ddb7e
  license: LGPL-2.0-only
  license_family: LGPL
  size: 528805
  timestamp: 1664996399305
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
  sha256: dcd2b1a065bbf5c54004ddf6551c775a8eb6993c8298ca8a6b92041ed413f785
  md5: 6dc9e1305e7d3129af4ad0dabda30e56
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  size: 670635
  timestamp: 1749858327854
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.43-h5e2c951_5.conda
  sha256: 0cb7f3a3281e86c850ea09e0dbd0c3652eaee367aa089170d7f6bec07ff9fc07
  md5: e62696c21a84af63cfc49f4b5428a36a
  constrains:
  - binutils_impl_linux-aarch64 2.43
  license: GPL-3.0-only
  license_family: GPL
  size: 699857
  timestamp: 1749858448061
- conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
  sha256: 412381a43d5ff9bbed82cd52a0bbca5b90623f62e41007c9c42d3870c60945ff
  md5: 9344155d33912347b37f0ae6c410a835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 264243
  timestamp: 1745264221534
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lerc-4.0.0-hfdc4d58_1.conda
  sha256: f01df5bbf97783fac9b89be602b4d02f94353f5221acfd80c424ec1c9a8d276c
  md5: 60dceb7e876f4d74a9cbd42bbbc6b9cf
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 227184
  timestamp: 1745265544057
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/lerc-4.0.0-hd64df32_1.conda
  sha256: 12361697f8ffc9968907d1a7b5830e34c670e4a59b638117a2cdfed8f63a38f8
  md5: a74332d9b60b62905e3d30709df08bf1
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: Apache-2.0
  license_family: Apache
  size: 188306
  timestamp: 1745264362794
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
  sha256: e30733a729eb6efd9cb316db0202897c882d46f6c20a0e647b4de8ec921b7218
  md5: 57566a81dd1e5aa3d98ac7582e8bfe03
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-or-later
  size: 53115
  timestamp: 1746228856865
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-0.24.1-h5e0f5ae_0.conda
  sha256: 969b002ab70bf2f27fc108c83c07d6e4f4a4fd1a1ad3a8028e625a78a748347b
  md5: e6dd5b99796423a24ecc42db08ab31da
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-or-later
  size: 53530
  timestamp: 1746228742482
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libasprintf-0.24.1-h493aca8_0.conda
  sha256: 54293ab2ce43085ac424dc62804fd4d7ec62cce404a77f0c99a9a48857bca0a9
  md5: b5a77d2b7c2013b3b1ffce193764302f
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: LGPL-2.1-or-later
  size: 52180
  timestamp: 1746229244376
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
  sha256: ccbfc465456133042eea3e8d69bae009893f57a47a786f772c0af382bda7ad99
  md5: 8f66ed2e34507b7ae44afa31c3e4ec79
  depends:
  - __glibc >=2.17,<3.0.a0
  - libasprintf 0.24.1 h8e693c7_0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 34680
  timestamp: 1746228884730
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-devel-0.24.1-h5e0f5ae_0.conda
  sha256: caec98b6fc50630741e80ed39c06421be84222c6da629e12248fbddccf54dd5e
  md5: b02fed476ae02f9bbd5ab534dbb57587
  depends:
  - libasprintf 0.24.1 h5e0f5ae_0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 34897
  timestamp: 1746228758454
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libasprintf-devel-0.24.1-h493aca8_0.conda
  sha256: 0ebda4d9d20b1fb43a9b7cf38e260c92469000300b6abe796e2a65f3e1257657
  md5: ae1153389db4100dc7f6f4f50facf6bc
  depends:
  - __osx >=11.0
  - libasprintf 0.24.1 h493aca8_0
  license: LGPL-2.1-or-later
  size: 35042
  timestamp: 1746229269033
- conda: https://conda.anaconda.org/conda-forge/linux-64/libavif16-1.3.0-h766b0b6_0.conda
  sha256: 170b51a3751c2f842ff9e11d22423494ef7254b448ef2b24751256ef18aa1302
  md5: f17f2d0e5c9ad6b958547fd67b155771
  depends:
  - __glibc >=2.17,<3.0.a0
  - aom >=3.9.1,<3.10.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - libgcc >=13
  - rav1e >=0.7.1,<0.8.0a0
  - svt-av1 >=3.0.2,<3.0.3.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 140052
  timestamp: 1746836263991
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libavif16-1.3.0-hb72faec_0.conda
  sha256: f9f788398dbb5eea297132ba7dcade06a2d59c61aa722708d30b4a2f69def1ac
  md5: 6f699633a5967c4d44c777b7f7856d40
  depends:
  - aom >=3.9.1,<3.10.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - libgcc >=13
  - rav1e >=0.7.1,<0.8.0a0
  - svt-av1 >=3.0.2,<3.0.3.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 137787
  timestamp: 1746836360100
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libavif16-1.3.0-hf1e31eb_0.conda
  sha256: bd8bc77a0c81c73ba955a05c4b4179b1bf9d0fef1a379bdb37fcd41961650175
  md5: c61522d664c4ee27234f802d631ddb88
  depends:
  - __osx >=11.0
  - aom >=3.9.1,<3.10.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - rav1e >=0.7.1,<0.8.0a0
  - svt-av1 >=3.0.2,<3.0.3.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 111817
  timestamp: 1746836468929
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_h59b9bed_openblas.conda
  build_number: 32
  sha256: 1540bf739feb446ff71163923e7f044e867d163c50b605c8b421c55ff39aa338
  md5: 2af9f3d5c2e39f417ce040f5a35c40c6
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - libcblas   3.9.0   32*_openblas
  - mkl <2025
  - liblapacke 3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17330
  timestamp: 1750388798074
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libblas-3.9.0-32_h1a9f1db_openblas.conda
  build_number: 32
  sha256: a257f0c43dd142be7eab85bf78999a869a6938ddb2415202f74724ff51dff316
  md5: 833718ed1c0b597ce17e5f410bd9b017
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - libcblas   3.9.0   32*_openblas
  - liblapack  3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  - mkl <2025
  license: BSD-3-Clause
  license_family: BSD
  size: 17341
  timestamp: 1750388911474
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-32_h10e41b3_openblas.conda
  build_number: 32
  sha256: 2775472dd81d43dc20804b484028560bfecd5ab4779e39f1fb95684da3ff2029
  md5: d4a1732d2b330c9d5d4be16438a0ac78
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - mkl <2025
  - libcblas   3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17520
  timestamp: 1750388963178
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
  sha256: 9c84448305e7c9cc44ccec7757cf5afcb5a021f4579aa750a1fa6ea398783950
  md5: c44c16d6976d2aebbd65894d7741e67e
  depends:
  - __glibc >=2.17,<3.0.a0
  - attr >=2.5.1,<2.6.0a0
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 120375
  timestamp: 1741176638215
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcap-2.75-h51d75a7_0.conda
  sha256: d77e8bd8d5714a80c1fa88037e71d5c29f21bae1e9281528006c9c5a6175ac1a
  md5: c5456e13665779bf7a62dc7724ca2938
  depends:
  - attr >=2.5.1,<2.6.0a0
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 108212
  timestamp: 1741177682469
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_he106b2a_openblas.conda
  build_number: 32
  sha256: 92a001fc181e6abe4f4a672b81d9413ca2f22609f8a95327dfcc6eee593ffeb9
  md5: 3d3f9355e52f269cd8bc2c440d8a5263
  depends:
  - libblas 3.9.0 32_h59b9bed_openblas
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17308
  timestamp: 1750388809353
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcblas-3.9.0-32_hab92f65_openblas.conda
  build_number: 32
  sha256: e902de3cd34d4fc1f7d15b9c9c1d297d90043d5283d28db410d32e45ea4e1a33
  md5: 2f02a3ea0960118a0a8d45cdd348b039
  depends:
  - libblas 3.9.0 32_h1a9f1db_openblas
  constrains:
  - liblapack  3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17335
  timestamp: 1750388919351
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-32_hb3479ef_openblas.conda
  build_number: 32
  sha256: 25d46ace14c3ac45d4aa18b5f7a0d3d30cec422297e900f8b97a66334232061c
  md5: d8e8ba717ae863b13a7495221f2b5a71
  depends:
  - libblas 3.9.0 32_h10e41b3_openblas
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17485
  timestamp: 1750388970626
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-20.1.7-ha82da77_0.conda
  sha256: a3fd34773f1252a4f089e74a075ff5f0f6b878aede097e83a405f35687c36f24
  md5: 881de227abdddbe596239fa9e82eb3ab
  depends:
  - __osx >=11.0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 567189
  timestamp: 1749847129529
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
  sha256: 8420748ea1cc5f18ecc5068b4f24c7a023cc9b20971c99c824ba10641fb95ddf
  md5: 64f0c503da58ec25ebd359e4d990afa8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 72573
  timestamp: 1747040452262
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdeflate-1.24-he377734_0.conda
  sha256: dd0e4baa983803227ec50457731d6f41258b90b3530f579b5d3151d5a98af191
  md5: f0b3d6494663b3385bf87fc206d7451a
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 70417
  timestamp: 1747040440762
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libdeflate-1.24-h5773f1b_0.conda
  sha256: 417d52b19c679e1881cce3f01cad3a2d542098fa2d6df5485aac40f01aede4d1
  md5: 3baf58a5a87e7c2f4d243ce2f8f2fe5c
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 54790
  timestamp: 1747040549847
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
  sha256: f53458db897b93b4a81a6dbfd7915ed8fa4a54951f97c698dde6faa028aadfd2
  md5: 4c0ab57463117fbb8df85268415082f5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  size: 246161
  timestamp: 1749904704373
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdrm-2.4.125-h86ecc28_0.conda
  sha256: 4413fda35527cf7a746c5e386fa5406349c0948d51fc20f7896732795a369e5d
  md5: c5e4a8dad08e393b3616651e963304e5
  depends:
  - libgcc >=13
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  size: 252778
  timestamp: 1749904786465
- conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
  sha256: d789471216e7aba3c184cd054ed61ce3f6dac6f87a50ec69291b9297f8c18724
  md5: c277e0a4d549b03ac1e9d6cbbe3d017b
  depends:
  - ncurses
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 134676
  timestamp: 1738479519902
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libedit-3.1.20250104-pl5321h976ea20_0.conda
  sha256: c0b27546aa3a23d47919226b3a1635fccdb4f24b94e72e206a751b33f46fd8d6
  md5: fb640d776fc92b682a14e001980825b1
  depends:
  - ncurses
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 148125
  timestamp: 1738479808948
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libedit-3.1.20250104-pl5321hafb1f1b_0.conda
  sha256: 66aa216a403de0bb0c1340a88d1a06adaff66bae2cfd196731aa24db9859d631
  md5: 44083d2d2c2025afca315c7a172eab2b
  depends:
  - ncurses
  - __osx >=11.0
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 107691
  timestamp: 1738479560845
- conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
  sha256: 7fd5408d359d05a969133e47af580183fbf38e2235b562193d427bb9dad79723
  md5: c151d5eb730e9b7480e6d48c0fc44048
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  size: 44840
  timestamp: 1731330973553
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libegl-1.7.0-hd24410f_2.conda
  sha256: 8962abf38a58c235611ce356b9899f6caeb0352a8bce631b0bcc59352fda455e
  md5: cf105bce884e4ef8c8ccdca9fe6695e7
  depends:
  - libglvnd 1.7.0 hd24410f_2
  license: LicenseRef-libglvnd
  size: 53551
  timestamp: 1731330990477
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
  sha256: 33ab03438aee65d6aa667cf7d90c91e5e7d734c19a67aa4c7040742c0a13d505
  md5: db0bfbe7dd197b68ad5f30333bae6ce0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  size: 74427
  timestamp: 1743431794976
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
  sha256: e3a0d95fe787cccf286f5dced9fa9586465d3cd5ec8e04f7ad7f0e72c4afd089
  md5: d41a057e7968705dae8dcb7c8ba2c8dd
  depends:
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  size: 73155
  timestamp: 1743432002397
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.0-h286801f_0.conda
  sha256: ee550e44765a7bbcb2a0216c063dcd53ac914a7be5386dd0554bd06e6be61840
  md5: 6934bbb74380e045741eb8637641a65b
  depends:
  - __osx >=11.0
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  size: 65714
  timestamp: 1743431789879
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
  sha256: 608b8c8b0315423e524b48733d91edd43f95cb3354a765322ac306a858c2cd2e
  md5: 15a131f30cae36e9a655ca81fee9a285
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 55847
  timestamp: 1743434586764
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
  sha256: c6a530924a9b14e193ea9adfe92843de2a806d1b7dbfd341546ece9653129e60
  md5: c215a60c2935b517dcda8cad4705734d
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 39839
  timestamp: 1743434670405
- conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
  sha256: 65908b75fa7003167b8a8f0001e11e58ed5b1ef5e98b96ab2ba66d7c1b822c7d
  md5: ee48bf17cc83a00f59ca1494d5646869
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 394383
  timestamp: 1687765514062
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libflac-1.4.3-h2f0025b_0.conda
  sha256: b54935360349d3418b0663d787f20b3cba0b7ce3fcdf3ba5e7ef02b884759049
  md5: 520b12eab32a92e19b1f239ac545ec03
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 371550
  timestamp: 1687765491794
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libflac-1.4.3-hb765f3a_0.conda
  sha256: 3990b52782fe7207ab642df25368ed443094f6d1a7ea61854935c24192b388aa
  md5: 356faba64411660f6c4d24ea31640733
  depends:
  - gettext >=0.21.1,<1.0a0
  - libcxx >=15.0.7
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 314408
  timestamp: 1687766236790
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
  sha256: 7be9b3dac469fe3c6146ff24398b685804dfc7a1de37607b84abd076f57cc115
  md5: 51f5be229d83ecd401fb369ab96ae669
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7693
  timestamp: 1745369988361
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype-2.13.3-h8af1aa0_1.conda
  sha256: c1bb6726b054b00ad509b9ace5e04f4bfe97e6fdaf5c4473c537e6c03d1f660b
  md5: 2d4a1c3dcabb80b4a56d5c34bdacea08
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7774
  timestamp: 1745370050680
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype-2.13.3-hce30654_1.conda
  sha256: 1f8c16703fe333cdc2639f7cdaf677ac2120843453222944a7c6c85ec342903c
  md5: d06282e08e55b752627a707d58779b8f
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7813
  timestamp: 1745370144506
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
  sha256: 7759bd5c31efe5fbc36a7a1f8ca5244c2eabdbeb8fc1bee4b99cf989f35c7d81
  md5: 3c255be50a506c50765a93a6644f32fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 380134
  timestamp: 1745369987697
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype6-2.13.3-he93130f_1.conda
  sha256: 9f189f75bb79f6b97c48804e89b4f1db5dc3fba5729551e4cbd2deca98580635
  md5: 51eae9012d75b8f7e4b0adfe61a83330
  depends:
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 408198
  timestamp: 1745370049871
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype6-2.13.3-h1d14073_1.conda
  sha256: c278df049b1a071841aa0aca140a338d087ea594e07dcf8a871d2cfe0e330e75
  md5: b163d446c55872ef60530231879908b9
  depends:
  - __osx >=11.0
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 333529
  timestamp: 1745370142848
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
  sha256: 59a87161212abe8acc57d318b0cc8636eb834cdfdfddcf1f588b5493644b39a3
  md5: 9e60c55e725c20d23125a5f0dd69af5d
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_3
  - libgomp 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 824921
  timestamp: 1750808216066
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
  sha256: a08e3f89d4cd7c5e18a7098419e378a8506ebfaf4dc1eaac59bf7b962ca66cdb
  md5: 409b902521be20c2efb69d2e0c5e3bc8
  depends:
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 15.1.0 he277a41_3
  - libgcc-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 510464
  timestamp: 1750808926824
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
  sha256: b0b0a5ee6ce645a09578fc1cb70c180723346f8a45fdb6d23b3520591c6d6996
  md5: e66f2b8ad787e7beb0f846e4bd7e8493
  depends:
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29033
  timestamp: 1750808224854
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
  sha256: 222eedd38467f7af8fb703c16cc1abf83038e7b6a09f707bbb4340e8ed589e14
  md5: 831062d3b6a4cdfdde1015be90016102
  depends:
  - libgcc 15.1.0 he277a41_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29009
  timestamp: 1750808930406
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
  sha256: dc9c7d7a6c0e6639deee6fde2efdc7e119e7739a6b229fa5f9049a449bae6109
  md5: 8504a291085c9fb809b66cabd5834307
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgpg-error >=1.55,<2.0a0
  license: LGPL-2.1-or-later
  size: 590353
  timestamp: 1747060639058
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcrypt-lib-1.11.1-h86ecc28_0.conda
  sha256: 5c572886ae3bf8f55fbc8f18275317679b559a9dd00cf1f128d24057dc6de70e
  md5: 50df370cbbbcfb4aa67556879e6643a1
  depends:
  - libgcc >=13
  - libgpg-error >=1.55,<2.0a0
  license: LGPL-2.1-or-later
  size: 652592
  timestamp: 1747060671875
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
  sha256: 104f2341546e295d1136ab3010e81391bd3fd5be0f095db59266e8eba2082d37
  md5: 2ee6d71b72f75d50581f2f68e965efdb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 171165
  timestamp: 1746228870846
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-0.24.1-h5ad3122_0.conda
  sha256: 3dc0c79b5780d1155ed38a9b4e484d5bd3ea8d5f7e825cff69741cf8dc53313f
  md5: 54bb1208da39417046f68daa603a37eb
  depends:
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  size: 217039
  timestamp: 1746228750642
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgettextpo-0.24.1-h493aca8_0.conda
  sha256: 0f380fee5d5dc870b6b9d3134cca344965d68bbf454f6ac741907fee4cc3e07a
  md5: 218a45f477876644cf75c7ed0b5158c7
  depends:
  - __osx >=11.0
  - libiconv >=1.18,<2.0a0
  - libintl 0.24.1 h493aca8_0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 176982
  timestamp: 1746229282723
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
  sha256: a9a0cba030778eb2944a1f235dba51e503b66f8be0ce6f55f745173a515c3644
  md5: 8f04c7aae6a46503bc36d1ed5abc8c7c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 37234
  timestamp: 1746228897993
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-devel-0.24.1-h5ad3122_0.conda
  sha256: 5fbd5afb2a48282c9b147c068c513bfdfaab22ffdb5eae6c6bc573d44f08d791
  md5: 376a04253d0a8345e67fc01ceae504c5
  depends:
  - libgcc >=13
  - libgettextpo 0.24.1 h5ad3122_0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 37390
  timestamp: 1746228765836
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgettextpo-devel-0.24.1-h493aca8_0.conda
  sha256: f1874034577a09d15486e10935ee6eb942a7ef2745b22e4c250b7904deff6e79
  md5: 90c8817b50a32cc63bd2c4f85a6f4589
  depends:
  - __osx >=11.0
  - libgettextpo 0.24.1 h493aca8_0
  - libiconv >=1.18,<2.0a0
  - libintl 0.24.1 h493aca8_0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 37636
  timestamp: 1746229306213
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
  sha256: 77dd1f1efd327e6991e87f09c7c97c4ae1cfbe59d9485c41d339d6391ac9c183
  md5: bfbca721fd33188ef923dfe9ba172f29
  depends:
  - libgfortran5 15.1.0 hcea5267_3
  constrains:
  - libgfortran-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29057
  timestamp: 1750808257258
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran-15.1.0-he9431aa_3.conda
  sha256: 9459e5e273397ee6680ea2f1f4bd64161f58a198e36a9983737f494642e08535
  md5: 2987b138ed84460e6898daab172e9798
  depends:
  - libgfortran5 15.1.0 hbc25352_3
  constrains:
  - libgfortran-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29043
  timestamp: 1750808952391
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-5.0.0-14_2_0_h6c33f7e_103.conda
  sha256: 8628746a8ecd311f1c0d14bb4f527c18686251538f7164982ccbe3b772de58b5
  md5: 044a210bc1d5b8367857755665157413
  depends:
  - libgfortran5 14.2.0 h6c33f7e_103
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 156291
  timestamp: 1743863532821
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
  sha256: eea6c3cf22ad739c279b4d665e6cf20f8081f483b26a96ddd67d4df3c88dfa0a
  md5: 530566b68c3b8ce7eec4cd047eae19fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 1565627
  timestamp: 1750808236464
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran5-15.1.0-hbc25352_3.conda
  sha256: c835503233b6114387e552fc549437657f893e06b684e42aff3739fef2bae235
  md5: eb1421397fe5db5ad4c3f8d611dd5117
  depends:
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 1140270
  timestamp: 1750808938364
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-14.2.0-h6c33f7e_103.conda
  sha256: 8599453990bd3a449013f5fa3d72302f1c68f0680622d419c3f751ff49f01f17
  md5: 69806c1e957069f1d515830dcc9f6cbb
  depends:
  - llvm-openmp >=8.0.0
  constrains:
  - libgfortran 5.0.0 14_2_0_*_103
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 806566
  timestamp: 1743863491726
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
  sha256: dc2752241fa3d9e40ce552c1942d0a4b5eeb93740c9723873f6fcf8d39ef8d2d
  md5: 928b8be80851f5d8ffb016f9c81dae7a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - libglx 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  size: 134712
  timestamp: 1731330998354
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgl-1.7.0-hd24410f_2.conda
  sha256: 3e954380f16255d1c8ae5da3bd3044d3576a0e1ac2e3c3ff2fe8f2f1ad2e467a
  md5: 0d00176464ebb25af83d40736a2cd3bb
  depends:
  - libglvnd 1.7.0 hd24410f_2
  - libglx 1.7.0 hd24410f_2
  license: LicenseRef-libglvnd
  size: 145442
  timestamp: 1731331005019
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
  sha256: a6b5cf4d443044bc9a0293dd12ca2015f0ebe5edfdc9c4abdde0b9947f9eb7bd
  md5: 072ab14a02164b7c0c089055368ff776
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.2 *_0
  license: LGPL-2.1-or-later
  size: 3955066
  timestamp: 1747836671118
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglib-2.84.2-hc022ef1_0.conda
  sha256: a74d52adc3b913e75185c0afaf9403c85f47c2c6ad585fdbd16f29b6c364a848
  md5: 51323eab8e9f049d001424828c4c25a4
  depends:
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.2 *_0
  license: LGPL-2.1-or-later
  size: 4016850
  timestamp: 1747836804570
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libglib-2.84.2-hbec27ea_0.conda
  sha256: 5fcc5e948706cc64e45e2454267f664ed5a1e84f15345aae04a41d852a879c0e
  md5: 7bbb8961dca1b4b9f2b01b6e722111a7
  depends:
  - __osx >=11.0
  - libffi >=3.4.6,<3.5.0a0
  - libiconv >=1.18,<2.0a0
  - libintl >=0.24.1,<1.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.2 *_0
  license: LGPL-2.1-or-later
  size: 3666180
  timestamp: 1747837044507
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
  sha256: 1175f8a7a0c68b7f81962699751bb6574e6f07db4c9f72825f978e3016f46850
  md5: 434ca7e50e40f4918ab701e3facd59a0
  depends:
  - __glibc >=2.17,<3.0.a0
  license: LicenseRef-libglvnd
  size: 132463
  timestamp: 1731330968309
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglvnd-1.7.0-hd24410f_2.conda
  sha256: 57ec3898a923d4bcc064669e90e8abfc4d1d945a13639470ba5f3748bd3090da
  md5: 9e115653741810778c9a915a2f8439e7
  license: LicenseRef-libglvnd
  size: 152135
  timestamp: 1731330986070
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
  sha256: 2d35a679624a93ce5b3e9dd301fff92343db609b79f0363e6d0ceb3a6478bfa7
  md5: c8013e438185f33b13814c5c488acd5c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - xorg-libx11 >=1.8.10,<2.0a0
  license: LicenseRef-libglvnd
  size: 75504
  timestamp: 1731330988898
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglx-1.7.0-hd24410f_2.conda
  sha256: 6591af640cb05a399fab47646025f8b1e1a06a0d4bbb4d2e320d6629b47a1c61
  md5: 1d4269e233636148696a67e2d30dad2a
  depends:
  - libglvnd 1.7.0 hd24410f_2
  - xorg-libx11 >=1.8.9,<2.0a0
  license: LicenseRef-libglvnd
  size: 77736
  timestamp: 1731330998960
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
  sha256: 43710ab4de0cd7ff8467abff8d11e7bb0e36569df04ce1c099d48601818f11d1
  md5: 3cd1a7238a0dd3d0860fdefc496cc854
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 447068
  timestamp: 1750808138400
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgomp-15.1.0-he277a41_3.conda
  sha256: a6654342666271da9c396a41ea745dc6e56574806b42abb2be61511314f5cc40
  md5: b79b8a69669f9ac6311f9ff2e6bffdf2
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 449966
  timestamp: 1750808867863
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
  sha256: 697334de4786a1067ea86853e520c64dd72b11a05137f5b318d8a444007b5e60
  md5: 2bd47db5807daade8500ed7ca4c512a4
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  size: 312184
  timestamp: 1745575272035
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgpg-error-1.55-h5ad3122_0.conda
  sha256: a744c0a137a084af7cee4a33de9bffb988182b5be4edb8a45d51d2a1efd3724c
  md5: 39f742598d0f18c8e1cb01712bc03ee8
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  license: LGPL-2.1-only
  size: 327973
  timestamp: 1745575312848
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libiconv-1.18-hc99b53d_1.conda
  sha256: 3db14977036fe1f511a6dbecacbeff3fdb58482c5c0cf87a2ea3232f5a540836
  md5: 81541d85a45fbf4d0a29346176f1f21c
  depends:
  - libgcc >=13
  license: LGPL-2.1-only
  size: 718600
  timestamp: 1740130562607
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libiconv-1.18-hfe07756_1.conda
  sha256: d30780d24bf3a30b4f116fca74dedb4199b34d500fe6c52cced5f8cc1e926f03
  md5: 450e6bdc0c7d986acf7b8443dce87111
  depends:
  - __osx >=11.0
  license: LGPL-2.1-only
  size: 681804
  timestamp: 1740128227484
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libintl-0.24.1-h493aca8_0.conda
  sha256: fb6d211d9e75e6becfbf339d255ea01f7bd3a61fe6237b3dad740de1b74b3b81
  md5: 0dca9914f2722b773c863508723dfe6e
  depends:
  - __osx >=11.0
  - libiconv >=1.18,<2.0a0
  license: LGPL-2.1-or-later
  size: 90547
  timestamp: 1746229257769
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libintl-devel-0.24.1-h493aca8_0.conda
  sha256: 5e71abebd1340f3051bcd441bbd23e97c65d6f1de1738b71aef455dde7253c65
  md5: 42ce4a88aa2fd300aa128c9c599f9676
  depends:
  - __osx >=11.0
  - libiconv >=1.18,<2.0a0
  - libintl 0.24.1 h493aca8_0
  license: LGPL-2.1-or-later
  size: 40167
  timestamp: 1746229294880
- conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
  sha256: 98b399287e27768bf79d48faba8a99a2289748c65cd342ca21033fab1860d4a4
  md5: 9fa334557db9f63da6c9285fd2a48638
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 628947
  timestamp: 1745268527144
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libjpeg-turbo-3.1.0-h86ecc28_0.conda
  sha256: c7e4f017eeadcabb30e2a95dae95aad27271d633835e55e5dae23c932ae7efab
  md5: a689388210d502364b79e8b19e7fa2cb
  depends:
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 653054
  timestamp: 1745268199701
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libjpeg-turbo-3.1.0-h5505292_0.conda
  sha256: 78df2574fa6aa5b6f5fc367c03192f8ddf8e27dc23641468d54e031ff560b9d4
  md5: 01caa4fbcaf0e6b08b3aef1151e91745
  depends:
  - __osx >=11.0
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 553624
  timestamp: 1745268405713
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_h7ac8fdf_openblas.conda
  build_number: 32
  sha256: 5b55a30ed1b3f8195dad9020fe1c6d0f514829bfaaf0cf5e393e93682af009f2
  md5: 6c3f04ccb6c578138e9f9899da0bd714
  depends:
  - libblas 3.9.0 32_h59b9bed_openblas
  constrains:
  - libcblas   3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17316
  timestamp: 1750388820745
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblapack-3.9.0-32_h411afd4_openblas.conda
  build_number: 32
  sha256: 9d88f242d138e23bcaf3c1f2d41b53cef5594b1fd9da84dd35ec7e944a946de3
  md5: 8d143759d5a22e9975a996bd13eeb8f0
  depends:
  - libblas 3.9.0 32_h1a9f1db_openblas
  constrains:
  - libcblas   3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17308
  timestamp: 1750388926844
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-32_hc9a63f6_openblas.conda
  build_number: 32
  sha256: 5e1cfa3581d1dec6b07a75084ff6cfa4b4465c646c6884a71c78a28543f83b61
  md5: bf9ead3fa92fd75ad473c6a1d255ffcb
  depends:
  - libblas 3.9.0 32_h10e41b3_openblas
  constrains:
  - blas 2.132   openblas
  - libcblas   3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17507
  timestamp: 1750388977861
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
  sha256: 498ea4b29155df69d7f20990a7028d75d91dbea24d04b2eb8a3d6ef328806849
  md5: 7d362346a479256857ab338588190da0
  depends:
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 125103
  timestamp: 1749232230009
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
  sha256: 0cb92a9e026e7bd4842f410a5c5c665c89b2eb97794ffddba519a626b8ce7285
  md5: d6df911d4564d77c4374b02552cb17d1
  depends:
  - __osx >=11.0
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 92286
  timestamp: 1749230283517
- conda: https://conda.anaconda.org/conda-forge/linux-64/libmad-0.15.1b-h0b41bf4_1001.conda
  sha256: 9e94cec54c4baadaa652c761179b8d32771fe7fa55faf6c78c2e35f942367f74
  md5: dc5cc4700f02ffeecc48253c9f29025b
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-only
  license_family: GPL
  size: 78561
  timestamp: 1670815547616
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmad-0.15.1b-hb4cce97_1001.conda
  sha256: f39c36ce40bc75bc2eb101e8569caf166d6a02e0b1ef95403146b4f9310df0e3
  md5: 10814f7a570b160cea0c885bc1c5e8d5
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-only
  license_family: GPL
  size: 79598
  timestamp: 1673354561940
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libmad-0.15.1b-h1a8c8d9_1001.conda
  sha256: 8e9209acf4bf6865760e61dc0d86116ffc9efc3e13c7f559eb1ea78532a3a625
  md5: 1eb30852ed396fbe6e301fe6d715aef9
  license: GPL-2.0-only
  license_family: GPL
  size: 77255
  timestamp: 1670815732700
- conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
  sha256: 3aa92d4074d4063f2a162cd8ecb45dccac93e543e565c01a787e16a43501f7ee
  md5: c7e925f37e3b40d893459e625f6a53f1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 91183
  timestamp: 1748393666725
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
  sha256: ef8697f934c80b347bf9d7ed45650928079e303bad01bd064995b0e3166d6e7a
  md5: 78cfed3f76d6f3f279736789d319af76
  depends:
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 114064
  timestamp: 1748393729243
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libmpdec-4.0.0-h5505292_0.conda
  sha256: 0a1875fc1642324ebd6c4ac864604f3f18f57fbcf558a8264f6ced028a3c75b2
  md5: 85ccccb47823dd9f7a99d2c7f530342f
  depends:
  - __osx >=11.0
  license: BSD-2-Clause
  license_family: BSD
  size: 71829
  timestamp: 1748393749336
- conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
  sha256: ffb066ddf2e76953f92e06677021c73c85536098f1c21fcd15360dbc859e22e4
  md5: 68e52064ed3897463c0e958ab5c8f91b
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  size: 218500
  timestamp: 1745825989535
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libogg-1.3.5-h86ecc28_1.conda
  sha256: 2c1b7c59badc2fd6c19b6926eabfce906c996068d38c2972bd1cfbe943c07420
  md5: 319df383ae401c40970ee4e9bc836c7a
  depends:
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 220653
  timestamp: 1745826021156
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libogg-1.3.5-h48c0fde_1.conda
  sha256: 28bd1fe20fe43da105da41b95ac201e95a1616126f287985df8e86ddebd1c3d8
  md5: 29b8b11f6d7e6bd0e76c029dcf9dd024
  depends:
  - __osx >=11.0
  license: BSD-3-Clause
  license_family: BSD
  size: 216719
  timestamp: 1745826006052
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_0.conda
  sha256: 225f4cfdb06b3b73f870ad86f00f49a9ca0a8a2d2afe59440521fafe2b6c23d9
  md5: 323dc8f259224d13078aaf7ce96c3efe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 5916819
  timestamp: 1750379877844
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenblas-0.3.30-pthreads_h9d3fd7e_0.conda
  sha256: be2e84de39422a8a4241ceff5145913a475571a9ed5729f435c715ad8d9db266
  md5: 7c3670fbc19809070c27948efda30c4b
  depends:
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 4960761
  timestamp: 1750379264152
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_hf332438_0.conda
  sha256: 501c8c64f1a6e6b671e49835e6c483bc25f0e7147f3eb4bbb19a4c3673dcaf28
  md5: 5d7dbaa423b4c253c476c24784286e4b
  depends:
  - __osx >=11.0
  - libgfortran 5.*
  - libgfortran5 >=13.3.0
  - llvm-openmp >=18.1.8
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 4163399
  timestamp: 1750378829050
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
  sha256: 786d43678d6d1dc5f88a6bad2d02830cfd5a0184e84a8caa45694049f0e3ea5f
  md5: b64523fb87ac6f87f0790f324ad43046
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  size: 312472
  timestamp: 1744330953241
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopus-1.5.2-h86ecc28_0.conda
  sha256: c887543068308fb0fd50175183a3513f60cd8eb1defc23adc3c89769fde80d48
  md5: 44b2cfec6e1b94723a960f8a5e6206ae
  depends:
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 357115
  timestamp: 1744331282621
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopus-1.5.2-h48c0fde_0.conda
  sha256: 3a01094a59dd59d7a5a1c8e838c2ef3fccf9e098af575c38c26fceb56c6bb917
  md5: 882feb9903f31dca2942796a360d1007
  depends:
  - __osx >=11.0
  license: BSD-3-Clause
  license_family: BSD
  size: 299498
  timestamp: 1744330988108
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
  sha256: 0bd91de9b447a2991e666f284ae8c722ffb1d84acb594dbd0c031bd656fa32b2
  md5: 70e3400cbbfa03e96dcde7fc13e38c7b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 28424
  timestamp: 1749901812541
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpciaccess-0.18-h86ecc28_0.conda
  sha256: 7641dfdfe9bda7069ae94379e9924892f0b6604c1a016a3f76b230433bb280f2
  md5: 5044e160c5306968d956c2a0a2a440d6
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 29512
  timestamp: 1749901899881
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
  sha256: c8f5dc929ba5fcee525a66777498e03bbcbfefc05a0773e5163bb08ac5122f1a
  md5: 37511c874cf3b8d0034c8d24e73c0884
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 289506
  timestamp: 1750095629466
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpng-1.6.49-hec79eb8_0.conda
  sha256: d9b2e2c1b47ea8889fa4407ad2ffbf6388b9608031a98acd9f9876d0b15a20cc
  md5: a665eccfe09f815de0cdda657598a5b3
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 293525
  timestamp: 1750097792167
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libpng-1.6.49-h3783ad8_0.conda
  sha256: b1050f6da51de507eec6902367cc2a3f381dd548eaaccb85673784543dcdee1a
  md5: 90be56ffd1a6b1950268f88c12e17c69
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 259291
  timestamp: 1750095759683
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
  sha256: f709cbede3d4f3aee4e2f8d60bd9e256057f410bd60b8964cb8cf82ec1457573
  md5: ef1910918dd895516a769ed36b5b3a4e
  depends:
  - lame >=3.100,<3.101.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 354372
  timestamp: 1695747735668
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsndfile-1.2.2-h79657aa_1.conda
  sha256: 8fcd5e45d6fb071e8baf492ebb8710203fd5eedf0cb791e007265db373c89942
  md5: ad8e62c0faec46b1442f960489c80b49
  depends:
  - lame >=3.100,<3.101.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 396501
  timestamp: 1695747749825
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsndfile-1.2.2-h9739721_1.conda
  sha256: e559f2f72bb03a554aa5b74230fa19160d33c7981ed385294f1eea9a5871cc03
  md5: 77d552455cbc52e089cdb9df5b283199
  depends:
  - lame >=3.100,<3.101.0a0
  - libcxx >=15.0.7
  - libflac >=1.4.3,<1.5.0a0
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 317185
  timestamp: 1695747981394
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
  sha256: 0105bd108f19ea8e6a78d2d994a6d4a8db16d19a41212070d2d1d48a63c34161
  md5: a587892d3c13b6621a6091be690dbca2
  depends:
  - libgcc-ng >=12
  license: ISC
  size: 205978
  timestamp: 1716828628198
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsodium-1.0.20-h68df207_0.conda
  sha256: 448df5ea3c5cf1af785aad46858d7a5be0522f4234a4dc9bb764f4d11ff3b981
  md5: 2e4a8f23bebdcb85ca8e5a0fbe75666a
  depends:
  - libgcc-ng >=12
  license: ISC
  size: 177394
  timestamp: 1716828514515
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsodium-1.0.20-h99b78c6_0.conda
  sha256: fade8223e1e1004367d7101dd17261003b60aa576df6d7802191f8972f7470b1
  md5: a7ce36e284c5faaf93c220dfc39e3abd
  depends:
  - __osx >=11.0
  license: ISC
  size: 164972
  timestamp: 1716828607917
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
  sha256: 9a9e5bf30178f821d4f8de25eac0ae848915bfde6a78a66ae8b77d9c33d9d0e5
  md5: c7c4888059a8324e52de475d1e7bdc53
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  size: 919723
  timestamp: 1750925531920
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.1-h4970e9a_7.conda
  sha256: 772350ba2252059871247c5f78410882fa1a25476e31e55da39aa4a634893e76
  md5: d758d556022c04b155a3525fe365e5c2
  depends:
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  size: 922189
  timestamp: 1750925625197
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.1-h6fb428d_7.conda
  sha256: a007d6aa37586d3a71ff9503e6ec70baac64fc40241c668a39581399502940ec
  md5: b351c3e11d75aab0b8145de530afbe58
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  size: 902114
  timestamp: 1750925723817
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
  sha256: 7650837344b7850b62fdba02155da0b159cf472b9ab59eb7b472f7bd01dff241
  md5: 6d11a5edae89fe413c0569f16d308f5a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 3896407
  timestamp: 1750808251302
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
  sha256: 916a8c2530992140d23c4d3f63502f250ff36df7298ed9a8b72d3629c347d4ce
  md5: 4e2d5a407e0ecfe493d8b2a65a437bd8
  depends:
  - libgcc 15.1.0 he277a41_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 3833339
  timestamp: 1750808947966
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
  sha256: bbaea1ecf973a7836f92b8ebecc94d3c758414f4de39d2cc6818a3d10cb3216b
  md5: 57541755b5a51691955012b8e197c06c
  depends:
  - libstdcxx 15.1.0 h8f9b012_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29093
  timestamp: 1750808292700
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
  sha256: cb93360dce004fda2f877fd6071c8c0d19ab67b161ff406d5c0d63b7658ad77c
  md5: f981af71cbd4c67c9e6acc7d4cc3f163
  depends:
  - libstdcxx 15.1.0 h3f4de04_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29078
  timestamp: 1750808974598
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
  sha256: e26b22c0ae40fb6ad4356104d5fa4ec33fe8dd8a10e6aef36a9ab0c6a6f47275
  md5: 1e12c8aa74fa4c3166a9bdc135bc4abf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  - libgcrypt-lib >=1.11.1,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: LGPL-2.1-or-later
  size: 487969
  timestamp: 1750949895969
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsystemd0-257.7-h2bb824b_0.conda
  sha256: 35ecfc98c22d4f035b051fe72398206607d48944e7bd4f60431e63eb95538e0d
  md5: 63b49a2d12a1739f72be430c2ed58727
  depends:
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  - libgcrypt-lib >=1.11.1,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: LGPL-2.1-or-later
  size: 510879
  timestamp: 1750949944203
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
  sha256: 7fa6ddac72e0d803bb08e55090a8f2e71769f1eb7adbd5711bdd7789561601b1
  md5: e79a094918988bb1807462cd42c83962
  depends:
  - __glibc >=2.17,<3.0.a0
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 429575
  timestamp: 1747067001268
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtiff-4.7.0-h7c15681_5.conda
  sha256: 4b2c6f5cd5199d5e345228a0422ecb31a4340ff69579db49faccba14186bb9a2
  md5: 264a9aac20276b1784dac8c5f8d3704a
  depends:
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 466229
  timestamp: 1747067015512
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libtiff-4.7.0-h2f21f7c_5.conda
  sha256: cc5ee1cffb8a8afb25a4bfd08fce97c5447f97aa7064a055cb4a617df45bc848
  md5: 4eb183bbf7f734f69875702fdbe17ea0
  depends:
  - __osx >=11.0
  - lerc >=4.0.0,<5.0a0
  - libcxx >=18
  - libdeflate >=1.24,<1.25.0a0
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 370943
  timestamp: 1747067160710
- conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
  sha256: 3fca2655f4cf2ce6b618a2b52e3dce92f27f429732b88080567d5bbeea404c82
  md5: 5a23e52bd654a5297bd3e247eebab5a3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 143533
  timestamp: 1750949902296
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libudev1-257.7-h7b9e449_0.conda
  sha256: 8c9847a934e251479f343f0be3b771836cdccfcf132145bd2da34946acd01988
  md5: d19d804623b40d7ab5f807c240b4caaf
  depends:
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 154447
  timestamp: 1750949949664
- conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
  sha256: f2ac872920833960e514ce9efd8f7c08ce66dd870738d73839d1bce1ac497de6
  md5: a730b2badd586580c5752cc73842e068
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 75491
  timestamp: 1638450786937
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libunwind-1.6.2-h01db608_0.tar.bz2
  sha256: 7862d36ffc9f6b2ed3381ce77c78b9e5691d7353a19dd2050630868e192adf6f
  md5: 93b7bbf9099cfe09e67c0abe34bb7885
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 90479
  timestamp: 1638452154070
- conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
  sha256: d1922de78ead6a9d19b7a4f82cf1fff7332e9012fd9968aa835c89888628d3d7
  md5: 1a11973f25f6168f4f6a65883cf7bb2a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 124944
  timestamp: 1748686602857
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liburing-2.10-h17cf362_0.conda
  sha256: 658d4138e5cd76794c81c3561361fd9d2091c9a13a329cb5b61a83371feeef4e
  md5: 49a98ebf80bbc4661df8a26706d8b3b5
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 126811
  timestamp: 1748687566969
- conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
  sha256: 89c84f5b26028a9d0f5c4014330703e7dff73ba0c98f90103e9cef6b43a5323c
  md5: d17e3fb595a9f24fa9e149239a33475d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libudev1 >=257.4
  license: LGPL-2.1-or-later
  size: 89551
  timestamp: 1748856210075
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libusb-1.0.29-h06eaf92_0.conda
  sha256: a60aae6b529cd7caa7842f9781ef95b93014e618f71fb005e404af434d76a33f
  md5: 9a86e7473e16fe25c5c47f6c1376ac82
  depends:
  - libgcc >=13
  - libudev1 >=257.4
  license: LGPL-2.1-or-later
  size: 93129
  timestamp: 1748856228398
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libusb-1.0.29-hbc156a2_0.conda
  sha256: 5eee9a2bf359e474d4548874bcfc8d29ebad0d9ba015314439c256904e40aaad
  md5: f6654e9e96e9d973981b3b2f898a5bfa
  depends:
  - __osx >=11.0
  license: LGPL-2.1-or-later
  size: 83849
  timestamp: 1748856224950
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
  sha256: 616277b0c5f7616c2cdf36f6c316ea3f9aa5bb35f2d4476a349ab58b9b91675f
  md5: 000e30b09db0b7c775b21695dff30969
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 35720
  timestamp: 1680113474501
- conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
  sha256: 53080d72388a57b3c31ad5805c93a7328e46ff22fab7c44ad2a86d712740af33
  md5: 309dec04b70a3cc0f1e84a4013683bc0
  depends:
  - libgcc-ng >=9.3.0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  size: 286280
  timestamp: 1610609811627
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvorbis-1.3.7-h01db608_0.tar.bz2
  sha256: 1ade4727be5c52b287001b8094d02af66342dfe0ba13ef69222aaaf2e9be4342
  md5: c2863ff72c6d8a59054f8b9102c206e9
  depends:
  - libgcc-ng >=9.3.0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  size: 292082
  timestamp: 1610616294416
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libvorbis-1.3.7-h9f76cd9_0.tar.bz2
  sha256: 60457217e20d8b24a8390c81338a8fa69c8656b440c067cd82f802a09da93cb9
  md5: 92a1a88d1a1d468c19d9e1659ac8d3df
  depends:
  - libcxx >=11.0.0
  - libogg >=1.3.4,<1.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 254839
  timestamp: 1610609991029
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
  sha256: c45283fd3e90df5f0bd3dbcd31f59cdd2b001d424cf30a07223655413b158eaf
  md5: 63f790534398730f59e1b899c3644d4a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  size: 429973
  timestamp: 1734777489810
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libwebp-base-1.5.0-h0886dbf_0.conda
  sha256: b3d881a0ae08bb07fff7fa8ead506c8d2e0388733182fe4f216f3ec5d61ffcf0
  md5: 95ef4a689b8cc1b7e18b53784d88f96b
  depends:
  - libgcc >=13
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  size: 362623
  timestamp: 1734779054659
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libwebp-base-1.5.0-h2471fea_0.conda
  sha256: f8bdb876b4bc8cb5df47c28af29188de8911c3fea4b799a33743500149de3f4a
  md5: 569466afeb84f90d5bb88c11cc23d746
  depends:
  - __osx >=11.0
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  size: 290013
  timestamp: 1734777593617
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxcb-1.17.0-h262b8f6_0.conda
  sha256: 461cab3d5650ac6db73a367de5c8eca50363966e862dcf60181d693236b1ae7b
  md5: cd14ee5cca2464a425b1dbfc24d90db2
  depends:
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 397493
  timestamp: 1727280745441
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
  sha256: a8043a46157511b3ceb6573a99952b5c0232313283f2d6a066cec7c8dcaed7d0
  md5: fedf6bfe5d21d21d2b1785ec00a8889a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  size: 707156
  timestamp: 1747911059945
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxkbcommon-1.10.0-hbab7b08_0.conda
  sha256: 620f16864f4f9d7181d89fa4266dba0b18cb9bca72f930500cf9307e549e4247
  md5: 36cd1db31e923c6068b7e0e6fce2cd7b
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  size: 719116
  timestamp: 1747911079432
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
  sha256: b0b3a96791fa8bb4ec030295e8c8bf2d3278f33c0f9ad540e73b5e538e6268e7
  md5: 14dbe05b929e329dbaa6f2d0aa19466d
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 690864
  timestamp: 1746634244154
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxml2-2.13.8-he060846_0.conda
  sha256: b453be503923e213ce5132de0b2e2bc89549d742dcc403acba8dcc4a0ced740c
  md5: c73dfe6886cc8d39a09c357a36f91fb2
  depends:
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 733785
  timestamp: 1746634366734
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
  sha256: 5a2c1eeef69342e88a98d1d95bff1603727ab1ff4ee0e421522acd8813439b84
  md5: 08aad7cbe9f5a6b460d0976076b6ae64
  depends:
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 66657
  timestamp: 1727963199518
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
  sha256: ce34669eadaba351cd54910743e6a2261b67009624dbc7daeeafdef93616711b
  md5: 369964e85dc26bfe78f41399b366c435
  depends:
  - __osx >=11.0
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 46438
  timestamp: 1727963202283
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-20.1.7-hdb05f8b_0.conda
  sha256: e7d95b50a90cdc9e0fc38bc37f493a61b9d08164114b562bbd9ff0034f45eca2
  md5: 741e1da0a0798d32e13e3724f2ca2dcf
  depends:
  - __osx >=11.0
  constrains:
  - openmp 20.1.7|20.1.7.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  size: 281996
  timestamp: 1749892286735
- conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
  sha256: 47326f811392a5fd3055f0f773036c392d26fdb32e4d8e7a8197eed951489346
  md5: 9de5350a85c4a20c685259b889aa6393
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 167055
  timestamp: 1733741040117
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lz4-c-1.10.0-h5ad3122_1.conda
  sha256: 67e55058d275beea76c1882399640c37b5be8be4eb39354c94b610928e9a0573
  md5: 6654e411da94011e8fbe004eacb8fe11
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 184953
  timestamp: 1733740984533
- conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 311e01e00ce7302eb97c263c021700c7baa1a6d9be9477f60edcfd17fbf4b49d
  depends:
  - max-core ==25.5.0.dev2025062705 release
  - max-python ==25.5.0.dev2025062705 release
  - mojo-jupyter ==25.5.0.dev2025062705 release
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 9411
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025062705-release.conda
  sha256: be1ef195c01fd7253ad1fb4b4ec647708bd53cbe31a2e97318943fed4062f92c
  depends:
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 222775006
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/linux-aarch64/max-core-25.5.0.dev2025062705-release.conda
  sha256: d7aa1cc232011221751258767cf91a7a6facd799cae6b1503c467b87d0980eda
  depends:
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 224027056
  timestamp: 1751001477655
- conda: https://conda.modular.com/max-nightly/osx-arm64/max-core-25.5.0.dev2025062705-release.conda
  sha256: b0a7c5a1b315f54fb376271a904d103ddba4f113ceeafd4d298bedc8e2e40ea9
  depends:
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 187571251
  timestamp: 1751002248746
- conda: https://conda.modular.com/max-nightly/linux-64/max-python-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 9204326c477affb8fd8f9c84de91591a21bd1cf24f0d4390716b3ca642cdb711
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025062705 release
  constrains:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 30541775
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/linux-aarch64/max-python-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 9341747ee3d40a553adf8ec0c9608dd5efefb4dfee6367587ac4e2bdfbd73d10
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025062705 release
  constrains:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 19347501
  timestamp: 1751001477655
- conda: https://conda.modular.com/max-nightly/osx-arm64/max-python-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 08f15fc80c1bdb33d7a35e8b58c04ed454cf80f7b0360f56429ef0b9c3b4e65b
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025062705 release
  constrains:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 15993002
  timestamp: 1751002248746
- conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 20b01c20917066a7f186385ae19a017c091e094dbddb4c59f1737bf2eed0a9fe
  depends:
  - python >=3.9,<3.14
  - click >=8.0.0
  - mypy_extensions >=0.4.3
  - packaging >=22.0
  - pathspec >=0.9.0
  - platformdirs >=2
  - typing_extensions >=v4.12.2
  - python
  license: MIT
  size: 131254
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 27842df9aea46c60c8d6487e38c44b18c5aa533e7e84f3d98c5ee7fd978862a4
  depends:
  - max-core ==25.5.0.dev2025062705 release
  - python >=3.9,<3.14
  - jupyter_client >=8.6.2,<8.7
  - python
  license: LicenseRef-Modular-Proprietary
  size: 22485
  timestamp: 1751001417267
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
  sha256: 39c4700fb3fbe403a77d8cc27352fa72ba744db487559d5d44bf8411bb4ea200
  md5: c7f302fd11eeb0987a6a5e1f3aed6a21
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-only
  license_family: LGPL
  size: 491140
  timestamp: 1730581373280
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpg123-1.32.9-h65af167_0.conda
  sha256: d65d5a00278544639ba4f99887154be00a1f57afb0b34d80b08e5cba40a17072
  md5: cdf140c7690ab0132106d3bc48bce47d
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-only
  license_family: LGPL
  size: 558708
  timestamp: 1730581372400
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/mpg123-1.32.9-hf642e45_0.conda
  sha256: 070bbbbb96856c325c0b6637638ce535afdc49adbaff306e2238c6032d28dddf
  md5: d2b4857bdc3b76c36e23236172d09840
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: LGPL-2.1-only
  license_family: LGPL
  size: 360712
  timestamp: 1730581491116
- conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
  sha256: 6ed158e4e5dd8f6a10ad9e525631e35cee8557718f83de7a4e3966b1f772c4b1
  md5: e9c622e0d00fa24a6292279af3ab6d06
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11766
  timestamp: 1745776666688
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
  sha256: 91cfb655a68b0353b2833521dc919188db3d8a7f4c64bea2c6a7557b24747468
  md5: 182afabe009dc78d8b73100255ee6868
  depends:
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  size: 926034
  timestamp: 1738196018799
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
  sha256: 2827ada40e8d9ca69a153a45f7fd14f32b2ead7045d3bbb5d10964898fe65733
  md5: 068d497125e4bf8a66bf707254fff5ae
  depends:
  - __osx >=11.0
  license: X11 AND BSD-3-Clause
  size: 797030
  timestamp: 1738196177597
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.0-py313h17eae1a_0.conda
  sha256: 8b88ade24df5229c5d76c5ef09568ae4630b1095982e94648fbbeb18f475aa61
  md5: db18a34466bef0863e9301b518a75e8f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 8545037
  timestamp: 1749430954481
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/numpy-2.3.0-py313hcf1be6b_0.conda
  sha256: 56f93763364ca7c6bc85c18b906d06ed0798edc888f2bcd863736cd39d2cc0ca
  md5: 234faee9472d58370838acee3030b3ef
  depends:
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 7198706
  timestamp: 1749430953292
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.0-py313h41a2e72_0.conda
  sha256: d473005786a27cf4e1430d45a99a61626c2fbf61eb25b4d021cee8d217b973d2
  md5: 0dc3aa075f3e64bdda6e779e2cbf5aa9
  depends:
  - __osx >=11.0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libcxx >=18
  - liblapack >=3.9.0,<4.0a0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 6525213
  timestamp: 1749430964570
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
  sha256: b4491077c494dbf0b5eaa6d87738c22f2154e9277e5293175ec187634bd808a0
  md5: de356753cfdbffcde5bb1e86e3aa6cd0
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 3117410
  timestamp: 1746223723843
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.0-hd08dc88_1.conda
  sha256: 58120daf06a52ba203f94eccb43900213a9f2b3cc310bbaa868505ccd7afbdaa
  md5: ee68fdc3a8723e9c58bdd2f10544658f
  depends:
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 3642633
  timestamp: 1746225726804
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.0-h81ee809_1.conda
  sha256: 73d366c1597a10bcd5f3604b5f0734b31c23225536e03782c6a13f9be9d01bff
  md5: 5c7aef00ef60738a14e0e612cfc5bcde
  depends:
  - __osx >=11.0
  - ca-certificates
  license: Apache-2.0
  license_family: Apache
  size: 3064197
  timestamp: 1746223530698
- conda: https://conda.anaconda.org/conda-forge/linux-64/opusfile-0.12-h3358134_2.conda
  sha256: f4df9df880e405e5c856383f869d5b9d434f78fb7c234c9e7b099ab604fb7fc3
  md5: 5931bcae00b98f952696b6bcdd0be34b
  depends:
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - openssl >=3.0.7,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 65901
  timestamp: 1670387479735
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/opusfile-0.12-hf55b2d5_2.conda
  sha256: a0ffa8054df68fad5f3533338557c7b985480ee3cf39f0e251ee6b03ff6896cf
  md5: a9a71d77aec174e4532f91f560bc413b
  depends:
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - openssl >=3.0.7,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 91662
  timestamp: 1673436651852
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/opusfile-0.12-h5643135_2.conda
  sha256: 108dbee936a8e3c21d2aa5618326343844df8f1fe14067c4dc5a731d7945ecc0
  md5: e34e472ae04beeb642c5e937a2aeeebf
  depends:
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - openssl >=3.0.7,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 80128
  timestamp: 1670387790769
- conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
  sha256: 289861ed0c13a15d7bbb408796af4de72c2fe67e2bcb0de98f4c3fce259d7991
  md5: 58335b26c38bf4a20f399384c33cbcf9
  depends:
  - python >=3.8
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 62477
  timestamp: 1745345660407
- conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9f64009cdf5b8e529995f18e03665b03f5d07c0b17445b8badef45bde76249ee
  md5: 617f15191456cc6a13db418a275435e5
  depends:
  - python >=3.9
  license: MPL-2.0
  license_family: MOZILLA
  size: 41075
  timestamp: 1733233471940
- conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
  sha256: 27c4014f616326240dcce17b5f3baca3953b6bc5f245ceb49c3fa1e6320571eb
  md5: b90bece58b4c2bf25969b70f3be42d25
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 1197308
  timestamp: 1745955064657
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pcre2-10.45-hf4ec17f_0.conda
  sha256: d5aecfcb64514719600e35290cc885098dbfef8e9c037eea6afc43d1acc65c2e
  md5: ad22a9a9497f7aedce73e0da53cd215f
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 1134832
  timestamp: 1745955178803
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pcre2-10.45-ha881caa_0.conda
  sha256: e9ecb706b58b5a2047c077b3a1470e8554f3aad02e9c3c00cfa35d537420fea3
  md5: a52385b93558d8e6bbaeec5d61a21cd7
  depends:
  - __osx >=11.0
  - bzip2 >=1.0.8,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 837826
  timestamp: 1745955207242
- conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
  sha256: 6cb261595b5f0ae7306599f2bb55ef6863534b6d4d1bc0dcfdfa5825b0e4e53d
  md5: 39b4228a867772d610c02e06f939a5b8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 402222
  timestamp: 1749552884791
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pixman-0.46.2-h86a87f0_0.conda
  sha256: df60bb320bbec8df804780c0310b471478a245192c16568769fc96269ce15445
  md5: 019114cf59c0cce5a08f6661179a1d65
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 310404
  timestamp: 1749554318638
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pixman-0.46.2-h2f9eb0b_0.conda
  sha256: 68d1eef12946d779ce4b4b9de88bc295d07adce5dd825a0baf0e1d7cf69bc5a6
  md5: 0587a57e200568a71982173c07684423
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: MIT
  license_family: MIT
  size: 214660
  timestamp: 1749553221709
- conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
  sha256: 0f48999a28019c329cd3f6fd2f01f09fc32cc832f7d6bbe38087ddac858feaa3
  md5: 424844562f5d337077b445ec6b1398a7
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 23531
  timestamp: 1746710438805
- conda: https://conda.anaconda.org/conda-forge/linux-64/portaudio-19.6.0-h7c63dc7_9.conda
  sha256: c09ae032d0303abfea34c0957834538b48133b0431283852741ed3e0f66fdb36
  md5: 893f2c33af6b03cfd04820a8c31f5798
  depends:
  - alsa-lib >=1.2.10,<1.3.0.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 115512
  timestamp: **********
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/portaudio-19.6.0-h5c6c0ed_9.conda
  sha256: a73e31c5fe9d717cd42470b394018f4e48eed4a439b9371d2c6d380c86aed591
  md5: ab049f8223bccc6f621975beaa75c624
  depends:
  - alsa-lib >=1.2.10,<1.3.0.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 118203
  timestamp: 1693868376750
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/portaudio-19.6.0-h13dd4ca_9.conda
  sha256: 5ff2b55d685c29dfe632ef856796a4b862305088543d4982f0b807e8d9bb756e
  md5: d325d46394b6c46d15718c855fb20b4a
  depends:
  - libcxx >=15.0.7
  license: MIT
  license_family: MIT
  size: 78863
  timestamp: 1693868663440
- conda: https://conda.anaconda.org/conda-forge/linux-64/portmidi-2.0.6-hf4617a5_0.conda
  sha256: 4b16598ded9fd0636ace2e9f6c31835ccbeaccc3ae284457c9aea9d810f558db
  md5: 4461787d1087ad942cc2ea151fd422c3
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.14,<1.3.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 43976
  timestamp: 1750372118122
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/portmidi-2.0.6-h9d01bbc_0.conda
  sha256: bc05e2ccbccc58418b1e9d3b5250971f1f11006a64ebfe8868f5a8c8484a6835
  md5: 4111a69e6af9ef2020489c7488990cab
  depends:
  - alsa-lib >=1.2.14,<1.3.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 45388
  timestamp: 1750373583071
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/portmidi-2.0.6-h286801f_0.conda
  sha256: adddc071e03a799d5e18d6f1fa0f0d6fc9a99b3f7a6796b18a12a7b5e3f05885
  md5: 5985a83d15a126fe0c9c6abf7ff3d1df
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: MIT
  license_family: MIT
  size: 41407
  timestamp: 1750372262579
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pthread-stubs-0.4-h86ecc28_1002.conda
  sha256: 977dfb0cb3935d748521dd80262fe7169ab82920afd38ed14b7fee2ea5ec01ba
  md5: bb5a90c93e3bac3d5690acf76b4a6386
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 8342
  timestamp: 1726803319942
- conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
  sha256: d2377bb571932f2373f593b7b2fc3b9728dc6ae5b993b1b65d7f2c8bb39a0b49
  md5: 66b1fa9608d8836e25f9919159adc9c6
  depends:
  - __glibc >=2.17,<3.0.a0
  - dbus >=1.13.6,<2.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libiconv >=1.18,<2.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libsystemd0 >=257.4
  - libxcb >=1.17.0,<2.0a0
  constrains:
  - pulseaudio 17.0 *_1
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 764231
  timestamp: 1742507189208
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pulseaudio-client-17.0-h2f84921_1.conda
  sha256: 0294728d0a2fc0bdfbcfda98b7ada2d8bb420f76c944fa041ece4140858c2ee5
  md5: a617203ec445f510a3a8651810c735e0
  depends:
  - dbus >=1.13.6,<2.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libiconv >=1.18,<2.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libsystemd0 >=257.4
  - libxcb >=1.17.0,<2.0a0
  constrains:
  - pulseaudio 17.0 *_1
  license: LGPL-2.1-or-later
  license_family: LGPL
  size: 763814
  timestamp: 1742507234837
- conda: https://conda.anaconda.org/conda-forge/linux-64/pygame-2.6.1-py313h5fadf01_0.conda
  sha256: 4abc53097c01ec0445e3074d4d31d9374487f65364409e45eff7b9b6a4cc2db2
  md5: abbff23fe8a066e795467fc46518132c
  depends:
  - __glibc >=2.17,<3.0.a0
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.44,<1.7.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - portmidi >=2.0.4,<3.0a0
  - python >=3.13.0rc2,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - sdl2 >=2.30.7,<3.0a0
  - sdl2_image >=2.8.2,<3.0a0
  - sdl2_mixer >=2.6.3,<3.0a0
  - sdl2_ttf >=2.22.0,<3.0a0
  license: LGPL-2.1-only
  size: 2992551
  timestamp: 1727636691329
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pygame-2.6.1-py313h50090c8_0.conda
  sha256: 04fc5f5e58f125f1598bbb003bc4d09a2f1987e88b3317abffd1686d8a6d9329
  md5: 489ca30b219323a2e68fddb384a1676e
  depends:
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.44,<1.7.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - portmidi >=2.0.4,<3.0a0
  - python >=3.13.0rc2,<3.14.0a0
  - python >=3.13.0rc2,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - sdl2 >=2.30.7,<3.0a0
  - sdl2_image >=2.8.2,<3.0a0
  - sdl2_mixer >=2.6.3,<3.0a0
  - sdl2_ttf >=2.22.0,<3.0a0
  license: LGPL-2.1-only
  size: 3061802
  timestamp: 1727636868836
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pygame-2.6.1-py313h55b729e_0.conda
  sha256: 6d4beca5e827c8abb2eb1ef61d13d67debc46b502e9e714ac6b8bffe15a3f4bf
  md5: 6f1be59f963eefe8c5d2fae1edac3841
  depends:
  - __osx >=11.0
  - fontconfig >=2.14.2,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - libcxx >=17
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.44,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  - portmidi >=2.0.4,<3.0a0
  - python >=3.13.0rc2,<3.14.0a0
  - python >=3.13.0rc2,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - sdl2 >=2.30.7,<3.0a0
  - sdl2_image >=2.8.2,<3.0a0
  - sdl2_mixer >=2.6.3,<3.0a0
  - sdl2_ttf >=2.22.0,<3.0a0
  license: LGPL-2.1-only
  size: 2817914
  timestamp: 1727636943537
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
  build_number: 102
  sha256: c2cdcc98ea3cbf78240624e4077e164dc9d5588eefb044b4097c3df54d24d504
  md5: 89e07d92cf50743886f41638d58c4328
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  size: 33273132
  timestamp: 1750064035176
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
  build_number: 102
  sha256: 2eb3ce8b2acf036bd30d4d41cfb45766ad817e26479f18177cfb950c0af6f27b
  md5: ed5b16381ac28233a65c549a59d97b68
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-aarch64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  size: 33764400
  timestamp: 1750062474929
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.13.5-hf3f3da0_102_cp313.conda
  build_number: 102
  sha256: ee1b09fb5563be8509bb9b29b2b436a0af75488b5f1fa6bcd93fe0fba597d13f
  md5: 123b7f04e7b8d6fc206cf2d3466f8a4b
  depends:
  - __osx >=11.0
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  size: 12931515
  timestamp: 1750062475020
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
  sha256: a50052536f1ef8516ed11a844f9413661829aa083304dc624c5925298d078d79
  md5: 5ba79d7c71f03c678c8ead841f347d6e
  depends:
  - python >=3.9
  - six >=1.5
  license: Apache-2.0
  license_family: APACHE
  size: 222505
  timestamp: 1733215763718
- conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
  sha256: ac6cf618100c2e0cad1cabfe2c44bf4a944aa07bb1dc43abff73373351a7d079
  md5: 2eabcede0db21acee23c181db58b4128
  depends:
  - cpython 3.13.5.*
  - python_abi * *_cp313
  license: Python-2.0
  size: 47572
  timestamp: 1750062593102
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
  build_number: 7
  sha256: 0595134584589064f56e67d3de1d8fcbb673a972946bce25fb593fb092fdcd97
  md5: e84b44e6300f1703cb25d29120c5b1d8
  constrains:
  - python 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 6988
  timestamp: 1745258852285
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py313h8e95178_0.conda
  sha256: 6446721c43ba540c02ced4dde564f5a9a0131e40aa406e8af6313084c4a2024f
  md5: c912a00e5cb59357ef55b7930a48cf48
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 384549
  timestamp: 1749898593849
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyzmq-27.0.0-py313h6e72e74_0.conda
  sha256: dbe4072369da72df5017204457d3dbe1c7a302ac8aca9a4fe52015f4b6ad2c2d
  md5: d901488aaa8c5f0dbdd2d5848796c9ff
  depends:
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 381357
  timestamp: 1749900828011
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyzmq-27.0.0-py313he6960b1_0.conda
  sha256: da722b8ee2785d764182c2d3b9007fb5ef8bc4096f5fc018fd3b3026719b1ee7
  md5: 2cacb246854e185506768b3f7ae23a69
  depends:
  - __osx >=11.0
  - libcxx >=18
  - libsodium >=1.0.20,<1.0.21.0a0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 363932
  timestamp: 1749899287142
- conda: https://conda.anaconda.org/conda-forge/linux-64/rav1e-0.7.1-h8fae777_3.conda
  sha256: 6e5e704c1c21f820d760e56082b276deaf2b53cf9b751772761c3088a365f6f4
  md5: 2c42649888aac645608191ffdc80d13a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - __glibc >=2.17
  license: BSD-2-Clause
  license_family: BSD
  size: 5176669
  timestamp: 1746622023242
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/rav1e-0.7.1-ha3529ed_3.conda
  sha256: f1631eb0be7391b0f470fdd7c902741551eb00381efd52b234ceadfccf34588b
  md5: 0a6e034273782e6e863d46f1d2a5078b
  depends:
  - libgcc >=13
  constrains:
  - __glibc >=2.17
  license: BSD-2-Clause
  license_family: BSD
  size: 4822159
  timestamp: 1746621943955
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/rav1e-0.7.1-h0716509_3.conda
  sha256: 65f862b2b31ef2b557990a82015cbd41e5a66041c2f79b4451dd14b4595d4c04
  md5: 7b37f30516100b86ea522350c8cab44c
  depends:
  - __osx >=11.0
  constrains:
  - __osx >=11.0
  license: BSD-2-Clause
  license_family: BSD
  size: 856271
  timestamp: 1746622200646
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
  sha256: 54bed3a3041befaa9f5acde4a37b1a02f44705b7796689574bcf9d7beaad2959
  md5: c0f08fc2737967edde1a272d4bf41ed9
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 291806
  timestamp: 1740380591358
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
  sha256: 7db04684d3904f6151eff8673270922d31da1eea7fa73254d01c437f49702e34
  md5: 63ef3f6e6d6d5c589e64f11263dc5676
  depends:
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 252359
  timestamp: 1740379663071
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
  sha256: 7cd82ca1d1989de6ac28e72ba0bfaae1c055278f931b0c7ef51bb1abba3ddd2f
  md5: 91f8537d64c4d52cbbb2910e8bd61bd2
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - sdl3 >=3.2.10,<4.0a0
  - libgl >=1.7.0,<2.0a0
  - libegl >=1.7.0,<2.0a0
  license: Zlib
  size: 587053
  timestamp: 1745799881584
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2-2.32.54-h5ad3122_0.conda
  sha256: d83c13fc35ed447d186150d32b8bc48bdd73a047280ba6e06f151d4cce52639d
  md5: 6b38021cb802b4e5bede7fe38c547883
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - libegl >=1.7.0,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - sdl3 >=3.2.10,<4.0a0
  license: Zlib
  size: 597383
  timestamp: 1745799910298
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl2-2.32.54-ha1acc90_0.conda
  sha256: ba0ba41b3f7404ddc5421885ad9efe346c4bdc2ec88bc43edd271d9f25f6f0e4
  md5: 71364ba4c5f333860c4431cb46cb9b6c
  depends:
  - libcxx >=18
  - __osx >=11.0
  - sdl3 >=3.2.10,<4.0a0
  license: Zlib
  size: 546209
  timestamp: 1745799899902
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2_image-2.8.2-h06ee604_1.conda
  sha256: f18184e016e2e57306d1540dea584d38f4617d7ddb6aad4af6b5f21c52fa39ea
  md5: 65e113270b460dcdfc4dc0a80bb3d11c
  depends:
  - libavif16 >=1.0.4,<2.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libstdcxx-ng >=12
  - libtiff >=4.6.0,<4.8.0a0
  - libwebp-base >=1.4.0,<2.0a0
  - libzlib >=1.2.13,<2.0.0a0
  - sdl2 >=2.30.2,<3.0a0
  license: Zlib
  size: 152110
  timestamp: 1716857107234
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2_image-2.8.2-hd95cb85_1.conda
  sha256: 8bdd72e4789616b0db5fc8e756a4156d3bd7fd35cf96c1cb892d9f6bff3f6508
  md5: 45fbcda052d64b07fa601d965a41cb69
  depends:
  - libavif16 >=1.0.4,<2.0a0
  - libgcc-ng >=12
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libstdcxx-ng >=12
  - libtiff >=4.6.0,<4.8.0a0
  - libwebp-base >=1.4.0,<2.0a0
  - libzlib >=1.2.13,<2.0.0a0
  - sdl2 >=2.30.2,<3.0a0
  license: Zlib
  size: 151138
  timestamp: 1716858240454
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl2_image-2.8.2-h376e2e1_1.conda
  sha256: 8c385478a7b6a6e34b3b3f3d48ed48f504698544987ad331f03a7b43b11d689a
  md5: 247d2c5a873901ef51f378c69a2c708e
  depends:
  - __osx >=11.0
  - libavif16 >=1.0.4,<2.0a0
  - libcxx >=16
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libtiff >=4.6.0,<4.8.0a0
  - libwebp-base >=1.4.0,<2.0a0
  - libzlib >=1.2.13,<2.0.0a0
  - sdl2 >=2.30.2,<3.0a0
  license: Zlib
  size: 118041
  timestamp: 1716857215118
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2_mixer-2.6.3-h8830914_1.conda
  sha256: c3e99e222b091f26cfd1d6be22c5a2973df9e7caa020262f9d9523f340344a95
  md5: 1a2b60be4d860a0c419a87176c85c3ad
  depends:
  - fluidsynth >=2.3.4,<2.4.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libmad >=0.15.1b,<0.16.0a0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  - opusfile >=0.12,<0.13.0a0
  - sdl2 >=2.28.3,<3.0a0
  license: Zlib
  size: 202966
  timestamp: 1695761744535
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2_mixer-2.6.3-h422cae6_1.conda
  sha256: 8fa4580bddd4d33f5fbddf5c54873613a7f8fcd9f781656fbf9fd1b27975b196
  md5: 75e56f84030bd1244d8bff3c55e8418e
  depends:
  - fluidsynth >=2.3.4,<2.4.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libmad >=0.15.1b,<0.16.0a0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  - opusfile >=0.12,<0.13.0a0
  - sdl2 >=2.28.3,<3.0a0
  license: Zlib
  size: 238285
  timestamp: 1695761803447
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl2_mixer-2.6.3-h4fe3bdc_1.conda
  sha256: fcaaf1b589aed11498f5b9735b996fb92ff18e4673a4c804bbfa28eb00264e06
  md5: b92222911d46f08faa583df51191bd7f
  depends:
  - fluidsynth >=2.3.4,<2.4.0a0
  - libcxx >=15.0.7
  - libflac >=1.4.3,<1.5.0a0
  - libmad >=0.15.1b,<0.16.0a0
  - libogg >=1.3.4,<1.4.0a0
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  - opusfile >=0.12,<0.13.0a0
  - sdl2 >=2.28.3,<3.0a0
  license: Zlib
  size: 188504
  timestamp: 1695762176058
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2_ttf-2.24.0-hc1d668e_1.conda
  sha256: 5b72b99576455ba53cc294c8aa1ab389d6302a1072c90f76268257e70343fcfe
  md5: 0385816e000e885b420cdcfc1b7de3a3
  depends:
  - __glibc >=2.17,<3.0.a0
  - freetype >=2.13.3,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - sdl2 >=2.32.50,<3.0a0
  license: Zlib
  size: 62419
  timestamp: 1743198496689
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2_ttf-2.24.0-hc709ae0_1.conda
  sha256: 75f7cc4a22e853564a9e9c6bbc0af53b58d90b99be0a9ccb08ea57893f403aa5
  md5: cbee5c80c0bc1cf491715446f5c7cac9
  depends:
  - freetype >=2.13.3,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - sdl2 >=2.32.50,<3.0a0
  license: Zlib
  size: 56059
  timestamp: 1743199607984
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl2_ttf-2.24.0-h49b19f9_1.conda
  sha256: 8682907cc92eca66771e4fe951feb97396a1d76b73a6d910f3888d93951a3dc2
  md5: a9a40538ce016f25b160e80a193f4075
  depends:
  - __osx >=11.0
  - freetype >=2.13.3,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - sdl2 >=2.32.50,<3.0a0
  license: Zlib
  size: 45515
  timestamp: 1743198654173
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
  sha256: 7fe5ff84801d1ad0713efbb1a9c39c3c4245ccee5586bd62fc4604d0f23ce0df
  md5: c3ab38fdbcf36625620c9a4df786320a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - dbus >=1.16.2,<2.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - libxkbcommon >=1.10.0,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - wayland >=1.23.1,<2.0a0
  - libusb >=1.0.29,<2.0a0
  - libudev1 >=257.6
  - libegl >=1.7.0,<2.0a0
  - liburing >=2.10,<2.11.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxscrnsaver >=1.2.4,<2.0a0
  - libdrm >=2.4.124,<2.5.0a0
  - libunwind >=1.6.2,<1.7.0a0
  license: Zlib
  size: 1941645
  timestamp: 1748911618893
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl3-3.2.16-h7e2c5d6_0.conda
  sha256: 782c6ac4f96e2b75cf55afdd223ffd043fc459a5a7c7b460c4077764733d5bd9
  md5: 37fbfe4c4baa10eca13823b535ec5d1a
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - libxkbcommon >=1.10.0,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - libunwind >=1.6.2,<1.7.0a0
  - libusb >=1.0.29,<2.0a0
  - libudev1 >=257.6
  - wayland >=1.23.1,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - dbus >=1.16.2,<2.0a0
  - liburing >=2.10,<2.11.0a0
  - libegl >=1.7.0,<2.0a0
  - libdrm >=2.4.124,<2.5.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  license: Zlib
  size: 1899812
  timestamp: 1748911660322
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/sdl3-3.2.16-h92d3ae7_0.conda
  sha256: 423dcd03b46603e8c8284b51ed0ca8756bb33d0e228e5a2cf6d45a264ff2925e
  md5: 22fa0445fc15cd2ccda8efe12f536540
  depends:
  - __osx >=11.0
  - libcxx >=18
  - dbus >=1.16.2,<2.0a0
  - libusb >=1.0.29,<2.0a0
  license: Zlib
  size: 1419368
  timestamp: 1748911641937
- conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
  sha256: 41db0180680cc67c3fa76544ffd48d6a5679d96f4b71d7498a759e94edc9a2db
  md5: a451d576819089b0d672f18768be0f65
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 16385
  timestamp: 1733381032766
- conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
  sha256: fb4b97a3fd259eff4849b2cfe5678ced0c5792b697eb1f7bcd93a4230e90e80e
  md5: 0096882bd623e6cc09e8bf920fc8fb47
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 2750235
  timestamp: 1742907589246
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/svt-av1-3.0.2-h5ad3122_0.conda
  sha256: 6d2ac9e4f68355ba3b42395054a7558b9eb6bcf3d70e91bb99ada1450a74d2f6
  md5: 4fafb3aafa73a875312cb4a1099d2a46
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 1975547
  timestamp: 1742910351387
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/svt-av1-3.0.2-h8ab69cd_0.conda
  sha256: d6bb376dc9a00728be26be2b1b859d13534067922c13cc4adbbc441ca4c4ca6d
  md5: 76f20156833dea73510379b6cd7975e5
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: BSD-2-Clause
  license_family: BSD
  size: 1484549
  timestamp: 1742907655838
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3285204
  timestamp: 1748387766691
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
  sha256: 46e10488e9254092c655257c18fcec0a9864043bdfbe935a9fbf4fb2028b8514
  md5: 2562c9bfd1de3f9c590f0fe53858d85c
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3342845
  timestamp: 1748393219221
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
  sha256: cb86c522576fa95c6db4c878849af0bccfd3264daf0cc40dd18e7f4a7bfced0e
  md5: 7362396c170252e7b7b0c8fb37fe9c78
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3125538
  timestamp: 1748388189063
- conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py313h536fd9c_0.conda
  sha256: 282c9c3380217119c779fc4c432b0e4e1e42e9a6265bfe36b6f17f6b5d4e6614
  md5: e9434a5155db25c38ade26f71a2f5a48
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 873269
  timestamp: 1748003477089
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tornado-6.5.1-py313h6a51379_0.conda
  sha256: 819497d044a23d6d69fa09aaf7f4d59b6c9db6443d6e32691ccb3361849e3979
  md5: efd003285041d23e604630b18f24b9dd
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 876988
  timestamp: 1748005374481
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/tornado-6.5.1-py313h90d716c_0.conda
  sha256: 29c623cfb1f9ea7c1d865cf5f52ae6faa6497ceddbe7841ae27901a21f8cf79f
  md5: 1ab3bef3e9aa0bba9eee2dfbedab1dba
  depends:
  - __osx >=11.0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 874352
  timestamp: 1748003547444
- conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
  sha256: f39a5620c6e8e9e98357507262a7869de2ae8cc07da8b7f84e517c9fd6c2b959
  md5: 019a7385be9af33791c989871317e1ed
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 110051
  timestamp: 1733367480074
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
  sha256: 8561db52f278c5716b436da6d4ee5521712a49e8f3c70fcae5350f5ebb4be41c
  md5: 2adcd9bb86f656d3d43bf84af59a1faf
  depends:
  - python >=3.9
  - python
  license: PSF-2.0
  license_family: PSF
  size: 50978
  timestamp: 1748959427551
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
  sha256: 73d809ec8056c2f08e077f9d779d7f4e4c2b625881cad6af303c33dc1562ea01
  md5: a37843723437ba75f42c9270ffe800b1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 321099
  timestamp: 1745806602179
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/wayland-1.23.1-h698ed42_1.conda
  sha256: b4f36d5acd06945d0fe4da61ec469fc0f0948458172d13013dabb30854f1ccd3
  md5: 229b00f81a229af79547a7e4776ccf6e
  depends:
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 324694
  timestamp: 1745806658759
- conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
  sha256: a5d4af601f71805ec67403406e147c48d6bad7aaeae92b0622b7e2396842d3fe
  md5: 397a013c2dc5145a70737871aaa87e98
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  size: 392406
  timestamp: 1749375847832
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xkeyboard-config-2.45-h86ecc28_0.conda
  sha256: 730ff2f6fbfecce94db54bbf3f1ae0ce79c54b6abc089f8a65a041525228d454
  md5: 01251d1503a253e39be4fa9bcf447d63
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  size: 392754
  timestamp: 1749375869926
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
  sha256: c12396aabb21244c212e488bbdc4abcdef0b7404b15761d9329f5a4a39113c4b
  md5: fb901ff28063514abb6046c9ec2c4a45
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 58628
  timestamp: 1734227592886
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libice-1.1.2-h86ecc28_0.conda
  sha256: a2ba1864403c7eb4194dacbfe2777acf3d596feae43aada8d1b478617ce45031
  md5: c8d8ec3e00cd0fd8a231789b91a7c5b7
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 60433
  timestamp: 1734229908988
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
  sha256: 277841c43a39f738927145930ff963c5ce4c4dacf66637a3d95d802a64173250
  md5: 1c74ff8c35dcadf952a16f752ca5aa49
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  size: 27590
  timestamp: 1741896361728
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libsm-1.2.6-h0808dbd_0.conda
  sha256: b86a819cd16f90c01d9d81892155126d01555a20dabd5f3091da59d6309afd0a
  md5: 2d1409c50882819cb1af2de82e2b7208
  depends:
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  size: 28701
  timestamp: 1741897678254
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
  sha256: 51909270b1a6c5474ed3978628b341b4d4472cd22610e5f22b506855a5e20f67
  md5: db038ce880f100acc74dba10302b5630
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  size: 835896
  timestamp: 1741901112627
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libx11-1.8.12-hca56bd8_0.conda
  sha256: 452977d8ad96f04ec668ba74f46e70a53e00f99c0e0307956aeca75894c8131d
  md5: 3df132f0048b9639bc091ef22937c111
  depends:
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  size: 864850
  timestamp: 1741901264068
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxau-1.0.12-h86ecc28_0.conda
  sha256: 7829a0019b99ba462aece7592d2d7f42e12d12ccd3b9614e529de6ddba453685
  md5: d5397424399a66d33c80b1f2345a36a6
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 15873
  timestamp: 1734230458294
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
  sha256: 832f538ade441b1eee863c8c91af9e69b356cd3e9e1350fff4fe36cc573fc91a
  md5: 2ccd714aa2242315acaf0a67faea780b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  size: 32533
  timestamp: 1730908305254
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxcursor-1.2.3-h86ecc28_0.conda
  sha256: c5d3692520762322a9598e7448492309f5ee9d8f3aff72d787cf06e77c42507f
  md5: f2054759c2203d12d0007005e1f1296d
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.9,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  size: 34596
  timestamp: 1730908388714
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxdmcp-1.1.5-h57736b2_0.conda
  sha256: efcc150da5926cf244f757b8376d96a4db78bc15b8d90ca9f56ac6e75755971f
  md5: 25a5a7b797fe6e084e04ffe2db02fc62
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 20615
  timestamp: 1727796660574
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
  sha256: da5dc921c017c05f38a38bd75245017463104457b63a1ce633ed41f214159c14
  md5: febbab7d15033c913d53c7a2c102309d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 50060
  timestamp: 1727752228921
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxext-1.3.6-h57736b2_0.conda
  sha256: 8e216b024f52e367463b4173f237af97cf7053c77d9ce3e958bc62473a053f71
  md5: bd1e86dd8aa3afd78a4bfdb4ef918165
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.9,<2.0a0
  license: MIT
  license_family: MIT
  size: 50746
  timestamp: 1727754268156
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
  sha256: 2fef37e660985794617716eb915865ce157004a4d567ed35ec16514960ae9271
  md5: 4bdb303603e9821baf5fe5fdff1dc8f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 19575
  timestamp: 1727794961233
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxfixes-6.0.1-h57736b2_0.conda
  sha256: f5c71e0555681a82a65c483374b91d91b2cb9a9903b3a22ddc00f36719fce549
  md5: 78f8715c002cc66991d7c11e3cf66039
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.9,<2.0a0
  license: MIT
  license_family: MIT
  size: 20289
  timestamp: 1727796500830
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
  sha256: 044c7b3153c224c6cedd4484dd91b389d2d7fd9c776ad0f4a34f099b3389f4a1
  md5: 96d57aba173e878a2089d5638016dc5e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 33005
  timestamp: 1734229037766
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxrender-0.9.12-h86ecc28_0.conda
  sha256: ffd77ee860c9635a28cfda46163dcfe9224dc6248c62404c544ae6b564a0be1f
  md5: ae2c2dd0e2d38d249887727db2af960e
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 33649
  timestamp: 1734229123157
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
  sha256: 58e8fc1687534124832d22e102f098b5401173212ac69eb9fd96b16a3e2c8cb2
  md5: 303f7a0e9e0cd7d250bb6b952cecda90
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  license: MIT
  license_family: MIT
  size: 14412
  timestamp: 1727899730073
- conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
  sha256: a4dc72c96848f764bb5a5176aa93dd1e9b9e52804137b99daeebba277b31ea10
  md5: 3947a35e916fcc6b9825449affbf4214
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  size: 335400
  timestamp: 1731585026517
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zeromq-4.3.5-h5efb499_7.conda
  sha256: a6003096dc0570a86492040ba32b04ce7662b159600be2252b7a0dfb9414e21c
  md5: f2f3282559a4b87b7256ecafb4610107
  depends:
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  size: 371419
  timestamp: 1731589490850
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zeromq-4.3.5-hc1bb282_7.conda
  sha256: 9e585569fe2e7d3bea71972cd4b9f06b1a7ab8fa7c5139f92a31cbceecf25a8a
  md5: f7e6b65943cb73bce0143737fded08f1
  depends:
  - __osx >=11.0
  - krb5 >=1.21.3,<1.22.0a0
  - libcxx >=18
  - libsodium >=1.0.20,<1.0.21.0a0
  license: MPL-2.0
  license_family: MOZILLA
  size: 281565
  timestamp: 1731585108039
- conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
  sha256: 7560d21e1b021fd40b65bfb72f67945a3fcb83d78ad7ccf37b8b3165ec3b68ad
  md5: df5e78d904988eb55042c0c97446079f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 22963
  timestamp: 1749421737203
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 567578
  timestamp: 1742433379869
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstd-1.5.7-hbcf94c1_2.conda
  sha256: 0812e7b45f087cfdd288690ada718ce5e13e8263312e03b643dd7aa50d08b51b
  md5: 5be90c5a3e4b43c53e38f50a85e11527
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 551176
  timestamp: 1742433378347
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstd-1.5.7-h6491c7d_2.conda
  sha256: 0d02046f57f7a1a3feae3e9d1aa2113788311f3cf37a3244c71e61a93177ba67
  md5: e6f69c7bcccdefa417f056fa593b40f0
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 399979
  timestamp: 1742433432699
