# Mojo code examples

A collection of sample programs written in the
[Mojo](https://docs.modular.com/mojo/manual/) programming language.

## Getting Started

The easiest way to get started with Mojo is to install the
[Pixi](https://pixi.sh/latest/) package and virtual environment manager:

```sh
curl -fsSL https://pixi.sh/install.sh | sh
```

Then use `git` to clone this repository of Mojo samples using the command
below:

```bash
git clone https://github.com/modular/modular.git
```

## Running

Navigate into the `examples` directory and use `pixi run` to invoke `mojo` and
run the basic example programs. For example:

```bash
pixi run mojo matmul.mojo
```

## License

The Mojo examples in this repository are licensed under the Apache License v2.0
with LLVM Exceptions (see the LLVM [License](https://llvm.org/LICENSE.txt)).

## Contributing

As a contributor, your efforts and expertise are invaluable in driving the
evolution of the Mojo programming language. The [Mojo contributor
guide](../CONTRIBUTING.md) provides all the information necessary to make
meaningful contributions—from understanding the submission process to
adhering to best practices:

- [Mojo contributor guide](../CONTRIBUTING.md)
