[project]
authors = [{name = "Modu<PERSON>", email = "<EMAIL>"}]
dependencies = []
name = "Python to Mojo interoperability"
requires-python = ">= 3.11"
version = "0.1.0"

[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[tool.pixi.project]
channels = ["conda-forge", "https://conda.modular.com/max-nightly/"]
platforms = ["osx-arm64", "linux-aarch64", "linux-64"]

[tool.pixi.dependencies]
max = "*"

[tool.pixi.tasks]
hello = "python hello.py"
mandelbrot = "python mandelbrot.py"
