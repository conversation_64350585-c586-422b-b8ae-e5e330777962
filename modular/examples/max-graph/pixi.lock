version: 6
environments:
  default:
    channels:
    - url: https://conda.modular.com/max-nightly/
    - url: https://conda.anaconda.org/conda-forge/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_h59b9bed_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_he106b2a_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_h7ac8fdf_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.0-py313h17eae1a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py313h8e95178_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      linux-aarch64:
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/keyutils-1.6.1-h4e544f5_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/krb5-1.21.3-h50a48e9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.43-h5e2c951_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libblas-3.9.0-32_h1a9f1db_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcblas-3.9.0-32_hab92f65_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libedit-3.1.20250104-pl5321h976ea20_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran5-15.1.0-hbc25352_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgomp-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblapack-3.9.0-32_h411afd4_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenblas-0.3.30-pthreads_h9d3fd7e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsodium-1.0.20-h68df207_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.1-h4970e9a_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-aarch64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-aarch64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/numpy-2.3.0-py313hcf1be6b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.0-hd08dc88_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyzmq-27.0.0-py313h6e72e74_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tornado-6.5.1-py313h6a51379_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zeromq-4.3.5-h5efb499_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      osx-arm64:
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/krb5-1.21.3-h237132a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-32_h10e41b3_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-32_hb3479ef_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-20.1.7-ha82da77_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libedit-3.1.20250104-pl5321hafb1f1b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.0-h286801f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-5.0.0-14_2_0_h6c33f7e_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-14.2.0-h6c33f7e_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-32_hc9a63f6_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libmpdec-4.0.0-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_hf332438_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsodium-1.0.20-h99b78c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.1-h6fb428d_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-20.1.7-hdb05f8b_0.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/osx-arm64/max-core-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/osx-arm64/max-python-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.0-py313h41a2e72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.0-h81ee809_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.13.5-hf3f3da0_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyzmq-27.0.0-py313he6960b1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tornado-6.5.1-py313h90d716c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zeromq-4.3.5-hc1bb282_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  size: 23621
  timestamp: 1650670423406
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: 3702bef2f0a4d38bd8288bbe54aace623602a1343c2cfbefd3fa188e015bebf0
  md5: 6168d71addc746e8f2b8d57dfd2edcea
  depends:
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  size: 23712
  timestamp: 1650670790230
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
  sha256: 2258b0b33e1cb3a9852d47557984abb6e7ea58e3d7f92706ec1f8e879290c4cb
  md5: 56398c28220513b9ea13d7b450acfb20
  depends:
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 189884
  timestamp: 1720974504976
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
  sha256: adfa71f158cbd872a36394c56c3568e6034aa55c623634b37a4836bd036e6b91
  md5: fc6948412dbbbe9a4c9ddbbcfe0a79ab
  depends:
  - __osx >=11.0
  license: bzip2-1.0.6
  license_family: BSD
  size: 122909
  timestamp: 1720974522888
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
  sha256: 7cfec9804c84844ea544d98bda1d9121672b66ff7149141b8415ca42dfcd44f6
  md5: 72525f07d72806e3b639ad4504c30ce5
  depends:
  - __unix
  license: ISC
  size: 151069
  timestamp: 1749990087500
- conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
  sha256: 8aee789c82d8fdd997840c952a586db63c6890b00e88c4fb6e80a38edd5f51c0
  md5: 94b550b8d3a614dbd326af798c7dfb40
  depends:
  - __unix
  - python >=3.10
  license: BSD-3-Clause
  license_family: BSD
  size: 87749
  timestamp: 1747811451319
- conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
  sha256: ab29d57dc70786c1269633ba3dff20288b81664d3ff8d21af995742e2bb03287
  md5: 962b9857ee8e7018c22f2776ffa0b2d7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 27011
  timestamp: 1733218222191
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
  noarch: generic
  sha256: 058c8156ff880b1180a36b94307baad91f9130d0e3019ad8c7ade035852016fb
  md5: 0401f31e3c9e48cebf215472aa3e7104
  depends:
  - python >=3.13,<3.14.0a0
  - python_abi * *_cp313
  license: Python-2.0
  size: 47560
  timestamp: 1750062514868
- conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
  sha256: ce61f4f99401a4bd455b89909153b40b9c823276aefcbb06f2044618696009ca
  md5: 72e42d28960d875c7654614f8b50939a
  depends:
  - python >=3.9
  - typing_extensions >=4.6.0
  license: MIT and PSF-2.0
  size: 21284
  timestamp: 1746947398083
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
  sha256: 813298f2e54ef087dbfc9cc2e56e08ded41de65cff34c639cc8ba4e27e4540c9
  md5: 268203e8b983fddb6412b36f2024e75c
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12282786
  timestamp: 1720853454991
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
  sha256: c18ab120a0613ada4391b15981d86ff777b5690ca461ea7e9e49531e8f374745
  md5: 63ccfdc3a3ce25b027b8767eb722fca8
  depends:
  - python >=3.9
  - zipp >=3.20
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 34641
  timestamp: 1747934053147
- conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
  sha256: 0ec8f4d02053cd03b0f3e63168316530949484f80e16f5e2fb199a1d117a89ca
  md5: 6837f3eff7dcea42ecd714ce1ac2b108
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11474
  timestamp: 1733223232820
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
  sha256: 19d8bd5bb2fde910ec59e081eeb59529491995ce0d653a5209366611023a0b3a
  md5: 4ebae00eae9705b0c3d6d1018a81d047
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-dateutil >=2.8.2
  - pyzmq >=23.0
  - tornado >=6.2
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 106342
  timestamp: 1733441040958
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
  sha256: 56a7a7e907f15cca8c4f9b0c99488276d4cb10821d2d15df9245662184872e81
  md5: b7d89d860ebcda28a5303526cdee68ab
  depends:
  - __unix
  - platformdirs >=2.5
  - python >=3.8
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 59562
  timestamp: 1748333186063
- conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 117831
  timestamp: 1646151697040
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/keyutils-1.6.1-h4e544f5_0.tar.bz2
  sha256: 6d4233d97a9b38acbb26e1268bcf8c10a8e79c2aed7e5a385ec3769967e3e65b
  md5: 1f24853e59c68892452ef94ddd8afd4b
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 112327
  timestamp: 1646166857935
- conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
  sha256: 99df692f7a8a5c27cd14b5fb1374ee55e756631b9c3d659ed3ee60830249b238
  md5: 3f43953b7d3fb3aaa1d0d0723d91e368
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1370023
  timestamp: 1719463201255
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/krb5-1.21.3-h50a48e9_0.conda
  sha256: 0ec272afcf7ea7fbf007e07a3b4678384b7da4047348107b2ae02630a570a815
  md5: 29c10432a2ca1472b53f299ffb2ffa37
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1474620
  timestamp: 1719463205834
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/krb5-1.21.3-h237132a_0.conda
  sha256: 4442f957c3c77d69d9da3521268cad5d54c9033f1a73f99cde0a3658937b159b
  md5: c6dc8a0fdec13a0565936655c33069a1
  depends:
  - __osx >=11.0
  - libcxx >=16
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1155530
  timestamp: 1719463474401
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
  sha256: dcd2b1a065bbf5c54004ddf6551c775a8eb6993c8298ca8a6b92041ed413f785
  md5: 6dc9e1305e7d3129af4ad0dabda30e56
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  size: 670635
  timestamp: 1749858327854
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.43-h5e2c951_5.conda
  sha256: 0cb7f3a3281e86c850ea09e0dbd0c3652eaee367aa089170d7f6bec07ff9fc07
  md5: e62696c21a84af63cfc49f4b5428a36a
  constrains:
  - binutils_impl_linux-aarch64 2.43
  license: GPL-3.0-only
  license_family: GPL
  size: 699857
  timestamp: 1749858448061
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_h59b9bed_openblas.conda
  build_number: 32
  sha256: 1540bf739feb446ff71163923e7f044e867d163c50b605c8b421c55ff39aa338
  md5: 2af9f3d5c2e39f417ce040f5a35c40c6
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - libcblas   3.9.0   32*_openblas
  - mkl <2025
  - liblapacke 3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17330
  timestamp: 1750388798074
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libblas-3.9.0-32_h1a9f1db_openblas.conda
  build_number: 32
  sha256: a257f0c43dd142be7eab85bf78999a869a6938ddb2415202f74724ff51dff316
  md5: 833718ed1c0b597ce17e5f410bd9b017
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - libcblas   3.9.0   32*_openblas
  - liblapack  3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  - mkl <2025
  license: BSD-3-Clause
  license_family: BSD
  size: 17341
  timestamp: 1750388911474
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-32_h10e41b3_openblas.conda
  build_number: 32
  sha256: 2775472dd81d43dc20804b484028560bfecd5ab4779e39f1fb95684da3ff2029
  md5: d4a1732d2b330c9d5d4be16438a0ac78
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - mkl <2025
  - libcblas   3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17520
  timestamp: 1750388963178
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_he106b2a_openblas.conda
  build_number: 32
  sha256: 92a001fc181e6abe4f4a672b81d9413ca2f22609f8a95327dfcc6eee593ffeb9
  md5: 3d3f9355e52f269cd8bc2c440d8a5263
  depends:
  - libblas 3.9.0 32_h59b9bed_openblas
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17308
  timestamp: 1750388809353
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcblas-3.9.0-32_hab92f65_openblas.conda
  build_number: 32
  sha256: e902de3cd34d4fc1f7d15b9c9c1d297d90043d5283d28db410d32e45ea4e1a33
  md5: 2f02a3ea0960118a0a8d45cdd348b039
  depends:
  - libblas 3.9.0 32_h1a9f1db_openblas
  constrains:
  - liblapack  3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17335
  timestamp: 1750388919351
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-32_hb3479ef_openblas.conda
  build_number: 32
  sha256: 25d46ace14c3ac45d4aa18b5f7a0d3d30cec422297e900f8b97a66334232061c
  md5: d8e8ba717ae863b13a7495221f2b5a71
  depends:
  - libblas 3.9.0 32_h10e41b3_openblas
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17485
  timestamp: 1750388970626
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-20.1.7-ha82da77_0.conda
  sha256: a3fd34773f1252a4f089e74a075ff5f0f6b878aede097e83a405f35687c36f24
  md5: 881de227abdddbe596239fa9e82eb3ab
  depends:
  - __osx >=11.0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 567189
  timestamp: 1749847129529
- conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
  sha256: d789471216e7aba3c184cd054ed61ce3f6dac6f87a50ec69291b9297f8c18724
  md5: c277e0a4d549b03ac1e9d6cbbe3d017b
  depends:
  - ncurses
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 134676
  timestamp: 1738479519902
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libedit-3.1.20250104-pl5321h976ea20_0.conda
  sha256: c0b27546aa3a23d47919226b3a1635fccdb4f24b94e72e206a751b33f46fd8d6
  md5: fb640d776fc92b682a14e001980825b1
  depends:
  - ncurses
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 148125
  timestamp: 1738479808948
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libedit-3.1.20250104-pl5321hafb1f1b_0.conda
  sha256: 66aa216a403de0bb0c1340a88d1a06adaff66bae2cfd196731aa24db9859d631
  md5: 44083d2d2c2025afca315c7a172eab2b
  depends:
  - ncurses
  - __osx >=11.0
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 107691
  timestamp: 1738479560845
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
  sha256: 33ab03438aee65d6aa667cf7d90c91e5e7d734c19a67aa4c7040742c0a13d505
  md5: db0bfbe7dd197b68ad5f30333bae6ce0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  size: 74427
  timestamp: 1743431794976
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
  sha256: e3a0d95fe787cccf286f5dced9fa9586465d3cd5ec8e04f7ad7f0e72c4afd089
  md5: d41a057e7968705dae8dcb7c8ba2c8dd
  depends:
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  size: 73155
  timestamp: 1743432002397
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.0-h286801f_0.conda
  sha256: ee550e44765a7bbcb2a0216c063dcd53ac914a7be5386dd0554bd06e6be61840
  md5: 6934bbb74380e045741eb8637641a65b
  depends:
  - __osx >=11.0
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  size: 65714
  timestamp: 1743431789879
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
  sha256: 608b8c8b0315423e524b48733d91edd43f95cb3354a765322ac306a858c2cd2e
  md5: 15a131f30cae36e9a655ca81fee9a285
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 55847
  timestamp: 1743434586764
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
  sha256: c6a530924a9b14e193ea9adfe92843de2a806d1b7dbfd341546ece9653129e60
  md5: c215a60c2935b517dcda8cad4705734d
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 39839
  timestamp: 1743434670405
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
  sha256: 59a87161212abe8acc57d318b0cc8636eb834cdfdfddcf1f588b5493644b39a3
  md5: 9e60c55e725c20d23125a5f0dd69af5d
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_3
  - libgomp 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 824921
  timestamp: 1750808216066
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
  sha256: a08e3f89d4cd7c5e18a7098419e378a8506ebfaf4dc1eaac59bf7b962ca66cdb
  md5: 409b902521be20c2efb69d2e0c5e3bc8
  depends:
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 15.1.0 he277a41_3
  - libgcc-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 510464
  timestamp: 1750808926824
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
  sha256: b0b0a5ee6ce645a09578fc1cb70c180723346f8a45fdb6d23b3520591c6d6996
  md5: e66f2b8ad787e7beb0f846e4bd7e8493
  depends:
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29033
  timestamp: 1750808224854
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
  sha256: 222eedd38467f7af8fb703c16cc1abf83038e7b6a09f707bbb4340e8ed589e14
  md5: 831062d3b6a4cdfdde1015be90016102
  depends:
  - libgcc 15.1.0 he277a41_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29009
  timestamp: 1750808930406
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
  sha256: 77dd1f1efd327e6991e87f09c7c97c4ae1cfbe59d9485c41d339d6391ac9c183
  md5: bfbca721fd33188ef923dfe9ba172f29
  depends:
  - libgfortran5 15.1.0 hcea5267_3
  constrains:
  - libgfortran-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29057
  timestamp: 1750808257258
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran-15.1.0-he9431aa_3.conda
  sha256: 9459e5e273397ee6680ea2f1f4bd64161f58a198e36a9983737f494642e08535
  md5: 2987b138ed84460e6898daab172e9798
  depends:
  - libgfortran5 15.1.0 hbc25352_3
  constrains:
  - libgfortran-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29043
  timestamp: 1750808952391
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-5.0.0-14_2_0_h6c33f7e_103.conda
  sha256: 8628746a8ecd311f1c0d14bb4f527c18686251538f7164982ccbe3b772de58b5
  md5: 044a210bc1d5b8367857755665157413
  depends:
  - libgfortran5 14.2.0 h6c33f7e_103
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 156291
  timestamp: 1743863532821
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
  sha256: eea6c3cf22ad739c279b4d665e6cf20f8081f483b26a96ddd67d4df3c88dfa0a
  md5: 530566b68c3b8ce7eec4cd047eae19fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 1565627
  timestamp: 1750808236464
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgfortran5-15.1.0-hbc25352_3.conda
  sha256: c835503233b6114387e552fc549437657f893e06b684e42aff3739fef2bae235
  md5: eb1421397fe5db5ad4c3f8d611dd5117
  depends:
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 1140270
  timestamp: 1750808938364
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-14.2.0-h6c33f7e_103.conda
  sha256: 8599453990bd3a449013f5fa3d72302f1c68f0680622d419c3f751ff49f01f17
  md5: 69806c1e957069f1d515830dcc9f6cbb
  depends:
  - llvm-openmp >=8.0.0
  constrains:
  - libgfortran 5.0.0 14_2_0_*_103
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 806566
  timestamp: 1743863491726
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
  sha256: 43710ab4de0cd7ff8467abff8d11e7bb0e36569df04ce1c099d48601818f11d1
  md5: 3cd1a7238a0dd3d0860fdefc496cc854
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 447068
  timestamp: 1750808138400
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgomp-15.1.0-he277a41_3.conda
  sha256: a6654342666271da9c396a41ea745dc6e56574806b42abb2be61511314f5cc40
  md5: b79b8a69669f9ac6311f9ff2e6bffdf2
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 449966
  timestamp: 1750808867863
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_h7ac8fdf_openblas.conda
  build_number: 32
  sha256: 5b55a30ed1b3f8195dad9020fe1c6d0f514829bfaaf0cf5e393e93682af009f2
  md5: 6c3f04ccb6c578138e9f9899da0bd714
  depends:
  - libblas 3.9.0 32_h59b9bed_openblas
  constrains:
  - libcblas   3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17316
  timestamp: 1750388820745
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblapack-3.9.0-32_h411afd4_openblas.conda
  build_number: 32
  sha256: 9d88f242d138e23bcaf3c1f2d41b53cef5594b1fd9da84dd35ec7e944a946de3
  md5: 8d143759d5a22e9975a996bd13eeb8f0
  depends:
  - libblas 3.9.0 32_h1a9f1db_openblas
  constrains:
  - libcblas   3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17308
  timestamp: 1750388926844
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-32_hc9a63f6_openblas.conda
  build_number: 32
  sha256: 5e1cfa3581d1dec6b07a75084ff6cfa4b4465c646c6884a71c78a28543f83b61
  md5: bf9ead3fa92fd75ad473c6a1d255ffcb
  depends:
  - libblas 3.9.0 32_h10e41b3_openblas
  constrains:
  - blas 2.132   openblas
  - libcblas   3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17507
  timestamp: 1750388977861
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
  sha256: 498ea4b29155df69d7f20990a7028d75d91dbea24d04b2eb8a3d6ef328806849
  md5: 7d362346a479256857ab338588190da0
  depends:
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 125103
  timestamp: 1749232230009
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
  sha256: 0cb92a9e026e7bd4842f410a5c5c665c89b2eb97794ffddba519a626b8ce7285
  md5: d6df911d4564d77c4374b02552cb17d1
  depends:
  - __osx >=11.0
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 92286
  timestamp: 1749230283517
- conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
  sha256: 3aa92d4074d4063f2a162cd8ecb45dccac93e543e565c01a787e16a43501f7ee
  md5: c7e925f37e3b40d893459e625f6a53f1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 91183
  timestamp: 1748393666725
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
  sha256: ef8697f934c80b347bf9d7ed45650928079e303bad01bd064995b0e3166d6e7a
  md5: 78cfed3f76d6f3f279736789d319af76
  depends:
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 114064
  timestamp: 1748393729243
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libmpdec-4.0.0-h5505292_0.conda
  sha256: 0a1875fc1642324ebd6c4ac864604f3f18f57fbcf558a8264f6ced028a3c75b2
  md5: 85ccccb47823dd9f7a99d2c7f530342f
  depends:
  - __osx >=11.0
  license: BSD-2-Clause
  license_family: BSD
  size: 71829
  timestamp: 1748393749336
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_0.conda
  sha256: 225f4cfdb06b3b73f870ad86f00f49a9ca0a8a2d2afe59440521fafe2b6c23d9
  md5: 323dc8f259224d13078aaf7ce96c3efe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 5916819
  timestamp: 1750379877844
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenblas-0.3.30-pthreads_h9d3fd7e_0.conda
  sha256: be2e84de39422a8a4241ceff5145913a475571a9ed5729f435c715ad8d9db266
  md5: 7c3670fbc19809070c27948efda30c4b
  depends:
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 4960761
  timestamp: 1750379264152
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_hf332438_0.conda
  sha256: 501c8c64f1a6e6b671e49835e6c483bc25f0e7147f3eb4bbb19a4c3673dcaf28
  md5: 5d7dbaa423b4c253c476c24784286e4b
  depends:
  - __osx >=11.0
  - libgfortran 5.*
  - libgfortran5 >=13.3.0
  - llvm-openmp >=18.1.8
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 4163399
  timestamp: 1750378829050
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
  sha256: 0105bd108f19ea8e6a78d2d994a6d4a8db16d19a41212070d2d1d48a63c34161
  md5: a587892d3c13b6621a6091be690dbca2
  depends:
  - libgcc-ng >=12
  license: ISC
  size: 205978
  timestamp: 1716828628198
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsodium-1.0.20-h68df207_0.conda
  sha256: 448df5ea3c5cf1af785aad46858d7a5be0522f4234a4dc9bb764f4d11ff3b981
  md5: 2e4a8f23bebdcb85ca8e5a0fbe75666a
  depends:
  - libgcc-ng >=12
  license: ISC
  size: 177394
  timestamp: 1716828514515
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsodium-1.0.20-h99b78c6_0.conda
  sha256: fade8223e1e1004367d7101dd17261003b60aa576df6d7802191f8972f7470b1
  md5: a7ce36e284c5faaf93c220dfc39e3abd
  depends:
  - __osx >=11.0
  license: ISC
  size: 164972
  timestamp: 1716828607917
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
  sha256: 9a9e5bf30178f821d4f8de25eac0ae848915bfde6a78a66ae8b77d9c33d9d0e5
  md5: c7c4888059a8324e52de475d1e7bdc53
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  size: 919723
  timestamp: 1750925531920
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.1-h4970e9a_7.conda
  sha256: 772350ba2252059871247c5f78410882fa1a25476e31e55da39aa4a634893e76
  md5: d758d556022c04b155a3525fe365e5c2
  depends:
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  size: 922189
  timestamp: 1750925625197
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.1-h6fb428d_7.conda
  sha256: a007d6aa37586d3a71ff9503e6ec70baac64fc40241c668a39581399502940ec
  md5: b351c3e11d75aab0b8145de530afbe58
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  size: 902114
  timestamp: 1750925723817
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
  sha256: 7650837344b7850b62fdba02155da0b159cf472b9ab59eb7b472f7bd01dff241
  md5: 6d11a5edae89fe413c0569f16d308f5a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 3896407
  timestamp: 1750808251302
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
  sha256: 916a8c2530992140d23c4d3f63502f250ff36df7298ed9a8b72d3629c347d4ce
  md5: 4e2d5a407e0ecfe493d8b2a65a437bd8
  depends:
  - libgcc 15.1.0 he277a41_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 3833339
  timestamp: 1750808947966
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
  sha256: bbaea1ecf973a7836f92b8ebecc94d3c758414f4de39d2cc6818a3d10cb3216b
  md5: 57541755b5a51691955012b8e197c06c
  depends:
  - libstdcxx 15.1.0 h8f9b012_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29093
  timestamp: 1750808292700
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
  sha256: cb93360dce004fda2f877fd6071c8c0d19ab67b161ff406d5c0d63b7658ad77c
  md5: f981af71cbd4c67c9e6acc7d4cc3f163
  depends:
  - libstdcxx 15.1.0 h3f4de04_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  size: 29078
  timestamp: 1750808974598
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
  sha256: 616277b0c5f7616c2cdf36f6c316ea3f9aa5bb35f2d4476a349ab58b9b91675f
  md5: 000e30b09db0b7c775b21695dff30969
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 35720
  timestamp: 1680113474501
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
  sha256: 5a2c1eeef69342e88a98d1d95bff1603727ab1ff4ee0e421522acd8813439b84
  md5: 08aad7cbe9f5a6b460d0976076b6ae64
  depends:
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 66657
  timestamp: 1727963199518
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
  sha256: ce34669eadaba351cd54910743e6a2261b67009624dbc7daeeafdef93616711b
  md5: 369964e85dc26bfe78f41399b366c435
  depends:
  - __osx >=11.0
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 46438
  timestamp: 1727963202283
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-20.1.7-hdb05f8b_0.conda
  sha256: e7d95b50a90cdc9e0fc38bc37f493a61b9d08164114b562bbd9ff0034f45eca2
  md5: 741e1da0a0798d32e13e3724f2ca2dcf
  depends:
  - __osx >=11.0
  constrains:
  - openmp 20.1.7|20.1.7.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  size: 281996
  timestamp: 1749892286735
- conda: https://conda.modular.com/max-nightly/noarch/max-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 311e01e00ce7302eb97c263c021700c7baa1a6d9be9477f60edcfd17fbf4b49d
  depends:
  - max-core ==25.5.0.dev2025062705 release
  - max-python ==25.5.0.dev2025062705 release
  - mojo-jupyter ==25.5.0.dev2025062705 release
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 9411
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025062705-release.conda
  sha256: be1ef195c01fd7253ad1fb4b4ec647708bd53cbe31a2e97318943fed4062f92c
  depends:
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 222775006
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/linux-aarch64/max-core-25.5.0.dev2025062705-release.conda
  sha256: d7aa1cc232011221751258767cf91a7a6facd799cae6b1503c467b87d0980eda
  depends:
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 224027056
  timestamp: 1751001477655
- conda: https://conda.modular.com/max-nightly/osx-arm64/max-core-25.5.0.dev2025062705-release.conda
  sha256: b0a7c5a1b315f54fb376271a904d103ddba4f113ceeafd4d298bedc8e2e40ea9
  depends:
  - mblack ==25.5.0.dev2025062705 release
  license: LicenseRef-Modular-Proprietary
  size: 187571251
  timestamp: 1751002248746
- conda: https://conda.modular.com/max-nightly/linux-64/max-python-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 9204326c477affb8fd8f9c84de91591a21bd1cf24f0d4390716b3ca642cdb711
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025062705 release
  constrains:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 30541775
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/linux-aarch64/max-python-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 9341747ee3d40a553adf8ec0c9608dd5efefb4dfee6367587ac4e2bdfbd73d10
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025062705 release
  constrains:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 19347501
  timestamp: 1751001477655
- conda: https://conda.modular.com/max-nightly/osx-arm64/max-python-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 08f15fc80c1bdb33d7a35e8b58c04ed454cf80f7b0360f56429ef0b9c3b4e65b
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025062705 release
  constrains:
  - aiohttp >=3.11.12
  - click >=8.0.0
  - gguf >=0.14.0
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - safetensors >=0.5.2
  - pysoundfile >=0.12.1
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - xgrammar >=0.1.18
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - grpcio-tools >=1.68.0
  - grpcio-reflection >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0,<1.31.1
  - opentelemetry-exporter-prometheus >=0.50b0,<1.1.12.0rc1
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus-async >=22.2.0
  - prometheus_client >=0.21.0
  - protobuf >=5.29.1,<=5.30.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - scipy >=1.13.0
  - sentinel >=0.3.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 15993002
  timestamp: 1751002248746
- conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 20b01c20917066a7f186385ae19a017c091e094dbddb4c59f1737bf2eed0a9fe
  depends:
  - python >=3.9,<3.14
  - click >=8.0.0
  - mypy_extensions >=0.4.3
  - packaging >=22.0
  - pathspec >=0.9.0
  - platformdirs >=2
  - typing_extensions >=v4.12.2
  - python
  license: MIT
  size: 131254
  timestamp: 1751001417267
- conda: https://conda.modular.com/max-nightly/noarch/mojo-jupyter-25.5.0.dev2025062705-release.conda
  noarch: python
  sha256: 27842df9aea46c60c8d6487e38c44b18c5aa533e7e84f3d98c5ee7fd978862a4
  depends:
  - max-core ==25.5.0.dev2025062705 release
  - python >=3.9,<3.14
  - jupyter_client >=8.6.2,<8.7
  - python
  license: LicenseRef-Modular-Proprietary
  size: 22485
  timestamp: 1751001417267
- conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
  sha256: 6ed158e4e5dd8f6a10ad9e525631e35cee8557718f83de7a4e3966b1f772c4b1
  md5: e9c622e0d00fa24a6292279af3ab6d06
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11766
  timestamp: 1745776666688
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
  sha256: 91cfb655a68b0353b2833521dc919188db3d8a7f4c64bea2c6a7557b24747468
  md5: 182afabe009dc78d8b73100255ee6868
  depends:
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  size: 926034
  timestamp: 1738196018799
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
  sha256: 2827ada40e8d9ca69a153a45f7fd14f32b2ead7045d3bbb5d10964898fe65733
  md5: 068d497125e4bf8a66bf707254fff5ae
  depends:
  - __osx >=11.0
  license: X11 AND BSD-3-Clause
  size: 797030
  timestamp: 1738196177597
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.0-py313h17eae1a_0.conda
  sha256: 8b88ade24df5229c5d76c5ef09568ae4630b1095982e94648fbbeb18f475aa61
  md5: db18a34466bef0863e9301b518a75e8f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 8545037
  timestamp: 1749430954481
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/numpy-2.3.0-py313hcf1be6b_0.conda
  sha256: 56f93763364ca7c6bc85c18b906d06ed0798edc888f2bcd863736cd39d2cc0ca
  md5: 234faee9472d58370838acee3030b3ef
  depends:
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 7198706
  timestamp: 1749430953292
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.0-py313h41a2e72_0.conda
  sha256: d473005786a27cf4e1430d45a99a61626c2fbf61eb25b4d021cee8d217b973d2
  md5: 0dc3aa075f3e64bdda6e779e2cbf5aa9
  depends:
  - __osx >=11.0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libcxx >=18
  - liblapack >=3.9.0,<4.0a0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 6525213
  timestamp: 1749430964570
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
  sha256: b4491077c494dbf0b5eaa6d87738c22f2154e9277e5293175ec187634bd808a0
  md5: de356753cfdbffcde5bb1e86e3aa6cd0
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 3117410
  timestamp: 1746223723843
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.0-hd08dc88_1.conda
  sha256: 58120daf06a52ba203f94eccb43900213a9f2b3cc310bbaa868505ccd7afbdaa
  md5: ee68fdc3a8723e9c58bdd2f10544658f
  depends:
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 3642633
  timestamp: 1746225726804
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.0-h81ee809_1.conda
  sha256: 73d366c1597a10bcd5f3604b5f0734b31c23225536e03782c6a13f9be9d01bff
  md5: 5c7aef00ef60738a14e0e612cfc5bcde
  depends:
  - __osx >=11.0
  - ca-certificates
  license: Apache-2.0
  license_family: Apache
  size: 3064197
  timestamp: 1746223530698
- conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
  sha256: 289861ed0c13a15d7bbb408796af4de72c2fe67e2bcb0de98f4c3fce259d7991
  md5: 58335b26c38bf4a20f399384c33cbcf9
  depends:
  - python >=3.8
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 62477
  timestamp: 1745345660407
- conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9f64009cdf5b8e529995f18e03665b03f5d07c0b17445b8badef45bde76249ee
  md5: 617f15191456cc6a13db418a275435e5
  depends:
  - python >=3.9
  license: MPL-2.0
  license_family: MOZILLA
  size: 41075
  timestamp: 1733233471940
- conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
  sha256: 0f48999a28019c329cd3f6fd2f01f09fc32cc832f7d6bbe38087ddac858feaa3
  md5: 424844562f5d337077b445ec6b1398a7
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 23531
  timestamp: 1746710438805
- conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
  sha256: a8eb555eef5063bbb7ba06a379fa7ea714f57d9741fe0efdb9442dbbc2cccbcc
  md5: 7da7ccd349dbf6487a7778579d2bb971
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 24246
  timestamp: 1747339794916
- conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
  sha256: 5577623b9f6685ece2697c6eb7511b4c9ac5fb607c9babc2646c811b428fd46a
  md5: 6b6ece66ebcae2d5f326c77ef2c5a066
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  size: 889287
  timestamp: 1750615908735
- conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.4.1-pyhd8ed1ab_0.conda
  sha256: 93e267e4ec35353e81df707938a6527d5eb55c97bf54c3b87229b69523afb59d
  md5: a49c2283f24696a7b30367b7346a0144
  depends:
  - colorama >=0.4
  - exceptiongroup >=1
  - iniconfig >=1
  - packaging >=20
  - pluggy >=1.5,<2
  - pygments >=2.7.2
  - python >=3.9
  - tomli >=1
  constrains:
  - pytest-faulthandler >=2
  license: MIT
  license_family: MIT
  size: 276562
  timestamp: 1750239526127
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
  build_number: 102
  sha256: c2cdcc98ea3cbf78240624e4077e164dc9d5588eefb044b4097c3df54d24d504
  md5: 89e07d92cf50743886f41638d58c4328
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  size: 33273132
  timestamp: 1750064035176
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
  build_number: 102
  sha256: 2eb3ce8b2acf036bd30d4d41cfb45766ad817e26479f18177cfb950c0af6f27b
  md5: ed5b16381ac28233a65c549a59d97b68
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-aarch64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  size: 33764400
  timestamp: 1750062474929
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.13.5-hf3f3da0_102_cp313.conda
  build_number: 102
  sha256: ee1b09fb5563be8509bb9b29b2b436a0af75488b5f1fa6bcd93fe0fba597d13f
  md5: 123b7f04e7b8d6fc206cf2d3466f8a4b
  depends:
  - __osx >=11.0
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  size: 12931515
  timestamp: 1750062475020
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
  sha256: a50052536f1ef8516ed11a844f9413661829aa083304dc624c5925298d078d79
  md5: 5ba79d7c71f03c678c8ead841f347d6e
  depends:
  - python >=3.9
  - six >=1.5
  license: Apache-2.0
  license_family: APACHE
  size: 222505
  timestamp: 1733215763718
- conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
  sha256: ac6cf618100c2e0cad1cabfe2c44bf4a944aa07bb1dc43abff73373351a7d079
  md5: 2eabcede0db21acee23c181db58b4128
  depends:
  - cpython 3.13.5.*
  - python_abi * *_cp313
  license: Python-2.0
  size: 47572
  timestamp: 1750062593102
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
  build_number: 7
  sha256: 0595134584589064f56e67d3de1d8fcbb673a972946bce25fb593fb092fdcd97
  md5: e84b44e6300f1703cb25d29120c5b1d8
  constrains:
  - python 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 6988
  timestamp: 1745258852285
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py313h8e95178_0.conda
  sha256: 6446721c43ba540c02ced4dde564f5a9a0131e40aa406e8af6313084c4a2024f
  md5: c912a00e5cb59357ef55b7930a48cf48
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 384549
  timestamp: 1749898593849
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pyzmq-27.0.0-py313h6e72e74_0.conda
  sha256: dbe4072369da72df5017204457d3dbe1c7a302ac8aca9a4fe52015f4b6ad2c2d
  md5: d901488aaa8c5f0dbdd2d5848796c9ff
  depends:
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 381357
  timestamp: 1749900828011
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyzmq-27.0.0-py313he6960b1_0.conda
  sha256: da722b8ee2785d764182c2d3b9007fb5ef8bc4096f5fc018fd3b3026719b1ee7
  md5: 2cacb246854e185506768b3f7ae23a69
  depends:
  - __osx >=11.0
  - libcxx >=18
  - libsodium >=1.0.20,<1.0.21.0a0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 363932
  timestamp: 1749899287142
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
  sha256: 54bed3a3041befaa9f5acde4a37b1a02f44705b7796689574bcf9d7beaad2959
  md5: c0f08fc2737967edde1a272d4bf41ed9
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 291806
  timestamp: 1740380591358
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
  sha256: 7db04684d3904f6151eff8673270922d31da1eea7fa73254d01c437f49702e34
  md5: 63ef3f6e6d6d5c589e64f11263dc5676
  depends:
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 252359
  timestamp: 1740379663071
- conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
  sha256: 41db0180680cc67c3fa76544ffd48d6a5679d96f4b71d7498a759e94edc9a2db
  md5: a451d576819089b0d672f18768be0f65
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 16385
  timestamp: 1733381032766
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3285204
  timestamp: 1748387766691
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
  sha256: 46e10488e9254092c655257c18fcec0a9864043bdfbe935a9fbf4fb2028b8514
  md5: 2562c9bfd1de3f9c590f0fe53858d85c
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3342845
  timestamp: 1748393219221
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
  sha256: cb86c522576fa95c6db4c878849af0bccfd3264daf0cc40dd18e7f4a7bfced0e
  md5: 7362396c170252e7b7b0c8fb37fe9c78
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3125538
  timestamp: 1748388189063
- conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
  sha256: 18636339a79656962723077df9a56c0ac7b8a864329eb8f847ee3d38495b863e
  md5: ac944244f1fed2eb49bae07193ae8215
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 19167
  timestamp: 1733256819729
- conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py313h536fd9c_0.conda
  sha256: 282c9c3380217119c779fc4c432b0e4e1e42e9a6265bfe36b6f17f6b5d4e6614
  md5: e9434a5155db25c38ade26f71a2f5a48
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 873269
  timestamp: 1748003477089
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tornado-6.5.1-py313h6a51379_0.conda
  sha256: 819497d044a23d6d69fa09aaf7f4d59b6c9db6443d6e32691ccb3361849e3979
  md5: efd003285041d23e604630b18f24b9dd
  depends:
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 876988
  timestamp: 1748005374481
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/tornado-6.5.1-py313h90d716c_0.conda
  sha256: 29c623cfb1f9ea7c1d865cf5f52ae6faa6497ceddbe7841ae27901a21f8cf79f
  md5: 1ab3bef3e9aa0bba9eee2dfbedab1dba
  depends:
  - __osx >=11.0
  - python >=3.13,<3.14.0a0
  - python >=3.13,<3.14.0a0 *_cp313
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 874352
  timestamp: 1748003547444
- conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
  sha256: f39a5620c6e8e9e98357507262a7869de2ae8cc07da8b7f84e517c9fd6c2b959
  md5: 019a7385be9af33791c989871317e1ed
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 110051
  timestamp: 1733367480074
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.0-pyhe01879c_0.conda
  sha256: 8561db52f278c5716b436da6d4ee5521712a49e8f3c70fcae5350f5ebb4be41c
  md5: 2adcd9bb86f656d3d43bf84af59a1faf
  depends:
  - python >=3.9
  - python
  license: PSF-2.0
  license_family: PSF
  size: 50978
  timestamp: 1748959427551
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
  sha256: a4dc72c96848f764bb5a5176aa93dd1e9b9e52804137b99daeebba277b31ea10
  md5: 3947a35e916fcc6b9825449affbf4214
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  size: 335400
  timestamp: 1731585026517
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zeromq-4.3.5-h5efb499_7.conda
  sha256: a6003096dc0570a86492040ba32b04ce7662b159600be2252b7a0dfb9414e21c
  md5: f2f3282559a4b87b7256ecafb4610107
  depends:
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  size: 371419
  timestamp: 1731589490850
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zeromq-4.3.5-hc1bb282_7.conda
  sha256: 9e585569fe2e7d3bea71972cd4b9f06b1a7ab8fa7c5139f92a31cbceecf25a8a
  md5: f7e6b65943cb73bce0143737fded08f1
  depends:
  - __osx >=11.0
  - krb5 >=1.21.3,<1.22.0a0
  - libcxx >=18
  - libsodium >=1.0.20,<1.0.21.0a0
  license: MPL-2.0
  license_family: MOZILLA
  size: 281565
  timestamp: 1731585108039
- conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
  sha256: 7560d21e1b021fd40b65bfb72f67945a3fcb83d78ad7ccf37b8b3165ec3b68ad
  md5: df5e78d904988eb55042c0c97446079f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 22963
  timestamp: 1749421737203
