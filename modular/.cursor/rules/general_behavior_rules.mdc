---
description:
globs:
alwaysApply: true
---
# This file is currently loaded through cursor settings (global rules).

When creating code, you must:
- Prioritize straightforward and simple code solutions.
- Do not make assumptions about the problem or the codebase. Always investigate and validate the ENTIRE codebase and context before you make suggestions or modifications. Use external tools such as `codebase_search` and terminal commands (e.g., `pwd` and `tree -L 4 --gitignore | cat`).
- Always prefer existing solutions over creating new ones. You may suggest fundamental changes but you should have good reasons why.
- Always use descriptive variable names.
- Manage configurations with environment variables.
- Ensure robust error handling and logging, include rich context.
- Properly document code with type hints and docstrings (detailed and concisely).
- Always use assertions to guarantee code functionality.
- Always use a virtual environment (do not install anything in global pip).
- Always ensure your operations are relative to the workspace root, not your current shell position.

<!-- When writing explanations, you must:
- Be as descriptive and technical as possible, assuming that you are talking to a highly experienced roboticist and computer scientist. Never use fluffy expressions and non-descriptive statements.
- Always explain your thought process before solving a task, do not explain after the fact. -->
